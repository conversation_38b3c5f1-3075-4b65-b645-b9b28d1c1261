/**
 * Theme Provider - 主题提供者
 * 
 * 提供主题上下文和主题切换功能
 */

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { Theme } from '@/types';
import { ThemeConfig } from '../types';
import { lightTheme, darkTheme, highContrastTheme, createThemeTransition } from './themes';
import { vscodeThemeSync, VSCodeThemeSync } from './VSCodeThemeSync';
import { monacoThemeAdapter } from './MonacoThemeAdapter';

interface ThemeContextType {
  theme: Theme;
  themeConfig: ThemeConfig;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  syncWithVSCode: boolean;
  setSyncWithVSCode: (sync: boolean) => void;
  isTransitioning: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
  enableVSCodeSync?: boolean;
  enableTransitions?: boolean;
  transitionDuration?: number;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  defaultTheme = 'dark',
  storageKey = 'ai-agent-theme',
  enableVSCodeSync = true,
  enableTransitions = true,
  transitionDuration = 300,
}) => {
  const [theme, setThemeState] = useState<Theme>(defaultTheme);
  const [syncWithVSCode, setSyncWithVSCode] = useState<boolean>(enableVSCodeSync);
  const [isTransitioning, setIsTransitioning] = useState<boolean>(false);
  const [vscodeSync, setVscodeSync] = useState<VSCodeThemeSync | null>(null);

  // 主题配置映射
  const themeConfigs: Record<Theme, ThemeConfig> = {
    light: lightTheme,
    dark: darkTheme,
    'high-contrast': highContrastTheme,
  };

  // 根据主题配置确定主题名称
  const determineThemeFromConfig = useCallback((config: ThemeConfig): Theme => {
    // 简单的主题识别逻辑，可以根据需要扩展
    const backgroundColor = config.colors.background;
    const isLight = isColorLight(backgroundColor);

    if (config.name === 'high-contrast') {
      return 'high-contrast';
    }

    return isLight ? 'light' : 'dark';
  }, []);

  // 判断颜色是否为浅色
  const isColorLight = (color: string): boolean => {
    const rgb = hexToRgb(color);
    if (!rgb) return false;

    const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
    return brightness > 128;
  };

  // 十六进制颜色转RGB
  const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16),
    } : null;
  };

  // 初始化VS Code主题同步
  useEffect(() => {
    if (syncWithVSCode) {
      const sync = new VSCodeThemeSync({
        enableAutoSync: true,
        enableTransitions: enableTransitions,
        transitionDuration: transitionDuration,
      });

      const unsubscribe = sync.onThemeChange((newThemeConfig) => {
        // 根据主题配置确定主题名称
        const themeName = determineThemeFromConfig(newThemeConfig);
        setThemeState(themeName);
      });

      setVscodeSync(sync);

      return () => {
        unsubscribe();
        sync.destroy();
      };
    }
  }, [syncWithVSCode, enableTransitions, transitionDuration]);

  // 从本地存储加载主题
  useEffect(() => {
    if (!syncWithVSCode) {
      try {
        const savedTheme = localStorage.getItem(storageKey) as Theme;
        if (savedTheme && themeConfigs[savedTheme]) {
          setThemeState(savedTheme);
        }
      } catch (error) {
        console.warn('Failed to load theme from localStorage:', error);
      }
    }
  }, [storageKey, syncWithVSCode]);

  // 应用主题到文档
  useEffect(() => {
    const applyTheme = async () => {
      if (enableTransitions) {
        setIsTransitioning(true);
        createThemeTransition(transitionDuration);
      }

      const config = themeConfigs[theme];
      const root = document.documentElement;

      // 设置CSS变量
      Object.entries(config.colors).forEach(([key, value]) => {
        root.style.setProperty(`--color-${key}`, value);
      });

      Object.entries(config.spacing).forEach(([key, value]) => {
        root.style.setProperty(`--spacing-${key}`, value);
      });

      Object.entries(config.typography.fontSize).forEach(([key, value]) => {
        root.style.setProperty(`--font-size-${key}`, value);
      });

      Object.entries(config.typography.fontWeight).forEach(([key, value]) => {
        root.style.setProperty(`--font-weight-${key}`, value.toString());
      });

      Object.entries(config.borderRadius).forEach(([key, value]) => {
        root.style.setProperty(`--border-radius-${key}`, value);
      });

      Object.entries(config.shadows).forEach(([key, value]) => {
        root.style.setProperty(`--shadow-${key}`, value);
      });

      // 设置基础样式
      root.style.setProperty('--font-family', config.typography.fontFamily);
      root.style.setProperty('--theme-name', theme);

      // 添加主题类名
      document.body.className = document.body.className
        .replace(/theme-\w+/g, '')
        .trim();
      document.body.classList.add(`theme-${theme}`);

      // 应用Monaco Editor主题
      if (typeof window !== 'undefined' && (window as any).monaco) {
        const monaco = (window as any).monaco;
        const themeName = `ai-agent-${theme}`;

        try {
          monacoThemeAdapter.registerMonacoTheme(monaco, themeName, config);
          monacoThemeAdapter.applyMonacoTheme(monaco, themeName);
        } catch (error) {
          console.warn('Failed to apply Monaco theme:', error);
        }
      }

      // 结束过渡动画
      if (enableTransitions) {
        setTimeout(() => {
          setIsTransitioning(false);
        }, transitionDuration);
      }
    };

    applyTheme();
  }, [theme, themeConfigs, enableTransitions, transitionDuration]);

  const setTheme = useCallback((newTheme: Theme) => {
    setThemeState(newTheme);

    // 只有在不同步VS Code时才保存到本地存储
    if (!syncWithVSCode) {
      try {
        localStorage.setItem(storageKey, newTheme);
      } catch (error) {
        console.warn('Failed to save theme to localStorage:', error);
      }
    }
  }, [storageKey, syncWithVSCode]);

  const toggleTheme = () => {
    const themes: Theme[] = ['light', 'dark', 'high-contrast'];
    const currentIndex = themes.indexOf(theme);
    const nextIndex = (currentIndex + 1) % themes.length;
    setTheme(themes[nextIndex]);
  };

  const value: ThemeContextType = {
    theme,
    themeConfig: themeConfigs[theme],
    setTheme,
    toggleTheme,
    syncWithVSCode,
    setSyncWithVSCode,
    isTransitioning,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

// 主题切换钩子
export const useThemeToggle = () => {
  const { theme, setTheme, toggleTheme } = useTheme();
  
  return {
    theme,
    setTheme,
    toggleTheme,
    isLight: theme === 'light',
    isDark: theme === 'dark',
    isHighContrast: theme === 'high-contrast',
  };
};

// CSS-in-JS 样式钩子
export const useThemeStyles = () => {
  const { themeConfig } = useTheme();
  
  return {
    colors: themeConfig.colors,
    spacing: themeConfig.spacing,
    typography: themeConfig.typography,
    borderRadius: themeConfig.borderRadius,
    shadows: themeConfig.shadows,
  };
};

// 响应式主题钩子
export const useResponsiveTheme = () => {
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const { themeConfig } = useTheme();

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
      setIsTablet(window.innerWidth >= 768 && window.innerWidth < 1024);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  return {
    isMobile,
    isTablet,
    isDesktop: !isMobile && !isTablet,
    themeConfig,
    spacing: {
      xs: isMobile ? '4px' : themeConfig.spacing.xs,
      sm: isMobile ? '8px' : themeConfig.spacing.sm,
      md: isMobile ? '12px' : themeConfig.spacing.md,
      lg: isMobile ? '16px' : themeConfig.spacing.lg,
      xl: isMobile ? '20px' : themeConfig.spacing.xl,
    },
    fontSize: {
      xs: isMobile ? '12px' : themeConfig.typography.fontSize.xs,
      sm: isMobile ? '14px' : themeConfig.typography.fontSize.sm,
      md: isMobile ? '16px' : themeConfig.typography.fontSize.md,
      lg: isMobile ? '18px' : themeConfig.typography.fontSize.lg,
      xl: isMobile ? '20px' : themeConfig.typography.fontSize.xl,
    },
  };
};
