/* Input Area Styles */
.input-area {
  position: relative;
  background: var(--vscode-panel-background);
  border-top: 1px solid var(--vscode-panel-border);
  padding: 16px;
}

.input-area.dragging {
  background: var(--vscode-inputValidation-infoBackground);
  border-color: var(--vscode-inputValidation-infoBorder);
}

.suggestions-list {
  position: absolute;
  bottom: 100%;
  left: 16px;
  right: 16px;
  background: var(--vscode-editorSuggestWidget-background);
  border: 1px solid var(--vscode-editorSuggestWidget-border);
  border-radius: 6px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.suggestion-item:hover,
.suggestion-item.selected {
  background: var(--vscode-editorSuggestWidget-selectedBackground);
}

.attachments-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 10px;
  background: var(--vscode-badge-background);
  color: var(--vscode-badge-foreground);
  border-radius: 16px;
  font-size: 12px;
}

.attachment-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.remove-attachment {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 2px;
  border-radius: 50%;
}

.remove-attachment:hover {
  background: rgba(255, 255, 255, 0.1);
}

.input-container {
  position: relative;
  display: flex;
  align-items: flex-end;
  gap: 8px;
  background: var(--vscode-input-background);
  border: 1px solid var(--vscode-input-border);
  border-radius: 8px;
  padding: 8px;
}

.message-input {
  flex: 1;
  background: none;
  border: none;
  color: var(--vscode-input-foreground);
  font-family: inherit;
  font-size: inherit;
  resize: none;
  outline: none;
  min-height: 20px;
  max-height: 200px;
}

.message-input::placeholder {
  color: var(--vscode-input-placeholderForeground);
}

.input-actions {
  display: flex;
  gap: 4px;
}

.action-button {
  background: none;
  border: none;
  color: var(--vscode-icon-foreground);
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.action-button:hover {
  background: var(--vscode-toolbar-hoverBackground);
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.send-button {
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border: 1px solid var(--vscode-button-border);
}

.send-button.active {
  background: var(--vscode-button-background);
}

.send-button:hover:not(:disabled) {
  background: var(--vscode-button-hoverBackground);
}

.input-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
}

.character-count .warning {
  color: var(--vscode-errorForeground);
}

.input-hints .hint {
  font-size: 11px;
}

.drag-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 123, 255, 0.1);
  border: 2px dashed var(--vscode-focusBorder);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.drag-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: var(--vscode-focusBorder);
  font-weight: 500;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--vscode-progressBar-background);
  border-top: 2px solid var(--vscode-button-foreground);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
