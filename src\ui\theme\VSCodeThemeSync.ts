/**
 * VS Code主题同步系统
 * 
 * 自动检测和同步VS Code主题，确保UI与编辑器主题保持一致
 * 支持实时主题切换和Monaco Editor主题适配
 */

import { ThemeConfig } from '../types';
import { lightTheme, darkTheme, highContrastTheme, vscodeTheme } from './themes';
import { createThemeTransition } from './themes';

export interface VSCodeThemeInfo {
  kind: 'light' | 'dark' | 'high-contrast';
  name: string;
  colors: Record<string, string>;
  tokenColors: any[];
}

export interface ThemeSyncOptions {
  enableAutoSync?: boolean;
  enableTransitions?: boolean;
  transitionDuration?: number;
  fallbackTheme?: 'light' | 'dark';
  customThemeMapping?: Record<string, ThemeConfig>;
}

export class VSCodeThemeSync {
  private currentTheme: ThemeConfig = darkTheme;
  private observers: MutationObserver[] = [];
  private listeners: ((theme: ThemeConfig) => void)[] = [];
  private options: Required<ThemeSyncOptions>;
  private vscodeAPI: any = null;

  constructor(options: ThemeSyncOptions = {}) {
    this.options = {
      enableAutoSync: true,
      enableTransitions: true,
      transitionDuration: 300,
      fallbackTheme: 'dark',
      customThemeMapping: {},
      ...options,
    };

    this.initializeVSCodeAPI();
    this.setupThemeDetection();
  }

  /**
   * 初始化VS Code API
   */
  private initializeVSCodeAPI(): void {
    try {
      // 在webview环境中获取vscode API
      if (typeof window !== 'undefined' && (window as any).acquireVsCodeApi) {
        this.vscodeAPI = (window as any).acquireVsCodeApi();
      }
    } catch (error) {
      console.warn('Failed to acquire VS Code API:', error);
    }
  }

  /**
   * 设置主题检测
   */
  private setupThemeDetection(): void {
    if (!this.options.enableAutoSync) return;

    // 检测body类名变化
    this.setupBodyClassObserver();
    
    // 检测CSS变量变化
    this.setupCSSVariableObserver();
    
    // 监听VS Code主题变化消息
    this.setupMessageListener();
    
    // 初始主题检测
    this.detectCurrentTheme();
  }

  /**
   * 设置body类名观察器
   */
  private setupBodyClassObserver(): void {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          this.detectCurrentTheme();
        }
      });
    });

    observer.observe(document.body, {
      attributes: true,
      attributeFilter: ['class'],
    });

    this.observers.push(observer);
  }

  /**
   * 设置CSS变量观察器
   */
  private setupCSSVariableObserver(): void {
    // 监听VS Code CSS变量变化
    const checkCSSVariables = () => {
      const computedStyle = getComputedStyle(document.documentElement);
      const vscodeBackground = computedStyle.getPropertyValue('--vscode-editor-background');
      const vscodeForeground = computedStyle.getPropertyValue('--vscode-editor-foreground');
      
      if (vscodeBackground && vscodeForeground) {
        this.syncFromVSCodeColors({
          background: vscodeBackground.trim(),
          foreground: vscodeForeground.trim(),
        });
      }
    };

    // 定期检查CSS变量变化
    setInterval(checkCSSVariables, 1000);
    
    // 立即检查一次
    checkCSSVariables();
  }

  /**
   * 设置消息监听器
   */
  private setupMessageListener(): void {
    if (!this.vscodeAPI) return;

    window.addEventListener('message', (event) => {
      const message = event.data;
      
      if (message.type === 'themeChanged') {
        this.handleThemeChange(message.theme);
      }
    });
  }

  /**
   * 检测当前主题
   */
  private detectCurrentTheme(): void {
    const body = document.body;
    const classList = body.classList;
    
    let detectedTheme: ThemeConfig;
    
    if (classList.contains('vscode-high-contrast') || 
        classList.contains('vscode-high-contrast-light')) {
      detectedTheme = highContrastTheme;
    } else if (classList.contains('vscode-light')) {
      detectedTheme = this.createLightThemeFromVSCode();
    } else if (classList.contains('vscode-dark')) {
      detectedTheme = this.createDarkThemeFromVSCode();
    } else {
      // 降级检测
      const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      detectedTheme = isDark ? darkTheme : lightTheme;
    }
    
    this.applyTheme(detectedTheme);
  }

  /**
   * 从VS Code颜色创建浅色主题
   */
  private createLightThemeFromVSCode(): ThemeConfig {
    const computedStyle = getComputedStyle(document.documentElement);
    
    return {
      ...lightTheme,
      colors: {
        ...lightTheme.colors,
        background: this.getVSCodeColor('--vscode-editor-background', lightTheme.colors.background),
        surface: this.getVSCodeColor('--vscode-sideBar-background', lightTheme.colors.surface),
        text: this.getVSCodeColor('--vscode-editor-foreground', lightTheme.colors.text),
        textSecondary: this.getVSCodeColor('--vscode-descriptionForeground', lightTheme.colors.textSecondary),
        border: this.getVSCodeColor('--vscode-panel-border', lightTheme.colors.border),
        primary: this.getVSCodeColor('--vscode-button-background', lightTheme.colors.primary),
        accent: this.getVSCodeColor('--vscode-focusBorder', lightTheme.colors.accent),
        success: this.getVSCodeColor('--vscode-terminal-ansiGreen', lightTheme.colors.success),
        warning: this.getVSCodeColor('--vscode-terminal-ansiYellow', lightTheme.colors.warning),
        error: this.getVSCodeColor('--vscode-terminal-ansiRed', lightTheme.colors.error),
        info: this.getVSCodeColor('--vscode-terminal-ansiBlue', lightTheme.colors.info),
      },
    };
  }

  /**
   * 从VS Code颜色创建深色主题
   */
  private createDarkThemeFromVSCode(): ThemeConfig {
    return {
      ...darkTheme,
      colors: {
        ...darkTheme.colors,
        background: this.getVSCodeColor('--vscode-editor-background', darkTheme.colors.background),
        surface: this.getVSCodeColor('--vscode-sideBar-background', darkTheme.colors.surface),
        text: this.getVSCodeColor('--vscode-editor-foreground', darkTheme.colors.text),
        textSecondary: this.getVSCodeColor('--vscode-descriptionForeground', darkTheme.colors.textSecondary),
        border: this.getVSCodeColor('--vscode-panel-border', darkTheme.colors.border),
        primary: this.getVSCodeColor('--vscode-button-background', darkTheme.colors.primary),
        accent: this.getVSCodeColor('--vscode-focusBorder', darkTheme.colors.accent),
        success: this.getVSCodeColor('--vscode-terminal-ansiGreen', darkTheme.colors.success),
        warning: this.getVSCodeColor('--vscode-terminal-ansiYellow', darkTheme.colors.warning),
        error: this.getVSCodeColor('--vscode-terminal-ansiRed', darkTheme.colors.error),
        info: this.getVSCodeColor('--vscode-terminal-ansiBlue', darkTheme.colors.info),
      },
    };
  }

  /**
   * 获取VS Code CSS变量值
   */
  private getVSCodeColor(variable: string, fallback: string): string {
    const value = getComputedStyle(document.documentElement)
      .getPropertyValue(variable)
      .trim();
    
    return value || fallback;
  }

  /**
   * 从VS Code颜色同步主题
   */
  private syncFromVSCodeColors(colors: { background: string; foreground: string }): void {
    // 根据背景色判断是深色还是浅色主题
    const isDark = this.isColorDark(colors.background);
    const baseTheme = isDark ? darkTheme : lightTheme;
    
    const syncedTheme: ThemeConfig = {
      ...baseTheme,
      colors: {
        ...baseTheme.colors,
        background: colors.background,
        text: colors.foreground,
      },
    };
    
    this.applyTheme(syncedTheme);
  }

  /**
   * 判断颜色是否为深色
   */
  private isColorDark(color: string): boolean {
    // 简单的颜色亮度检测
    const rgb = this.hexToRgb(color);
    if (!rgb) return true;
    
    const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
    return brightness < 128;
  }

  /**
   * 十六进制颜色转RGB
   */
  private hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16),
    } : null;
  }

  /**
   * 处理主题变化
   */
  private handleThemeChange(themeInfo: VSCodeThemeInfo): void {
    let newTheme: ThemeConfig;
    
    // 检查自定义主题映射
    if (this.options.customThemeMapping[themeInfo.name]) {
      newTheme = this.options.customThemeMapping[themeInfo.name];
    } else {
      // 根据主题类型选择
      switch (themeInfo.kind) {
        case 'light':
          newTheme = this.createLightThemeFromVSCode();
          break;
        case 'dark':
          newTheme = this.createDarkThemeFromVSCode();
          break;
        case 'high-contrast':
          newTheme = highContrastTheme;
          break;
        default:
          newTheme = this.options.fallbackTheme === 'light' ? lightTheme : darkTheme;
      }
    }
    
    this.applyTheme(newTheme);
  }

  /**
   * 应用主题
   */
  private applyTheme(theme: ThemeConfig): void {
    if (this.currentTheme === theme) return;
    
    this.currentTheme = theme;
    
    // 应用过渡动画
    if (this.options.enableTransitions) {
      createThemeTransition(this.options.transitionDuration);
    }
    
    // 通知监听器
    this.listeners.forEach(listener => listener(theme));
  }

  /**
   * 添加主题变化监听器
   */
  public onThemeChange(listener: (theme: ThemeConfig) => void): () => void {
    this.listeners.push(listener);
    
    // 返回取消监听的函数
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * 获取当前主题
   */
  public getCurrentTheme(): ThemeConfig {
    return this.currentTheme;
  }

  /**
   * 手动设置主题
   */
  public setTheme(theme: ThemeConfig): void {
    this.applyTheme(theme);
  }

  /**
   * 销毁同步器
   */
  public destroy(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.listeners = [];
  }
}

// 全局主题同步器实例
export const vscodeThemeSync = new VSCodeThemeSync();
