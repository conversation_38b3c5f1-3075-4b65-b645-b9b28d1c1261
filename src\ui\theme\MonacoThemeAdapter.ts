/**
 * Monaco Editor主题适配器
 * 
 * 将UI主题配置转换为Monaco Editor主题，确保代码编辑器与整体界面主题一致
 */

import { ThemeConfig } from '../types';

export interface MonacoThemeData {
  base: 'vs' | 'vs-dark' | 'hc-black';
  inherit: boolean;
  rules: MonacoTokenRule[];
  colors: Record<string, string>;
}

export interface MonacoTokenRule {
  token: string;
  foreground?: string;
  background?: string;
  fontStyle?: string;
}

export class MonacoThemeAdapter {
  private static instance: MonacoThemeAdapter;
  private registeredThemes: Map<string, MonacoThemeData> = new Map();

  public static getInstance(): MonacoThemeAdapter {
    if (!MonacoThemeAdapter.instance) {
      MonacoThemeAdapter.instance = new MonacoThemeAdapter();
    }
    return MonacoThemeAdapter.instance;
  }

  /**
   * 将UI主题转换为Monaco主题
   */
  public convertToMonacoTheme(themeConfig: ThemeConfig, themeName: string): MonacoThemeData {
    const isDark = this.isDarkTheme(themeConfig);
    const isHighContrast = themeName.includes('high-contrast');
    
    const base: 'vs' | 'vs-dark' | 'hc-black' = isHighContrast 
      ? 'hc-black' 
      : isDark 
        ? 'vs-dark' 
        : 'vs';

    const monacoTheme: MonacoThemeData = {
      base,
      inherit: true,
      rules: this.generateTokenRules(themeConfig, isDark),
      colors: this.generateEditorColors(themeConfig),
    };

    this.registeredThemes.set(themeName, monacoTheme);
    return monacoTheme;
  }

  /**
   * 判断是否为深色主题
   */
  private isDarkTheme(themeConfig: ThemeConfig): boolean {
    const backgroundColor = themeConfig.colors.background;
    return this.isColorDark(backgroundColor);
  }

  /**
   * 判断颜色是否为深色
   */
  private isColorDark(color: string): boolean {
    const rgb = this.hexToRgb(color);
    if (!rgb) return true;
    
    const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
    return brightness < 128;
  }

  /**
   * 十六进制颜色转RGB
   */
  private hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16),
    } : null;
  }

  /**
   * 生成代码高亮规则
   */
  private generateTokenRules(themeConfig: ThemeConfig, isDark: boolean): MonacoTokenRule[] {
    const { colors } = themeConfig;
    
    return [
      // 注释
      {
        token: 'comment',
        foreground: colors.textSecondary,
        fontStyle: 'italic',
      },
      
      // 关键字
      {
        token: 'keyword',
        foreground: colors.primary,
        fontStyle: 'bold',
      },
      
      // 字符串
      {
        token: 'string',
        foreground: colors.success,
      },
      
      // 数字
      {
        token: 'number',
        foreground: colors.warning,
      },
      
      // 类型
      {
        token: 'type',
        foreground: colors.info,
      },
      
      // 函数
      {
        token: 'function',
        foreground: colors.accent,
      },
      
      // 变量
      {
        token: 'variable',
        foreground: colors.text,
      },
      
      // 操作符
      {
        token: 'operator',
        foreground: colors.text,
      },
      
      // 分隔符
      {
        token: 'delimiter',
        foreground: colors.textSecondary,
      },
      
      // 标识符
      {
        token: 'identifier',
        foreground: colors.text,
      },
      
      // 错误
      {
        token: 'invalid',
        foreground: colors.error,
        fontStyle: 'bold',
      },
      
      // TypeScript/JavaScript特定
      {
        token: 'keyword.typescript',
        foreground: colors.primary,
      },
      {
        token: 'keyword.javascript',
        foreground: colors.primary,
      },
      
      // Python特定
      {
        token: 'keyword.python',
        foreground: colors.primary,
      },
      
      // JSON特定
      {
        token: 'key.json',
        foreground: colors.info,
      },
      {
        token: 'string.value.json',
        foreground: colors.success,
      },
      
      // Markdown特定
      {
        token: 'emphasis.markdown',
        fontStyle: 'italic',
      },
      {
        token: 'strong.markdown',
        fontStyle: 'bold',
      },
      {
        token: 'header.markdown',
        foreground: colors.primary,
        fontStyle: 'bold',
      },
    ];
  }

  /**
   * 生成编辑器颜色配置
   */
  private generateEditorColors(themeConfig: ThemeConfig): Record<string, string> {
    const { colors } = themeConfig;
    
    return {
      // 编辑器背景
      'editor.background': colors.background,
      'editor.foreground': colors.text,
      
      // 行号
      'editorLineNumber.foreground': colors.textSecondary,
      'editorLineNumber.activeForeground': colors.text,
      
      // 光标
      'editorCursor.foreground': colors.primary,
      
      // 选择
      'editor.selectionBackground': this.addAlpha(colors.primary, 0.3),
      'editor.selectionHighlightBackground': this.addAlpha(colors.primary, 0.2),
      
      // 当前行高亮
      'editor.lineHighlightBackground': this.addAlpha(colors.surface, 0.5),
      
      // 查找匹配
      'editor.findMatchBackground': this.addAlpha(colors.warning, 0.4),
      'editor.findMatchHighlightBackground': this.addAlpha(colors.warning, 0.2),
      
      // 括号匹配
      'editorBracketMatch.background': this.addAlpha(colors.accent, 0.3),
      'editorBracketMatch.border': colors.accent,
      
      // 缩进参考线
      'editorIndentGuide.background': this.addAlpha(colors.border, 0.5),
      'editorIndentGuide.activeBackground': colors.border,
      
      // 滚动条
      'scrollbar.shadow': this.addAlpha(colors.text, 0.1),
      'scrollbarSlider.background': this.addAlpha(colors.textSecondary, 0.3),
      'scrollbarSlider.hoverBackground': this.addAlpha(colors.textSecondary, 0.5),
      'scrollbarSlider.activeBackground': this.addAlpha(colors.textSecondary, 0.7),
      
      // 建议窗口
      'editorSuggestWidget.background': colors.surface,
      'editorSuggestWidget.border': colors.border,
      'editorSuggestWidget.foreground': colors.text,
      'editorSuggestWidget.selectedBackground': this.addAlpha(colors.primary, 0.2),
      
      // 悬停窗口
      'editorHoverWidget.background': colors.surface,
      'editorHoverWidget.border': colors.border,
      
      // 错误和警告
      'editorError.foreground': colors.error,
      'editorWarning.foreground': colors.warning,
      'editorInfo.foreground': colors.info,
      
      // 装订线
      'editorGutter.background': colors.background,
      'editorGutter.modifiedBackground': colors.warning,
      'editorGutter.addedBackground': colors.success,
      'editorGutter.deletedBackground': colors.error,
      
      // 面板
      'panel.background': colors.surface,
      'panel.border': colors.border,
      'panelTitle.activeForeground': colors.text,
      'panelTitle.inactiveForeground': colors.textSecondary,
      
      // 侧边栏
      'sideBar.background': colors.surface,
      'sideBar.foreground': colors.text,
      'sideBar.border': colors.border,
      
      // 活动栏
      'activityBar.background': colors.surface,
      'activityBar.foreground': colors.text,
      'activityBar.border': colors.border,
      
      // 状态栏
      'statusBar.background': colors.surface,
      'statusBar.foreground': colors.text,
      'statusBar.border': colors.border,
      
      // 标题栏
      'titleBar.activeBackground': colors.surface,
      'titleBar.activeForeground': colors.text,
      'titleBar.inactiveBackground': colors.background,
      'titleBar.inactiveForeground': colors.textSecondary,
      
      // 按钮
      'button.background': colors.primary,
      'button.foreground': '#ffffff',
      'button.hoverBackground': this.lightenColor(colors.primary, 0.1),
      
      // 输入框
      'input.background': colors.background,
      'input.foreground': colors.text,
      'input.border': colors.border,
      'input.placeholderForeground': colors.textSecondary,
      
      // 下拉框
      'dropdown.background': colors.surface,
      'dropdown.foreground': colors.text,
      'dropdown.border': colors.border,
      
      // 列表
      'list.activeSelectionBackground': this.addAlpha(colors.primary, 0.3),
      'list.activeSelectionForeground': colors.text,
      'list.hoverBackground': this.addAlpha(colors.primary, 0.1),
      'list.focusBackground': this.addAlpha(colors.primary, 0.2),
      
      // 树
      'tree.indentGuidesStroke': colors.border,
    };
  }

  /**
   * 为颜色添加透明度
   */
  private addAlpha(color: string, alpha: number): string {
    const rgb = this.hexToRgb(color);
    if (!rgb) return color;
    
    return `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${alpha})`;
  }

  /**
   * 调亮颜色
   */
  private lightenColor(color: string, amount: number): string {
    const rgb = this.hexToRgb(color);
    if (!rgb) return color;
    
    const r = Math.min(255, Math.round(rgb.r + (255 - rgb.r) * amount));
    const g = Math.min(255, Math.round(rgb.g + (255 - rgb.g) * amount));
    const b = Math.min(255, Math.round(rgb.b + (255 - rgb.b) * amount));
    
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  }

  /**
   * 注册Monaco主题
   */
  public registerMonacoTheme(monaco: any, themeName: string, themeConfig: ThemeConfig): void {
    const monacoTheme = this.convertToMonacoTheme(themeConfig, themeName);
    
    try {
      monaco.editor.defineTheme(themeName, monacoTheme);
    } catch (error) {
      console.warn(`Failed to register Monaco theme ${themeName}:`, error);
    }
  }

  /**
   * 应用Monaco主题
   */
  public applyMonacoTheme(monaco: any, themeName: string): void {
    try {
      monaco.editor.setTheme(themeName);
    } catch (error) {
      console.warn(`Failed to apply Monaco theme ${themeName}:`, error);
    }
  }

  /**
   * 获取已注册的主题
   */
  public getRegisteredTheme(themeName: string): MonacoThemeData | undefined {
    return this.registeredThemes.get(themeName);
  }

  /**
   * 清除所有注册的主题
   */
  public clearRegisteredThemes(): void {
    this.registeredThemes.clear();
  }
}

// 导出单例实例
export const monacoThemeAdapter = MonacoThemeAdapter.getInstance();
