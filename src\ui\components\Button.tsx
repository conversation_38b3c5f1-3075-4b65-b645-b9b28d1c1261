/**
 * Button Component - 按钮组件
 * 
 * 可重用的按钮组件，支持多种样式和状态
 */

import React from 'react';
import { ButtonProps } from '../types';
import { useThemeStyles } from '../theme/ThemeProvider';
import { useAccessibility } from '../accessibility/useAccessibility';

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  icon,
  children,
  onClick,
  ...props
}) => {
  const { colors, spacing, typography, borderRadius } = useThemeStyles();

  // 可访问性增强
  const { elementRef, state, announce } = useAccessibility({
    enableFocusManagement: true,
    enableAnnouncements: true,
    role: 'button',
    ariaLabel: props['aria-label'] || (typeof children === 'string' ? children : undefined),
    tabIndex: disabled ? -1 : 0,
  });

  const getVariantStyles = () => {
    switch (variant) {
      case 'primary':
        return {
          backgroundColor: disabled ? colors.secondary : colors.primary,
          color: colors.background,
          border: 'none',
          '&:hover': !disabled && !loading ? {
            backgroundColor: colors.accent,
          } : {},
        };
      case 'secondary':
        return {
          backgroundColor: colors.surface,
          color: colors.text,
          border: `1px solid ${colors.border}`,
          '&:hover': !disabled && !loading ? {
            backgroundColor: colors.border,
          } : {},
        };
      case 'outline':
        return {
          backgroundColor: 'transparent',
          color: colors.primary,
          border: `1px solid ${colors.primary}`,
          '&:hover': !disabled && !loading ? {
            backgroundColor: colors.primary,
            color: colors.background,
          } : {},
        };
      case 'ghost':
        return {
          backgroundColor: 'transparent',
          color: colors.text,
          border: 'none',
          '&:hover': !disabled && !loading ? {
            backgroundColor: colors.surface,
          } : {},
        };
      case 'danger':
        return {
          backgroundColor: disabled ? colors.secondary : colors.error,
          color: colors.background,
          border: 'none',
          '&:hover': !disabled && !loading ? {
            backgroundColor: colors.error,
            filter: 'brightness(0.9)',
          } : {},
        };
      default:
        return {};
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return {
          padding: `${spacing.xs} ${spacing.sm}`,
          fontSize: typography.fontSize.sm,
          minHeight: '32px',
        };
      case 'md':
        return {
          padding: `${spacing.sm} ${spacing.md}`,
          fontSize: typography.fontSize.md,
          minHeight: '40px',
        };
      case 'lg':
        return {
          padding: `${spacing.md} ${spacing.lg}`,
          fontSize: typography.fontSize.lg,
          minHeight: '48px',
        };
      default:
        return {};
    }
  };

  const baseStyles: React.CSSProperties = {
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing.xs,
    fontFamily: typography.fontFamily,
    fontWeight: typography.fontWeight.medium,
    borderRadius: borderRadius.md,
    cursor: disabled || loading ? 'not-allowed' : 'pointer',
    transition: 'all 0.2s ease-in-out',
    outline: 'none',
    textDecoration: 'none',
    userSelect: 'none',
    opacity: disabled ? 0.6 : 1,
    position: 'relative',
    overflow: 'hidden',
    ...getSizeStyles(),
    ...getVariantStyles(),
  };

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled || loading) {
      e.preventDefault();
      announce('按钮已禁用', 'assertive');
      return;
    }

    // 播报按钮点击
    if (children && typeof children === 'string') {
      announce(`${children}按钮已点击`);
    }

    onClick?.(e);
  };

  // 键盘事件处理
  const handleKeyDown = (e: React.KeyboardEvent<HTMLButtonElement>) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleClick(e as any);
    }
  };

  const LoadingSpinner = () => (
    <div
      style={{
        width: '16px',
        height: '16px',
        border: '2px solid transparent',
        borderTop: '2px solid currentColor',
        borderRadius: '50%',
        animation: 'spin 1s linear infinite',
      }}
    />
  );

  return (
    <>
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
          
          .button-ripple {
            position: absolute;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
          }
          
          @keyframes ripple {
            to {
              transform: scale(4);
              opacity: 0;
            }
          }
        `}
      </style>
      <button
        ref={elementRef}
        style={{
          ...baseStyles,
          // 可访问性焦点样式
          ...(state.isFocused && state.isKeyboardUser && {
            outline: `2px solid ${colors.primary}`,
            outlineOffset: '2px',
          }),
          // 高对比度模式适配
          ...(state.hasHighContrast && {
            border: `2px solid ${colors.text}`,
            backgroundColor: variant === 'primary' ? colors.text : 'transparent',
            color: variant === 'primary' ? colors.background : colors.text,
          }),
        }}
        disabled={disabled || loading}
        onClick={handleClick}
        onKeyDown={handleKeyDown}
        aria-disabled={disabled || loading}
        aria-busy={loading}
        aria-label={props['aria-label'] || (typeof children === 'string' ? children : undefined)}
        {...props}
      >
        {loading && (
          <>
            <LoadingSpinner />
            <span className="sr-only">加载中...</span>
          </>
        )}
        {!loading && icon && <span aria-hidden="true">{icon}</span>}
        {children && <span>{children}</span>}
      </button>
    </>
  );
};

// 按钮组组件
interface ButtonGroupProps {
  children: React.ReactNode;
  orientation?: 'horizontal' | 'vertical';
  spacing?: string;
}

export const ButtonGroup: React.FC<ButtonGroupProps> = ({
  children,
  orientation = 'horizontal',
  spacing = '8px',
}) => {
  const styles: React.CSSProperties = {
    display: 'flex',
    flexDirection: orientation === 'vertical' ? 'column' : 'row',
    gap: spacing,
    alignItems: orientation === 'horizontal' ? 'center' : 'stretch',
  };

  return <div style={styles}>{children}</div>;
};

// 图标按钮组件
interface IconButtonProps extends Omit<ButtonProps, 'children'> {
  icon: React.ReactNode;
  'aria-label': string;
}

export const IconButton: React.FC<IconButtonProps> = ({
  icon,
  size = 'md',
  ...props
}) => {
  const sizeMap = {
    sm: '32px',
    md: '40px',
    lg: '48px',
  };

  return (
    <Button
      {...props}
      size={size}
      style={{
        width: sizeMap[size],
        height: sizeMap[size],
        padding: 0,
        minHeight: 'auto',
        ...props.style,
      }}
    >
      {icon}
    </Button>
  );
};

// 浮动操作按钮
interface FABProps extends Omit<ButtonProps, 'variant' | 'size'> {
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  offset?: string;
}

export const FloatingActionButton: React.FC<FABProps> = ({
  position = 'bottom-right',
  offset = '24px',
  icon,
  children,
  ...props
}) => {
  const { colors, shadows } = useThemeStyles();

  const getPositionStyles = () => {
    const base = {
      position: 'fixed' as const,
      zIndex: 1000,
    };

    switch (position) {
      case 'bottom-right':
        return { ...base, bottom: offset, right: offset };
      case 'bottom-left':
        return { ...base, bottom: offset, left: offset };
      case 'top-right':
        return { ...base, top: offset, right: offset };
      case 'top-left':
        return { ...base, top: offset, left: offset };
      default:
        return base;
    }
  };

  return (
    <Button
      {...props}
      variant="primary"
      style={{
        ...getPositionStyles(),
        width: '56px',
        height: '56px',
        borderRadius: '50%',
        boxShadow: shadows.lg,
        padding: 0,
        minHeight: 'auto',
        ...props.style,
      }}
    >
      {icon}
      {children}
    </Button>
  );
};

// 切换按钮
interface ToggleButtonProps extends Omit<ButtonProps, 'variant'> {
  active: boolean;
  onToggle: (active: boolean) => void;
}

export const ToggleButton: React.FC<ToggleButtonProps> = ({
  active,
  onToggle,
  children,
  ...props
}) => {
  const handleClick = () => {
    onToggle(!active);
  };

  return (
    <Button
      {...props}
      variant={active ? 'primary' : 'outline'}
      onClick={handleClick}
    >
      {children}
    </Button>
  );
};

// 链接按钮
interface LinkButtonProps extends Omit<ButtonProps, 'onClick'> {
  href: string;
  target?: string;
  rel?: string;
}

export const LinkButton: React.FC<LinkButtonProps> = ({
  href,
  target,
  rel,
  children,
  ...props
}) => {
  return (
    <a
      href={href}
      target={target}
      rel={rel}
      style={{ textDecoration: 'none' }}
    >
      <Button {...props}>{children}</Button>
    </a>
  );
};
