/**
 * ChatInput Component - 聊天输入组件
 * 
 * 提供消息输入功能，支持多行输入、快捷键等
 */

import React, { useState, useRef, useEffect } from 'react';
import { ChatInputProps } from '../types';
import { useThemeStyles } from '../theme/ThemeProvider';
import { Button, IconButton } from './Button';

export const ChatInput: React.FC<ChatInputProps> = ({
  value,
  onChange,
  onSubmit,
  placeholder = "Type your message...",
  disabled = false,
  isLoading = false,
  maxLength = 4000,
  multiline = true,
}) => {
  const { colors, spacing, typography, borderRadius } = useThemeStyles();
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [isFocused, setIsFocused] = useState(false);

  // 自动调整高度
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea && multiline) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`;
    }
  }, [value, multiline]);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter') {
      if (e.shiftKey) {
        // Shift+Enter: 换行
        return;
      } else {
        // Enter: 发送消息
        e.preventDefault();
        handleSubmit();
      }
    }

    if (e.key === 'Escape') {
      // Escape: 清空输入
      onChange('');
      textareaRef.current?.blur();
    }
  };

  const handleSubmit = () => {
    if (value.trim() && !disabled && !isLoading) {
      onSubmit(value.trim());
      onChange('');
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    const pastedText = e.clipboardData.getData('text');
    if (value.length + pastedText.length > maxLength) {
      e.preventDefault();
      const remainingLength = maxLength - value.length;
      const truncatedText = pastedText.slice(0, remainingLength);
      onChange(value + truncatedText);
    }
  };

  const containerStyles: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    gap: spacing.sm,
    padding: spacing.md,
    backgroundColor: colors.surface,
    borderTop: `1px solid ${colors.border}`,
  };

  const inputContainerStyles: React.CSSProperties = {
    display: 'flex',
    gap: spacing.sm,
    alignItems: 'flex-end',
    position: 'relative',
  };

  const textareaStyles: React.CSSProperties = {
    flex: 1,
    minHeight: multiline ? '40px' : '40px',
    maxHeight: multiline ? '200px' : '40px',
    padding: `${spacing.sm} ${spacing.md}`,
    border: `2px solid ${isFocused ? colors.primary : colors.border}`,
    borderRadius: borderRadius.lg,
    backgroundColor: colors.background,
    color: colors.text,
    fontFamily: typography.fontFamily,
    fontSize: typography.fontSize.md,
    lineHeight: '1.5',
    resize: 'none',
    outline: 'none',
    transition: 'border-color 0.2s ease',
    overflow: 'hidden',
  };

  const counterStyles: React.CSSProperties = {
    fontSize: typography.fontSize.xs,
    color: value.length > maxLength * 0.9 ? colors.warning : colors.textSecondary,
    textAlign: 'right',
    marginTop: spacing.xs,
  };

  const shortcutHintStyles: React.CSSProperties = {
    fontSize: typography.fontSize.xs,
    color: colors.textSecondary,
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  };

  const actionButtonsStyles: React.CSSProperties = {
    display: 'flex',
    gap: spacing.xs,
    alignItems: 'center',
  };

  return (
    <div style={containerStyles}>
      <div style={inputContainerStyles}>
        <textarea
          ref={textareaRef}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          onPaste={handlePaste}
          placeholder={placeholder}
          disabled={disabled}
          maxLength={maxLength}
          style={textareaStyles}
          rows={1}
        />
        
        <div style={actionButtonsStyles}>
          {value.trim() && (
            <IconButton
              icon="🗑️"
              size="sm"
              variant="ghost"
              aria-label="Clear"
              onClick={() => onChange('')}
              disabled={disabled}
            />
          )}
          
          <Button
            variant="primary"
            size="sm"
            disabled={!value.trim() || disabled || isLoading}
            loading={isLoading}
            onClick={handleSubmit}
            icon={isLoading ? undefined : "➤"}
          >
            {isLoading ? 'Sending...' : 'Send'}
          </Button>
        </div>
      </div>
      
      <div style={shortcutHintStyles}>
        <div>
          <span>Press Enter to send, Shift+Enter for new line</span>
        </div>
        <div style={counterStyles}>
          {value.length}/{maxLength}
        </div>
      </div>
    </div>
  );
};

// 快速回复组件
interface QuickReplyProps {
  suggestions: string[];
  onSelect: (suggestion: string) => void;
  disabled?: boolean;
}

export const QuickReply: React.FC<QuickReplyProps> = ({
  suggestions,
  onSelect,
  disabled = false,
}) => {
  const { colors, spacing, borderRadius } = useThemeStyles();

  if (suggestions.length === 0) return null;

  const containerStyles: React.CSSProperties = {
    display: 'flex',
    flexWrap: 'wrap',
    gap: spacing.xs,
    padding: `0 ${spacing.md} ${spacing.sm}`,
  };

  const suggestionStyles: React.CSSProperties = {
    padding: `${spacing.xs} ${spacing.sm}`,
    backgroundColor: colors.surface,
    border: `1px solid ${colors.border}`,
    borderRadius: borderRadius.md,
    cursor: disabled ? 'not-allowed' : 'pointer',
    fontSize: '14px',
    color: colors.text,
    transition: 'all 0.2s ease',
    opacity: disabled ? 0.6 : 1,
  };

  return (
    <div style={containerStyles}>
      {suggestions.map((suggestion, index) => (
        <div
          key={index}
          style={suggestionStyles}
          onClick={() => !disabled && onSelect(suggestion)}
          onMouseEnter={(e) => {
            if (!disabled) {
              e.currentTarget.style.backgroundColor = colors.border;
            }
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = colors.surface;
          }}
        >
          {suggestion}
        </div>
      ))}
    </div>
  );
};

// 文件上传组件
interface FileUploadProps {
  onFileSelect: (files: File[]) => void;
  accept?: string;
  multiple?: boolean;
  disabled?: boolean;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  onFileSelect,
  accept = "*/*",
  multiple = false,
  disabled = false,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const { colors, spacing, borderRadius } = useThemeStyles();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      onFileSelect(files);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (disabled) return;

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      onFileSelect(files);
    }
  };

  const dropZoneStyles: React.CSSProperties = {
    border: `2px dashed ${isDragOver ? colors.primary : colors.border}`,
    borderRadius: borderRadius.lg,
    padding: spacing.lg,
    textAlign: 'center',
    backgroundColor: isDragOver ? `${colors.primary}10` : 'transparent',
    cursor: disabled ? 'not-allowed' : 'pointer',
    transition: 'all 0.2s ease',
    opacity: disabled ? 0.6 : 1,
  };

  return (
    <div
      style={dropZoneStyles}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onClick={() => !disabled && fileInputRef.current?.click()}
    >
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={handleFileChange}
        style={{ display: 'none' }}
        disabled={disabled}
      />
      
      <div style={{ color: colors.textSecondary }}>
        📎 Click or drag files here to upload
      </div>
    </div>
  );
};

// 语音输入组件（占位符）
interface VoiceInputProps {
  onTranscript: (text: string) => void;
  disabled?: boolean;
}

export const VoiceInput: React.FC<VoiceInputProps> = ({
  onTranscript,
  disabled = false,
}) => {
  const [isRecording, setIsRecording] = useState(false);

  const handleToggleRecording = () => {
    if (disabled) return;

    if (isRecording) {
      // 停止录音
      setIsRecording(false);
      // 这里应该实现实际的语音识别逻辑
      onTranscript("Voice input not implemented yet");
    } else {
      // 开始录音
      setIsRecording(true);
    }
  };

  return (
    <IconButton
      icon={isRecording ? "⏹️" : "🎤"}
      size="sm"
      variant={isRecording ? "danger" : "ghost"}
      aria-label={isRecording ? "Stop recording" : "Start recording"}
      onClick={handleToggleRecording}
      disabled={disabled}
    />
  );
};
