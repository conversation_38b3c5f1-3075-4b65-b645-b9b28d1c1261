/**
 * File System Tool - 文件系统工具
 * 
 * 提供文件和目录操作功能
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import {
  ITool,
  ToolDefinition,
  ToolExecutionContext,
  ToolExecutionResult,
  ValidationResult,
  ToolUsageStats,
  FileSystemOperation,
  FileInfo,
  ToolExecutionError,
} from '../interfaces';

export class FileSystemTool implements ITool {
  public readonly definition: ToolDefinition = {
    name: 'file_system',
    description: 'Perform file system operations like read, write, create, delete files and directories',
    category: 'file-system',
    parameters: {
      type: 'object',
      properties: {
        operation: {
          type: 'string',
          description: 'The operation to perform',
          enum: ['read', 'write', 'create', 'delete', 'move', 'copy', 'list', 'exists', 'stat'],
        },
        path: {
          type: 'string',
          description: 'The file or directory path',
        },
        content: {
          type: 'string',
          description: 'Content to write (for write/create operations)',
        },
        destination: {
          type: 'string',
          description: 'Destination path (for move/copy operations)',
        },
        encoding: {
          type: 'string',
          description: 'File encoding (default: utf8)',
          default: 'utf8',
        },
        recursive: {
          type: 'boolean',
          description: 'Recursive operation for directories',
          default: false,
        },
        overwrite: {
          type: 'boolean',
          description: 'Overwrite existing files',
          default: false,
        },
      },
      required: ['operation', 'path'],
    },
    permissions: ['read-files', 'write-files'],
    examples: [
      {
        description: 'Read a file',
        parameters: {
          operation: 'read',
          path: './src/index.ts',
        },
      },
      {
        description: 'Write content to a file',
        parameters: {
          operation: 'write',
          path: './src/new-file.ts',
          content: 'export const hello = "world";',
        },
      },
      {
        description: 'List directory contents',
        parameters: {
          operation: 'list',
          path: './src',
          recursive: true,
        },
      },
    ],
  };

  private usageStats: ToolUsageStats = {
    totalExecutions: 0,
    successfulExecutions: 0,
    failedExecutions: 0,
    averageExecutionTime: 0,
  };

  async execute(
    parameters: Record<string, any>,
    context: ToolExecutionContext
  ): Promise<ToolExecutionResult> {
    const startTime = Date.now();
    this.usageStats.totalExecutions++;

    try {
      const operation = parameters.operation as string;
      const filePath = this.resolvePath(parameters.path, context.workspaceRoot);
      
      let result: any;
      const filesModified: string[] = [];

      switch (operation) {
        case 'read':
          result = await this.readFile(filePath, parameters.encoding);
          break;

        case 'write':
          await this.writeFile(filePath, parameters.content, parameters.encoding, parameters.overwrite);
          filesModified.push(filePath);
          result = { success: true, path: filePath };
          break;

        case 'create':
          await this.createFile(filePath, parameters.content || '', parameters.encoding);
          filesModified.push(filePath);
          result = { success: true, path: filePath };
          break;

        case 'delete':
          await this.deleteFile(filePath, parameters.recursive);
          result = { success: true, path: filePath };
          break;

        case 'move':
          const moveDest = this.resolvePath(parameters.destination, context.workspaceRoot);
          await this.moveFile(filePath, moveDest);
          filesModified.push(moveDest);
          result = { success: true, from: filePath, to: moveDest };
          break;

        case 'copy':
          const copyDest = this.resolvePath(parameters.destination, context.workspaceRoot);
          await this.copyFile(filePath, copyDest, parameters.recursive);
          filesModified.push(copyDest);
          result = { success: true, from: filePath, to: copyDest };
          break;

        case 'list':
          result = await this.listDirectory(filePath, parameters.recursive);
          break;

        case 'exists':
          result = await this.fileExists(filePath);
          break;

        case 'stat':
          result = await this.getFileStats(filePath);
          break;

        default:
          throw new ToolExecutionError('file_system', `Unknown operation: ${operation}`);
      }

      const duration = Date.now() - startTime;
      this.updateStats(duration, true);

      return {
        success: true,
        result,
        metadata: {
          duration,
          filesModified,
        },
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      this.updateStats(duration, false);

      return {
        success: false,
        error: (error as Error).message,
        metadata: { duration },
      };
    }
  }

  validate(parameters: Record<string, any>): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!parameters.operation) {
      errors.push('Operation is required');
    } else if (!['read', 'write', 'create', 'delete', 'move', 'copy', 'list', 'exists', 'stat'].includes(parameters.operation)) {
      errors.push('Invalid operation');
    }

    if (!parameters.path) {
      errors.push('Path is required');
    }

    if (['write', 'create'].includes(parameters.operation) && parameters.content === undefined) {
      warnings.push('Content not provided for write/create operation');
    }

    if (['move', 'copy'].includes(parameters.operation) && !parameters.destination) {
      errors.push('Destination is required for move/copy operations');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  }

  getUsageStats(): ToolUsageStats {
    return { ...this.usageStats };
  }

  // 私有方法
  private resolvePath(filePath: string, workspaceRoot?: string): string {
    if (path.isAbsolute(filePath)) {
      return filePath;
    }
    
    if (workspaceRoot) {
      return path.resolve(workspaceRoot, filePath);
    }
    
    return path.resolve(filePath);
  }

  private async readFile(filePath: string, encoding: string = 'utf8'): Promise<string> {
    try {
      return await fs.readFile(filePath, encoding as BufferEncoding);
    } catch (error) {
      throw new ToolExecutionError('file_system', `Failed to read file: ${(error as Error).message}`);
    }
  }

  private async writeFile(filePath: string, content: string, encoding: string = 'utf8', overwrite: boolean = false): Promise<void> {
    try {
      if (!overwrite && await this.fileExists(filePath)) {
        throw new Error('File already exists and overwrite is false');
      }

      // 确保目录存在
      await fs.mkdir(path.dirname(filePath), { recursive: true });
      await fs.writeFile(filePath, content, encoding as BufferEncoding);
    } catch (error) {
      throw new ToolExecutionError('file_system', `Failed to write file: ${(error as Error).message}`);
    }
  }

  private async createFile(filePath: string, content: string = '', encoding: string = 'utf8'): Promise<void> {
    try {
      if (await this.fileExists(filePath)) {
        throw new Error('File already exists');
      }

      await this.writeFile(filePath, content, encoding, false);
    } catch (error) {
      throw new ToolExecutionError('file_system', `Failed to create file: ${(error as Error).message}`);
    }
  }

  private async deleteFile(filePath: string, recursive: boolean = false): Promise<void> {
    try {
      const stats = await fs.stat(filePath);
      
      if (stats.isDirectory()) {
        await fs.rmdir(filePath, { recursive });
      } else {
        await fs.unlink(filePath);
      }
    } catch (error) {
      throw new ToolExecutionError('file_system', `Failed to delete: ${(error as Error).message}`);
    }
  }

  private async moveFile(source: string, destination: string): Promise<void> {
    try {
      await fs.mkdir(path.dirname(destination), { recursive: true });
      await fs.rename(source, destination);
    } catch (error) {
      throw new ToolExecutionError('file_system', `Failed to move file: ${(error as Error).message}`);
    }
  }

  private async copyFile(source: string, destination: string, recursive: boolean = false): Promise<void> {
    try {
      await fs.mkdir(path.dirname(destination), { recursive: true });
      
      const stats = await fs.stat(source);
      
      if (stats.isDirectory()) {
        if (!recursive) {
          throw new Error('Cannot copy directory without recursive flag');
        }
        await this.copyDirectory(source, destination);
      } else {
        await fs.copyFile(source, destination);
      }
    } catch (error) {
      throw new ToolExecutionError('file_system', `Failed to copy: ${(error as Error).message}`);
    }
  }

  private async copyDirectory(source: string, destination: string): Promise<void> {
    await fs.mkdir(destination, { recursive: true });
    
    const entries = await fs.readdir(source, { withFileTypes: true });
    
    for (const entry of entries) {
      const srcPath = path.join(source, entry.name);
      const destPath = path.join(destination, entry.name);
      
      if (entry.isDirectory()) {
        await this.copyDirectory(srcPath, destPath);
      } else {
        await fs.copyFile(srcPath, destPath);
      }
    }
  }

  private async listDirectory(dirPath: string, recursive: boolean = false): Promise<FileInfo[]> {
    try {
      const result: FileInfo[] = [];
      
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        const stats = await fs.stat(fullPath);
        
        const fileInfo: FileInfo = {
          path: fullPath,
          name: entry.name,
          size: stats.size,
          type: entry.isDirectory() ? 'directory' : 'file',
          extension: entry.isFile() ? path.extname(entry.name).slice(1) : undefined,
          lastModified: stats.mtime.getTime(),
          permissions: {
            readable: true, // 简化处理
            writable: true,
            executable: entry.isFile() && (stats.mode & 0o111) !== 0,
          },
        };
        
        result.push(fileInfo);
        
        if (recursive && entry.isDirectory()) {
          const subFiles = await this.listDirectory(fullPath, true);
          result.push(...subFiles);
        }
      }
      
      return result;
    } catch (error) {
      throw new ToolExecutionError('file_system', `Failed to list directory: ${(error as Error).message}`);
    }
  }

  private async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  private async getFileStats(filePath: string): Promise<FileInfo> {
    try {
      const stats = await fs.stat(filePath);
      
      return {
        path: filePath,
        name: path.basename(filePath),
        size: stats.size,
        type: stats.isDirectory() ? 'directory' : 'file',
        extension: stats.isFile() ? path.extname(filePath).slice(1) : undefined,
        lastModified: stats.mtime.getTime(),
        permissions: {
          readable: true,
          writable: true,
          executable: stats.isFile() && (stats.mode & 0o111) !== 0,
        },
      };
    } catch (error) {
      throw new ToolExecutionError('file_system', `Failed to get file stats: ${(error as Error).message}`);
    }
  }

  private updateStats(duration: number, success: boolean): void {
    if (success) {
      this.usageStats.successfulExecutions++;
    } else {
      this.usageStats.failedExecutions++;
    }

    // 更新平均执行时间
    const totalTime = this.usageStats.averageExecutionTime * (this.usageStats.totalExecutions - 1) + duration;
    this.usageStats.averageExecutionTime = totalTime / this.usageStats.totalExecutions;
    this.usageStats.lastUsed = Date.now();
  }
}
