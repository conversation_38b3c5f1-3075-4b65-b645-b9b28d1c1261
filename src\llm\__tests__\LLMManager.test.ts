/**
 * LLMManager Tests
 */

import { LLMManager } from '../LLMManager';
import { EventBus } from '@/core/EventBus';
import { ILLMProvider, LLMRequestConfig, LLMResponse, ModelInfo, HealthStatus } from '../interfaces';
import { Message, LLMProvider } from '@/types';

// Mock LLM Provider
class MockLLMProvider implements ILLMProvider {
  public readonly name: LLMProvider = 'openai';
  public readonly models = ['gpt-4', 'gpt-3.5-turbo'];
  public readonly supportsStreaming = true;
  public readonly supportsTools = true;
  public readonly supportsVision = false;

  private mockResponse: LLMResponse = {
    id: 'test-response',
    content: 'Test response',
    role: 'assistant',
    model: 'gpt-4',
    usage: {
      promptTokens: 10,
      completionTokens: 20,
      totalTokens: 30,
    },
  };

  configure(config: Record<string, any>): void {
    // Mock implementation
  }

  async validateConfig(): Promise<boolean> {
    return true;
  }

  getModelInfo(model: string): ModelInfo | undefined {
    if (model === 'gpt-4') {
      return {
        name: 'gpt-4',
        displayName: 'GPT-4',
        description: 'Test model',
        maxTokens: 4096,
        inputCost: 0.03,
        outputCost: 0.06,
        supportsTools: true,
        supportsVision: false,
        contextWindow: 8192,
      };
    }
    return undefined;
  }

  async healthCheck(): Promise<HealthStatus> {
    return {
      status: 'healthy',
      latency: 100,
      timestamp: Date.now(),
    };
  }

  async chat(messages: Message[], config: LLMRequestConfig): Promise<LLMResponse> {
    return this.mockResponse;
  }

  async* chatStream(messages: Message[], config: LLMRequestConfig) {
    yield {
      id: 'test-chunk',
      delta: { content: 'Test' },
    };
    yield {
      id: 'test-chunk',
      delta: { content: ' response' },
    };
  }

  async callTool(toolCall: any, context?: any) {
    return {
      id: toolCall.id,
      result: 'Tool result',
    };
  }

  setMockResponse(response: LLMResponse) {
    this.mockResponse = response;
  }
}

describe('LLMManager', () => {
  let llmManager: LLMManager;
  let eventBus: EventBus;
  let mockProvider: MockLLMProvider;

  beforeEach(() => {
    eventBus = new EventBus();
    llmManager = new LLMManager(eventBus);
    mockProvider = new MockLLMProvider();
  });

  afterEach(() => {
    llmManager.dispose();
    eventBus.destroy();
  });

  describe('provider management', () => {
    it('should register a provider', () => {
      llmManager.registerProvider(mockProvider);
      
      const providers = llmManager.listProviders();
      expect(providers).toContain('openai');
      
      const provider = llmManager.getProvider('openai');
      expect(provider).toBe(mockProvider);
    });

    it('should unregister a provider', () => {
      llmManager.registerProvider(mockProvider);
      llmManager.unregisterProvider('openai');
      
      const providers = llmManager.listProviders();
      expect(providers).not.toContain('openai');
      
      const provider = llmManager.getProvider('openai');
      expect(provider).toBeUndefined();
    });

    it('should emit events when registering/unregistering providers', async () => {
      const eventListener = jest.fn();
      eventBus.subscribe('llm.provider_registered', eventListener);
      eventBus.subscribe('llm.provider_unregistered', eventListener);

      llmManager.registerProvider(mockProvider);
      llmManager.unregisterProvider('openai');

      // Wait for events to be processed
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(eventListener).toHaveBeenCalledTimes(2);
    });
  });

  describe('chat functionality', () => {
    beforeEach(() => {
      llmManager.registerProvider(mockProvider);
    });

    it('should perform chat request', async () => {
      const messages: Message[] = [
        {
          id: '1',
          type: 'user',
          content: 'Hello',
          timestamp: Date.now(),
        },
      ];

      const config: LLMRequestConfig = {
        model: 'gpt-4',
        temperature: 0.7,
        maxTokens: 1000,
      };

      const response = await llmManager.chat('openai', messages, config);

      expect(response).toBeDefined();
      expect(response.content).toBe('Test response');
      expect(response.model).toBe('gpt-4');
    });

    it('should handle chat stream', async () => {
      const messages: Message[] = [
        {
          id: '1',
          type: 'user',
          content: 'Hello',
          timestamp: Date.now(),
        },
      ];

      const config: LLMRequestConfig = {
        model: 'gpt-4',
        stream: true,
      };

      const chunks = [];
      for await (const chunk of llmManager.chatStream('openai', messages, config)) {
        chunks.push(chunk);
      }

      expect(chunks).toHaveLength(2);
      expect(chunks[0].delta.content).toBe('Test');
      expect(chunks[1].delta.content).toBe(' response');
    });

    it('should throw error for unknown provider', async () => {
      const messages: Message[] = [];
      const config: LLMRequestConfig = { model: 'test' };

      await expect(
        llmManager.chat('claude' as LLMProvider, messages, config)
      ).rejects.toThrow('Provider claude not found');
    });

    it('should emit events during chat request', async () => {
      const eventListener = jest.fn();
      eventBus.subscribe('llm.request_started', eventListener);
      eventBus.subscribe('llm.request_completed', eventListener);

      const messages: Message[] = [
        {
          id: '1',
          type: 'user',
          content: 'Hello',
          timestamp: Date.now(),
        },
      ];

      const config: LLMRequestConfig = {
        model: 'gpt-4',
      };

      await llmManager.chat('openai', messages, config);

      // Wait for events to be processed
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(eventListener).toHaveBeenCalledTimes(2);
    });
  });

  describe('model selection', () => {
    beforeEach(() => {
      llmManager.registerProvider(mockProvider);
    });

    it('should select best model based on requirements', () => {
      const requirements = {
        supportsTools: true,
        qualityLevel: 'premium' as const,
      };

      const result = llmManager.selectBestModel(requirements);

      expect(result).toBeDefined();
      expect(result?.provider).toBe('openai');
      expect(result?.model).toBe('gpt-4');
    });

    it('should return null if no model meets requirements', () => {
      const requirements = {
        supportsVision: true, // Mock provider doesn't support vision
      };

      const result = llmManager.selectBestModel(requirements);

      expect(result).toBeNull();
    });
  });

  describe('tool execution', () => {
    it('should execute tool call', async () => {
      const toolCall = {
        id: 'tool-1',
        name: 'test_tool',
        arguments: { param: 'value' },
      };

      const result = await llmManager.executeToolCall(toolCall);

      expect(result).toBeDefined();
      expect(result.id).toBe('tool-1');
      expect(result.result).toContain('Tool test_tool executed successfully');
    });

    it('should emit events during tool execution', async () => {
      const eventListener = jest.fn();
      eventBus.subscribe('llm.tool_call_started', eventListener);
      eventBus.subscribe('llm.tool_call_completed', eventListener);

      const toolCall = {
        id: 'tool-1',
        name: 'test_tool',
        arguments: {},
      };

      await llmManager.executeToolCall(toolCall);

      // Wait for events to be processed
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(eventListener).toHaveBeenCalledTimes(2);
    });
  });

  describe('statistics and monitoring', () => {
    beforeEach(() => {
      llmManager.registerProvider(mockProvider);
    });

    it('should provide usage statistics', async () => {
      // Perform some requests to generate stats
      const messages: Message[] = [
        {
          id: '1',
          type: 'user',
          content: 'Hello',
          timestamp: Date.now(),
        },
      ];

      const config: LLMRequestConfig = { model: 'gpt-4' };

      await llmManager.chat('openai', messages, config);

      const stats = llmManager.getUsageStats();

      expect(stats).toBeDefined();
      expect(stats.totalRequests).toBeGreaterThan(0);
    });

    it('should provide health status', async () => {
      const healthStatus = await llmManager.getHealthStatus();

      expect(healthStatus).toBeDefined();
      expect(healthStatus.openai).toBeDefined();
      expect(healthStatus.openai.status).toBe('healthy');
    });
  });

  describe('disposal', () => {
    it('should dispose cleanly', () => {
      llmManager.registerProvider(mockProvider);
      
      expect(() => {
        llmManager.dispose();
      }).not.toThrow();

      expect(llmManager.listProviders()).toHaveLength(0);
    });
  });
});
