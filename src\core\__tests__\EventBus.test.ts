/**
 * EventBus Tests
 */

import { EventBus } from '../EventBus';
import { BaseEvent } from '@/types';

interface TestEvent extends BaseEvent {
  type: 'test.event';
  data: string;
}

describe('EventBus', () => {
  let eventBus: EventBus;

  beforeEach(() => {
    eventBus = new EventBus();
  });

  afterEach(() => {
    eventBus.destroy();
  });

  describe('event subscription and emission', () => {
    it('should subscribe and emit events', async () => {
      const handler = jest.fn();
      
      eventBus.subscribe('test.event', handler);
      
      await eventBus.emit({
        type: 'test.event',
        source: 'test',
        data: 'test data',
      });

      expect(handler).toHaveBeenCalledTimes(1);
      expect(handler).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'test.event',
          source: 'test',
          data: 'test data',
          id: expect.any(String),
          timestamp: expect.any(Number),
        })
      );
    });

    it('should support multiple subscribers for same event', async () => {
      const handler1 = jest.fn();
      const handler2 = jest.fn();
      
      eventBus.subscribe('test.event', handler1);
      eventBus.subscribe('test.event', handler2);
      
      await eventBus.emit({
        type: 'test.event',
        source: 'test',
        data: 'test data',
      });

      expect(handler1).toHaveBeenCalledTimes(1);
      expect(handler2).toHaveBeenCalledTimes(1);
    });

    it('should unsubscribe correctly', async () => {
      const handler = jest.fn();
      
      const unsubscribe = eventBus.subscribe('test.event', handler);
      unsubscribe();
      
      await eventBus.emit({
        type: 'test.event',
        source: 'test',
        data: 'test data',
      });

      expect(handler).not.toHaveBeenCalled();
    });
  });

  describe('global subscriptions', () => {
    it('should subscribe to all events', async () => {
      const globalHandler = jest.fn();
      
      eventBus.subscribeAll(globalHandler);
      
      await eventBus.emit({
        type: 'test.event1',
        source: 'test',
        data: 'data1',
      });
      
      await eventBus.emit({
        type: 'test.event2',
        source: 'test',
        data: 'data2',
      });

      expect(globalHandler).toHaveBeenCalledTimes(2);
    });
  });

  describe('event filtering', () => {
    it('should filter events based on condition', async () => {
      const handler = jest.fn();
      const filter = (event: TestEvent) => event.data === 'allowed';
      
      eventBus.subscribe('test.event', handler, { filter });
      
      await eventBus.emit({
        type: 'test.event',
        source: 'test',
        data: 'allowed',
      });
      
      await eventBus.emit({
        type: 'test.event',
        source: 'test',
        data: 'blocked',
      });

      expect(handler).toHaveBeenCalledTimes(1);
      expect(handler).toHaveBeenCalledWith(
        expect.objectContaining({ data: 'allowed' })
      );
    });
  });

  describe('event priority', () => {
    it('should execute handlers in priority order', async () => {
      const executionOrder: number[] = [];
      
      const handler1 = jest.fn(() => { executionOrder.push(1); });
      const handler2 = jest.fn(() => { executionOrder.push(2); });
      const handler3 = jest.fn(() => { executionOrder.push(3); });
      
      eventBus.subscribe('test.event', handler1, { priority: 1 });
      eventBus.subscribe('test.event', handler3, { priority: 3 });
      eventBus.subscribe('test.event', handler2, { priority: 2 });
      
      await eventBus.emit({
        type: 'test.event',
        source: 'test',
        data: 'test',
      });

      expect(executionOrder).toEqual([3, 2, 1]); // 高优先级先执行
    });
  });

  describe('once subscriptions', () => {
    it('should execute handler only once', async () => {
      const handler = jest.fn();
      
      eventBus.subscribe('test.event', handler, { once: true });
      
      await eventBus.emit({
        type: 'test.event',
        source: 'test',
        data: 'first',
      });
      
      await eventBus.emit({
        type: 'test.event',
        source: 'test',
        data: 'second',
      });

      expect(handler).toHaveBeenCalledTimes(1);
      expect(handler).toHaveBeenCalledWith(
        expect.objectContaining({ data: 'first' })
      );
    });
  });

  describe('async handlers', () => {
    it('should handle async event handlers', async () => {
      const handler = jest.fn(async () => {
        await new Promise(resolve => setTimeout(resolve, 10));
      });
      
      eventBus.subscribe('test.event', handler);
      
      await eventBus.emit({
        type: 'test.event',
        source: 'test',
        data: 'async test',
      });

      expect(handler).toHaveBeenCalledTimes(1);
    });
  });

  describe('error handling', () => {
    it('should handle handler errors gracefully', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      const errorHandler = jest.fn(() => {
        throw new Error('Handler error');
      });
      const normalHandler = jest.fn();
      
      eventBus.subscribe('test.event', errorHandler);
      eventBus.subscribe('test.event', normalHandler);
      
      await eventBus.emit({
        type: 'test.event',
        source: 'test',
        data: 'error test',
      });

      expect(consoleSpy).toHaveBeenCalled();
      expect(normalHandler).toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });
  });

  describe('event history', () => {
    it('should maintain event history', async () => {
      await eventBus.emit({
        type: 'test.event1',
        source: 'test',
        data: 'data1',
      });
      
      await eventBus.emit({
        type: 'test.event2',
        source: 'test',
        data: 'data2',
      });

      const history = eventBus.getEventHistory();
      expect(history).toHaveLength(2);
      expect(history[0].event.type).toBe('test.event1');
      expect(history[1].event.type).toBe('test.event2');
    });

    it('should filter event history', async () => {
      await eventBus.emit({
        type: 'test.event1',
        source: 'test',
        data: 'data1',
      });
      
      await eventBus.emit({
        type: 'test.event2',
        source: 'test',
        data: 'data2',
      });

      const filteredHistory = eventBus.getEventHistory({
        eventType: 'test.event1',
      });
      
      expect(filteredHistory).toHaveLength(1);
      expect(filteredHistory[0].event.type).toBe('test.event1');
    });

    it('should clear event history', async () => {
      await eventBus.emit({
        type: 'test.event',
        source: 'test',
        data: 'data',
      });

      eventBus.clearHistory();
      const history = eventBus.getEventHistory();
      
      expect(history).toHaveLength(0);
    });
  });

  describe('subscription stats', () => {
    it('should provide subscription statistics', () => {
      eventBus.subscribe('event1', jest.fn());
      eventBus.subscribe('event1', jest.fn());
      eventBus.subscribe('event2', jest.fn());
      eventBus.subscribeAll(jest.fn());

      const stats = eventBus.getSubscriptionStats();
      
      expect(stats.totalSubscriptions).toBe(4);
      expect(stats.subscriptionsByType.event1).toBe(2);
      expect(stats.subscriptionsByType.event2).toBe(1);
      expect(stats.globalSubscriptions).toBe(1);
    });
  });

  describe('waitForEvent', () => {
    it('should wait for specific event', async () => {
      const eventPromise = eventBus.waitForEvent('test.event');
      
      setTimeout(() => {
        eventBus.emit({
          type: 'test.event',
          source: 'test',
          data: 'waited data',
        });
      }, 10);

      const event = await eventPromise;
      expect((event as any).data).toBe('waited data');
    });

    it('should timeout when waiting for event', async () => {
      const eventPromise = eventBus.waitForEvent('test.event', undefined, 10);
      
      await expect(eventPromise).rejects.toThrow('Timeout waiting for event');
    });
  });
});
