/**
 * Button Component Tests
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Button, ButtonGroup, IconButton, ToggleButton } from '../components/Button';
import { ThemeProvider } from '../theme/ThemeProvider';

// 测试包装器
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider defaultTheme="dark">
    {children}
  </ThemeProvider>
);

describe('Button Component', () => {
  it('should render with default props', () => {
    render(
      <TestWrapper>
        <Button>Click me</Button>
      </TestWrapper>
    );

    const button = screen.getByRole('button', { name: /click me/i });
    expect(button).toBeInTheDocument();
  });

  it('should handle click events', () => {
    const handleClick = jest.fn();
    
    render(
      <TestWrapper>
        <Button onClick={handleClick}>Click me</Button>
      </TestWrapper>
    );

    const button = screen.getByRole('button', { name: /click me/i });
    fireEvent.click(button);
    
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('should be disabled when disabled prop is true', () => {
    const handleClick = jest.fn();
    
    render(
      <TestWrapper>
        <Button disabled onClick={handleClick}>Disabled</Button>
      </TestWrapper>
    );

    const button = screen.getByRole('button', { name: /disabled/i });
    expect(button).toBeDisabled();
    
    fireEvent.click(button);
    expect(handleClick).not.toHaveBeenCalled();
  });

  it('should show loading state', () => {
    render(
      <TestWrapper>
        <Button loading>Loading</Button>
      </TestWrapper>
    );

    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
  });

  it('should render with icon', () => {
    render(
      <TestWrapper>
        <Button icon={<span data-testid="icon">🚀</span>}>With Icon</Button>
      </TestWrapper>
    );

    expect(screen.getByTestId('icon')).toBeInTheDocument();
    expect(screen.getByText('With Icon')).toBeInTheDocument();
  });

  it('should apply different variants', () => {
    const { rerender } = render(
      <TestWrapper>
        <Button variant="primary">Primary</Button>
      </TestWrapper>
    );

    let button = screen.getByRole('button');
    expect(button).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <Button variant="secondary">Secondary</Button>
      </TestWrapper>
    );

    button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
  });

  it('should apply different sizes', () => {
    const { rerender } = render(
      <TestWrapper>
        <Button size="sm">Small</Button>
      </TestWrapper>
    );

    let button = screen.getByRole('button');
    expect(button).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <Button size="lg">Large</Button>
      </TestWrapper>
    );

    button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
  });
});

describe('ButtonGroup Component', () => {
  it('should render multiple buttons', () => {
    render(
      <TestWrapper>
        <ButtonGroup>
          <Button>First</Button>
          <Button>Second</Button>
          <Button>Third</Button>
        </ButtonGroup>
      </TestWrapper>
    );

    expect(screen.getByText('First')).toBeInTheDocument();
    expect(screen.getByText('Second')).toBeInTheDocument();
    expect(screen.getByText('Third')).toBeInTheDocument();
  });

  it('should support vertical orientation', () => {
    render(
      <TestWrapper>
        <ButtonGroup orientation="vertical">
          <Button>First</Button>
          <Button>Second</Button>
        </ButtonGroup>
      </TestWrapper>
    );

    const container = screen.getByText('First').parentElement;
    expect(container).toHaveStyle({ flexDirection: 'column' });
  });
});

describe('IconButton Component', () => {
  it('should render icon button', () => {
    render(
      <TestWrapper>
        <IconButton icon={<span data-testid="icon">⚙️</span>} aria-label="Settings" />
      </TestWrapper>
    );

    expect(screen.getByTestId('icon')).toBeInTheDocument();
    expect(screen.getByLabelText('Settings')).toBeInTheDocument();
  });

  it('should handle click events', () => {
    const handleClick = jest.fn();
    
    render(
      <TestWrapper>
        <IconButton 
          icon={<span>⚙️</span>} 
          aria-label="Settings"
          onClick={handleClick}
        />
      </TestWrapper>
    );

    const button = screen.getByLabelText('Settings');
    fireEvent.click(button);
    
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});

describe('ToggleButton Component', () => {
  it('should toggle active state', () => {
    const handleToggle = jest.fn();
    
    render(
      <TestWrapper>
        <ToggleButton active={false} onToggle={handleToggle}>
          Toggle me
        </ToggleButton>
      </TestWrapper>
    );

    const button = screen.getByRole('button', { name: /toggle me/i });
    fireEvent.click(button);
    
    expect(handleToggle).toHaveBeenCalledWith(true);
  });

  it('should show active state', () => {
    render(
      <TestWrapper>
        <ToggleButton active={true} onToggle={jest.fn()}>
          Active
        </ToggleButton>
      </TestWrapper>
    );

    const button = screen.getByRole('button', { name: /active/i });
    expect(button).toBeInTheDocument();
  });

  it('should show inactive state', () => {
    render(
      <TestWrapper>
        <ToggleButton active={false} onToggle={jest.fn()}>
          Inactive
        </ToggleButton>
      </TestWrapper>
    );

    const button = screen.getByRole('button', { name: /inactive/i });
    expect(button).toBeInTheDocument();
  });
});

describe('Button Accessibility', () => {
  it('should have proper ARIA attributes', () => {
    render(
      <TestWrapper>
        <Button disabled>Disabled Button</Button>
      </TestWrapper>
    );

    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('disabled');
  });

  it('should support keyboard navigation', () => {
    const handleClick = jest.fn();
    
    render(
      <TestWrapper>
        <Button onClick={handleClick}>Keyboard Test</Button>
      </TestWrapper>
    );

    const button = screen.getByRole('button');
    button.focus();
    
    fireEvent.keyDown(button, { key: 'Enter' });
    fireEvent.keyDown(button, { key: ' ' });
    
    // Note: React Testing Library doesn't automatically trigger click on Enter/Space
    // In a real browser, this would work due to native button behavior
    expect(button).toHaveFocus();
  });

  it('should have proper aria-label for icon buttons', () => {
    render(
      <TestWrapper>
        <IconButton icon={<span>🔍</span>} aria-label="Search" />
      </TestWrapper>
    );

    const button = screen.getByLabelText('Search');
    expect(button).toBeInTheDocument();
  });
});
