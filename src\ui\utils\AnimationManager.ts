/**
 * 高性能动画管理器
 * 
 * 提供基于requestAnimationFrame的动画系统，确保60fps流畅动画
 * 支持动画队列、性能监控和优雅降级
 */

export interface AnimationConfig {
  duration: number;
  easing?: EasingFunction;
  delay?: number;
  onUpdate?: (progress: number, value: any) => void;
  onComplete?: () => void;
  onCancel?: () => void;
}

export interface TypewriterConfig extends AnimationConfig {
  text: string;
  speed?: number; // 字符/秒
  cursor?: boolean;
  cursorChar?: string;
}

export type EasingFunction = (t: number) => number;

// 缓动函数库
export const Easing = {
  linear: (t: number) => t,
  easeInQuad: (t: number) => t * t,
  easeOutQuad: (t: number) => t * (2 - t),
  easeInOutQuad: (t: number) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
  easeInCubic: (t: number) => t * t * t,
  easeOutCubic: (t: number) => (--t) * t * t + 1,
  easeInOutCubic: (t: number) => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1,
  easeInQuart: (t: number) => t * t * t * t,
  easeOutQuart: (t: number) => 1 - (--t) * t * t * t,
  easeInOutQuart: (t: number) => t < 0.5 ? 8 * t * t * t * t : 1 - 8 * (--t) * t * t * t,
  easeInQuint: (t: number) => t * t * t * t * t,
  easeOutQuint: (t: number) => 1 + (--t) * t * t * t * t,
  easeInOutQuint: (t: number) => t < 0.5 ? 16 * t * t * t * t * t : 1 + 16 * (--t) * t * t * t * t,
  easeInSine: (t: number) => 1 - Math.cos(t * Math.PI / 2),
  easeOutSine: (t: number) => Math.sin(t * Math.PI / 2),
  easeInOutSine: (t: number) => -(Math.cos(Math.PI * t) - 1) / 2,
  easeInExpo: (t: number) => t === 0 ? 0 : Math.pow(2, 10 * (t - 1)),
  easeOutExpo: (t: number) => t === 1 ? 1 : 1 - Math.pow(2, -10 * t),
  easeInOutExpo: (t: number) => {
    if (t === 0) return 0;
    if (t === 1) return 1;
    if (t < 0.5) return Math.pow(2, 20 * t - 10) / 2;
    return (2 - Math.pow(2, -20 * t + 10)) / 2;
  },
  easeInCirc: (t: number) => 1 - Math.sqrt(1 - t * t),
  easeOutCirc: (t: number) => Math.sqrt(1 - (--t) * t),
  easeInOutCirc: (t: number) => t < 0.5 ? (1 - Math.sqrt(1 - 4 * t * t)) / 2 : (Math.sqrt(1 - (-2 * t + 2) * (-2 * t + 2)) + 1) / 2,
  easeInBack: (t: number) => 2.70158 * t * t * t - 1.70158 * t * t,
  easeOutBack: (t: number) => 1 + 2.70158 * (--t) * t * t + 1.70158 * t * t,
  easeInOutBack: (t: number) => {
    const c1 = 1.70158;
    const c2 = c1 * 1.525;
    return t < 0.5
      ? (Math.pow(2 * t, 2) * ((c2 + 1) * 2 * t - c2)) / 2
      : (Math.pow(2 * t - 2, 2) * ((c2 + 1) * (t * 2 - 2) + c2) + 2) / 2;
  },
  easeInElastic: (t: number) => {
    const c4 = (2 * Math.PI) / 3;
    return t === 0 ? 0 : t === 1 ? 1 : -Math.pow(2, 10 * t - 10) * Math.sin((t * 10 - 10.75) * c4);
  },
  easeOutElastic: (t: number) => {
    const c4 = (2 * Math.PI) / 3;
    return t === 0 ? 0 : t === 1 ? 1 : Math.pow(2, -10 * t) * Math.sin((t * 10 - 0.75) * c4) + 1;
  },
  easeInOutElastic: (t: number) => {
    const c5 = (2 * Math.PI) / 4.5;
    return t === 0 ? 0 : t === 1 ? 1 : t < 0.5
      ? -(Math.pow(2, 20 * t - 10) * Math.sin((20 * t - 11.125) * c5)) / 2
      : (Math.pow(2, -20 * t + 10) * Math.sin((20 * t - 11.125) * c5)) / 2 + 1;
  },
  easeInBounce: (t: number) => 1 - Easing.easeOutBounce(1 - t),
  easeOutBounce: (t: number) => {
    const n1 = 7.5625;
    const d1 = 2.75;
    if (t < 1 / d1) {
      return n1 * t * t;
    } else if (t < 2 / d1) {
      return n1 * (t -= 1.5 / d1) * t + 0.75;
    } else if (t < 2.5 / d1) {
      return n1 * (t -= 2.25 / d1) * t + 0.9375;
    } else {
      return n1 * (t -= 2.625 / d1) * t + 0.984375;
    }
  },
  easeInOutBounce: (t: number) => t < 0.5
    ? (1 - Easing.easeOutBounce(1 - 2 * t)) / 2
    : (1 + Easing.easeOutBounce(2 * t - 1)) / 2,
};

interface Animation {
  id: string;
  startTime: number;
  config: AnimationConfig;
  from: any;
  to: any;
  isActive: boolean;
}

interface PerformanceMetrics {
  frameCount: number;
  lastFrameTime: number;
  fps: number;
  averageFps: number;
  frameTimeHistory: number[];
  droppedFrames: number;
}

export class AnimationManager {
  private animations: Map<string, Animation> = new Map();
  private rafId: number | null = null;
  private isRunning = false;
  private performanceMetrics: PerformanceMetrics = {
    frameCount: 0,
    lastFrameTime: 0,
    fps: 60,
    averageFps: 60,
    frameTimeHistory: [],
    droppedFrames: 0,
  };
  
  // 性能配置
  private readonly TARGET_FPS = 60;
  private readonly FRAME_TIME_THRESHOLD = 16.67; // 60fps = 16.67ms per frame
  private readonly MAX_FRAME_TIME_HISTORY = 60; // 保留最近60帧的数据
  private readonly PERFORMANCE_CHECK_INTERVAL = 1000; // 每秒检查一次性能
  
  // 优雅降级配置
  private isReducedMotion = false;
  private performanceMode: 'high' | 'medium' | 'low' = 'high';
  
  constructor() {
    this.detectReducedMotion();
    this.startPerformanceMonitoring();
  }

  /**
   * 检测用户是否偏好减少动画
   */
  private detectReducedMotion(): void {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
      this.isReducedMotion = mediaQuery.matches;
      
      mediaQuery.addEventListener('change', (e) => {
        this.isReducedMotion = e.matches;
        if (this.isReducedMotion) {
          this.setPerformanceMode('low');
        }
      });
    }
  }

  /**
   * 设置性能模式
   */
  public setPerformanceMode(mode: 'high' | 'medium' | 'low'): void {
    this.performanceMode = mode;
    
    // 根据性能模式调整动画
    if (mode === 'low') {
      // 低性能模式：禁用复杂动画，减少动画时长
      this.animations.forEach((animation) => {
        if (animation.config.duration > 300) {
          animation.config.duration = 300;
        }
      });
    }
  }

  /**
   * 启动性能监控
   */
  private startPerformanceMonitoring(): void {
    setInterval(() => {
      this.updatePerformanceMetrics();
      this.adjustPerformanceMode();
    }, this.PERFORMANCE_CHECK_INTERVAL);
  }

  /**
   * 更新性能指标
   */
  private updatePerformanceMetrics(): void {
    const { frameTimeHistory } = this.performanceMetrics;
    
    if (frameTimeHistory.length > 0) {
      const averageFrameTime = frameTimeHistory.reduce((sum, time) => sum + time, 0) / frameTimeHistory.length;
      this.performanceMetrics.averageFps = 1000 / averageFrameTime;
      
      // 计算丢帧数
      const droppedFrames = frameTimeHistory.filter(time => time > this.FRAME_TIME_THRESHOLD * 1.5).length;
      this.performanceMetrics.droppedFrames = droppedFrames;
    }
  }

  /**
   * 根据性能自动调整模式
   */
  private adjustPerformanceMode(): void {
    const { averageFps, droppedFrames } = this.performanceMetrics;
    
    if (averageFps < 30 || droppedFrames > 10) {
      this.setPerformanceMode('low');
    } else if (averageFps < 45 || droppedFrames > 5) {
      this.setPerformanceMode('medium');
    } else if (averageFps > 55 && droppedFrames < 2) {
      this.setPerformanceMode('high');
    }
  }

  /**
   * 创建动画
   */
  public animate(
    id: string,
    from: any,
    to: any,
    config: AnimationConfig
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      // 如果偏好减少动画，立即完成
      if (this.isReducedMotion) {
        config.onUpdate?.(1, to);
        config.onComplete?.();
        resolve();
        return;
      }

      // 取消现有的同ID动画
      this.cancel(id);

      const animation: Animation = {
        id,
        startTime: performance.now() + (config.delay || 0),
        config: {
          ...config,
          onComplete: () => {
            config.onComplete?.();
            resolve();
          },
          onCancel: () => {
            config.onCancel?.();
            reject(new Error('Animation cancelled'));
          },
        },
        from,
        to,
        isActive: true,
      };

      this.animations.set(id, animation);
      this.start();
    });
  }

  /**
   * 取消动画
   */
  public cancel(id: string): void {
    const animation = this.animations.get(id);
    if (animation) {
      animation.isActive = false;
      animation.config.onCancel?.();
      this.animations.delete(id);
    }
  }

  /**
   * 取消所有动画
   */
  public cancelAll(): void {
    this.animations.forEach((animation) => {
      animation.isActive = false;
      animation.config.onCancel?.();
    });
    this.animations.clear();
    this.stop();
  }

  /**
   * 启动动画循环
   */
  private start(): void {
    if (!this.isRunning) {
      this.isRunning = true;
      this.tick();
    }
  }

  /**
   * 停止动画循环
   */
  private stop(): void {
    if (this.rafId) {
      cancelAnimationFrame(this.rafId);
      this.rafId = null;
    }
    this.isRunning = false;
  }

  /**
   * 动画循环tick
   */
  private tick = (): void => {
    const currentTime = performance.now();
    
    // 计算帧时间
    if (this.performanceMetrics.lastFrameTime > 0) {
      const frameTime = currentTime - this.performanceMetrics.lastFrameTime;
      this.performanceMetrics.frameTimeHistory.push(frameTime);
      
      // 限制历史记录长度
      if (this.performanceMetrics.frameTimeHistory.length > this.MAX_FRAME_TIME_HISTORY) {
        this.performanceMetrics.frameTimeHistory.shift();
      }
      
      // 计算当前FPS
      this.performanceMetrics.fps = 1000 / frameTime;
    }
    
    this.performanceMetrics.lastFrameTime = currentTime;
    this.performanceMetrics.frameCount++;

    // 更新所有活动动画
    const activeAnimations = Array.from(this.animations.values()).filter(
      (animation) => animation.isActive
    );

    for (const animation of activeAnimations) {
      this.updateAnimation(animation, currentTime);
    }

    // 如果还有活动动画，继续下一帧
    if (activeAnimations.length > 0) {
      this.rafId = requestAnimationFrame(this.tick);
    } else {
      this.stop();
    }
  };

  /**
   * 更新单个动画
   */
  private updateAnimation(animation: Animation, currentTime: number): void {
    const { startTime, config, from, to } = animation;
    
    if (currentTime < startTime) {
      return; // 动画还未开始
    }

    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / config.duration, 1);
    
    // 应用缓动函数
    const easedProgress = config.easing ? config.easing(progress) : progress;
    
    // 计算当前值
    const currentValue = this.interpolate(from, to, easedProgress);
    
    // 调用更新回调
    config.onUpdate?.(easedProgress, currentValue);
    
    // 检查动画是否完成
    if (progress >= 1) {
      animation.isActive = false;
      config.onComplete?.();
      this.animations.delete(animation.id);
    }
  }

  /**
   * 值插值计算
   */
  private interpolate(from: any, to: any, progress: number): any {
    if (typeof from === 'number' && typeof to === 'number') {
      return from + (to - from) * progress;
    }
    
    if (typeof from === 'object' && typeof to === 'object') {
      const result: any = {};
      for (const key in from) {
        if (key in to) {
          result[key] = this.interpolate(from[key], to[key], progress);
        }
      }
      return result;
    }
    
    // 对于其他类型，在进度超过0.5时切换到目标值
    return progress > 0.5 ? to : from;
  }

  /**
   * 获取性能指标
   */
  public getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.performanceMetrics };
  }

  /**
   * 销毁动画管理器
   */
  public destroy(): void {
    this.cancelAll();
    this.stop();
  }
}

// 全局动画管理器实例
export const animationManager = new AnimationManager();
