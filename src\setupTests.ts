import '@testing-library/jest-dom';

// Mock VS Code API
const mockVSCode = {
  window: {
    showInformationMessage: jest.fn(),
    showWarningMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    createWebviewPanel: jest.fn(),
    createStatusBarItem: jest.fn(() => ({
      text: '',
      tooltip: '',
      command: '',
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn(),
    })),
    showQuickPick: jest.fn(),
    showInputBox: jest.fn(),
    withProgress: jest.fn(),
    createTreeView: jest.fn(),
    createTerminal: jest.fn(),
    createOutputChannel: jest.fn(),
    setStatusBarMessage: jest.fn(() => ({ dispose: jest.fn() })),
    showTextDocument: jest.fn(),
    activeTextEditor: undefined,
    onDidChangeActiveTextEditor: jest.fn(() => ({ dispose: jest.fn() })),
    onDidChangeTextEditorSelection: jest.fn(() => ({ dispose: jest.fn() })),
    onDidOpenTerminal: jest.fn(() => ({ dispose: jest.fn() })),
    onDidCloseTerminal: jest.fn(() => ({ dispose: jest.fn() })),
  },
  workspace: {
    getConfiguration: jest.fn(() => ({
      get: jest.fn(),
      update: jest.fn(),
    })),
    workspaceFolders: [],
    fs: {
      readFile: jest.fn(),
      writeFile: jest.fn(),
    },
    onDidChangeTextDocument: jest.fn(() => ({ dispose: jest.fn() })),
    onDidSaveTextDocument: jest.fn(() => ({ dispose: jest.fn() })),
    onDidOpenTextDocument: jest.fn(() => ({ dispose: jest.fn() })),
    onDidCloseTextDocument: jest.fn(() => ({ dispose: jest.fn() })),
    onDidChangeWorkspaceFolders: jest.fn(() => ({ dispose: jest.fn() })),
    onDidChangeConfiguration: jest.fn(() => ({ dispose: jest.fn() })),
    onDidCreateFiles: jest.fn(() => ({ dispose: jest.fn() })),
    onDidDeleteFiles: jest.fn(() => ({ dispose: jest.fn() })),
    onDidRenameFiles: jest.fn(() => ({ dispose: jest.fn() })),
  },
  commands: {
    registerCommand: jest.fn(() => ({ dispose: jest.fn() })),
    executeCommand: jest.fn(),
  },
  Uri: {
    file: jest.fn((path: string) => ({
      fsPath: path,
      with: jest.fn(() => 'vscode-resource://test'),
    })),
    joinPath: jest.fn(),
  },
  ViewColumn: {
    One: 1,
    Two: 2,
    Three: 3,
  },
  StatusBarAlignment: {
    Left: 1,
    Right: 2,
  },
  ProgressLocation: {
    SourceControl: 1,
    Window: 10,
    Notification: 15,
  },
  WebviewViewProvider: class {},
  Disposable: class {
    dispose() {}
  },
};

// Make vscode module available globally for tests
(global as any).vscode = mockVSCode;

// Mock vscode module for Jest
jest.mock('vscode', () => mockVSCode, { virtual: true });

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Setup fetch mock for API calls
global.fetch = jest.fn();

// Mock crypto for UUID generation
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: () => `test-uuid-${  Math.random().toString(36).substr(2, 9)}`,
  },
});
