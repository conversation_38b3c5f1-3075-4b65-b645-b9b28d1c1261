/**
 * Event Bus - 事件总线系统
 * 
 * 提供模块间的松耦合通信机制，支持事件的发布、订阅、过滤和优先级处理
 */

import { BaseEvent } from '@/types';
import { v4 as uuidv4 } from 'uuid';

type EventHandler<T extends BaseEvent = BaseEvent> = (event: T) => void | Promise<void>;
type EventFilter<T extends BaseEvent = BaseEvent> = (event: T) => boolean;

interface EventSubscription {
  id: string;
  eventType: string;
  handler: EventHandler;
  filter?: EventFilter;
  priority: number;
  once: boolean;
}

interface EventHistory {
  event: BaseEvent;
  timestamp: number;
  handled: boolean;
  error?: Error;
}

export class EventBus {
  private subscriptions: Map<string, EventSubscription[]> = new Map();
  private globalSubscriptions: EventSubscription[] = [];
  private eventHistory: EventHistory[] = [];
  private maxHistorySize = 1000;
  private isProcessing = false;
  private eventQueue: BaseEvent[] = [];

  /**
   * 订阅特定类型的事件
   */
  subscribe<T extends BaseEvent>(
    eventType: string,
    handler: EventHandler<T>,
    options: {
      filter?: EventFilter<T>;
      priority?: number;
      once?: boolean;
    } = {}
  ): () => void {
    const subscription: EventSubscription = {
      id: uuidv4(),
      eventType,
      handler: handler as EventHandler,
      filter: options.filter as EventFilter,
      priority: options.priority ?? 0,
      once: options.once ?? false,
    };

    if (!this.subscriptions.has(eventType)) {
      this.subscriptions.set(eventType, []);
    }

    const subscriptions = this.subscriptions.get(eventType)!;
    subscriptions.push(subscription);
    
    // 按优先级排序（高优先级先执行）
    subscriptions.sort((a, b) => b.priority - a.priority);

    // 返回取消订阅函数
    return () => {
      this.unsubscribe(subscription.id);
    };
  }

  /**
   * 订阅事件（subscribe的别名）
   */
  on<T extends BaseEvent>(
    eventType: string,
    handler: EventHandler<T>,
    options: {
      filter?: EventFilter<T>;
      priority?: number;
      once?: boolean;
    } = {}
  ): () => void {
    return this.subscribe(eventType, handler, options);
  }

  /**
   * 订阅所有事件
   */
  subscribeAll<T extends BaseEvent>(
    handler: EventHandler<T>,
    options: {
      filter?: EventFilter<T>;
      priority?: number;
      once?: boolean;
    } = {}
  ): () => void {
    const subscription: EventSubscription = {
      id: uuidv4(),
      eventType: '*',
      handler: handler as EventHandler,
      filter: options.filter as EventFilter,
      priority: options.priority ?? 0,
      once: options.once ?? false,
    };

    this.globalSubscriptions.push(subscription);
    this.globalSubscriptions.sort((a, b) => b.priority - a.priority);

    return () => {
      this.unsubscribe(subscription.id);
    };
  }

  /**
   * 取消订阅
   */
  private unsubscribe(subscriptionId: string): void {
    // 从特定事件订阅中移除
    for (const [eventType, subscriptions] of this.subscriptions.entries()) {
      const index = subscriptions.findIndex(sub => sub.id === subscriptionId);
      if (index !== -1) {
        subscriptions.splice(index, 1);
        if (subscriptions.length === 0) {
          this.subscriptions.delete(eventType);
        }
        return;
      }
    }

    // 从全局订阅中移除
    const globalIndex = this.globalSubscriptions.findIndex(sub => sub.id === subscriptionId);
    if (globalIndex !== -1) {
      this.globalSubscriptions.splice(globalIndex, 1);
    }
  }

  /**
   * 发布事件
   */
  async emit<T extends BaseEvent>(event: Omit<T, 'id' | 'timestamp'>): Promise<void> {
    const fullEvent: BaseEvent = {
      ...event,
      id: uuidv4(),
      timestamp: Date.now(),
    } as T;

    // 如果正在处理事件，加入队列
    if (this.isProcessing) {
      this.eventQueue.push(fullEvent);
      return;
    }

    await this.processEvent(fullEvent);
    
    // 处理队列中的事件
    while (this.eventQueue.length > 0) {
      const queuedEvent = this.eventQueue.shift()!;
      await this.processEvent(queuedEvent);
    }
  }

  /**
   * 处理单个事件
   */
  private async processEvent(event: BaseEvent): Promise<void> {
    this.isProcessing = true;
    
    const historyEntry: EventHistory = {
      event,
      timestamp: Date.now(),
      handled: false,
    };

    try {
      // 处理特定类型的订阅
      const typeSubscriptions = this.subscriptions.get(event.type) || [];
      await this.executeSubscriptions(typeSubscriptions, event);

      // 处理全局订阅
      await this.executeSubscriptions(this.globalSubscriptions, event);

      historyEntry.handled = true;
    } catch (error) {
      historyEntry.error = error as Error;
      console.error(`Error processing event ${event.type}:`, error);
    } finally {
      this.addToHistory(historyEntry);
      this.isProcessing = false;
    }
  }

  /**
   * 执行订阅处理器
   */
  private async executeSubscriptions(
    subscriptions: EventSubscription[],
    event: BaseEvent
  ): Promise<void> {
    const toRemove: string[] = [];

    for (const subscription of subscriptions) {
      try {
        // 应用过滤器
        if (subscription.filter && !subscription.filter(event)) {
          continue;
        }

        // 执行处理器
        await subscription.handler(event);

        // 如果是一次性订阅，标记为移除
        if (subscription.once) {
          toRemove.push(subscription.id);
        }
      } catch (error) {
        console.error(`Error in event handler for ${event.type}:`, error);
      }
    }

    // 移除一次性订阅
    toRemove.forEach(id => this.unsubscribe(id));
  }

  /**
   * 添加到历史记录
   */
  private addToHistory(entry: EventHistory): void {
    this.eventHistory.push(entry);
    
    // 限制历史记录大小
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory.shift();
    }
  }

  /**
   * 获取事件历史
   */
  getEventHistory(filter?: {
    eventType?: string;
    timeRange?: { start: number; end: number };
    onlyErrors?: boolean;
  }): EventHistory[] {
    let history = [...this.eventHistory];

    if (filter) {
      if (filter.eventType) {
        history = history.filter(entry => entry.event.type === filter.eventType);
      }

      if (filter.timeRange) {
        history = history.filter(
          entry =>
            entry.timestamp >= filter.timeRange!.start &&
            entry.timestamp <= filter.timeRange!.end
        );
      }

      if (filter.onlyErrors) {
        history = history.filter(entry => entry.error);
      }
    }

    return history;
  }

  /**
   * 清空事件历史
   */
  clearHistory(): void {
    this.eventHistory = [];
  }

  /**
   * 获取订阅统计信息
   */
  getSubscriptionStats(): {
    totalSubscriptions: number;
    subscriptionsByType: Record<string, number>;
    globalSubscriptions: number;
  } {
    const subscriptionsByType: Record<string, number> = {};
    let totalSubscriptions = 0;

    for (const [eventType, subscriptions] of this.subscriptions.entries()) {
      subscriptionsByType[eventType] = subscriptions.length;
      totalSubscriptions += subscriptions.length;
    }

    return {
      totalSubscriptions: totalSubscriptions + this.globalSubscriptions.length,
      subscriptionsByType,
      globalSubscriptions: this.globalSubscriptions.length,
    };
  }

  /**
   * 等待特定事件
   */
  waitForEvent<T extends BaseEvent>(
    eventType: string,
    filter?: EventFilter<T>,
    timeout?: number
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      let timeoutId: NodeJS.Timeout | undefined;
      
      if (timeout) {
        timeoutId = setTimeout(() => {
          reject(new Error(`Timeout waiting for event: ${eventType}`));
        }, timeout);
      }

      const unsubscribe = this.subscribe<T>(
        eventType,
        (event) => {
          if (timeoutId) {
            clearTimeout(timeoutId);
          }
          unsubscribe();
          resolve(event);
        },
        { filter, once: true }
      );
    });
  }

  /**
   * 销毁事件总线
   */
  destroy(): void {
    this.subscriptions.clear();
    this.globalSubscriptions = [];
    this.eventHistory = [];
    this.eventQueue = [];
    this.isProcessing = false;
  }
}
