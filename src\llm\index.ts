/**
 * LLM Module Exports
 * 
 * 导出LLM集成模块的所有公共接口
 */

export { LLMManager } from './LLMManager';
export { OpenAIProvider } from './providers/OpenAIProvider';
export { ClaudeProvider } from './providers/ClaudeProvider';

export type {
  ILLMProvider,
  ILLMManager,
  LLMRequestConfig,
  LLMResponse,
  LLMStreamChunk,
  ModelInfo,
  HealthStatus,
  ModelRequirements,
  UsageStats,
  ProviderConfig,
  OpenAIConfig,
  ClaudeConfig,
  GeminiConfig,
  DeepSeekConfig,
} from './interfaces';

export {
  LLMError,
  RateLimitError,
  QuotaExceededError,
  ModelUnavailableError,
  ToolCallError,
} from './interfaces';

// 工厂函数
export function createLLMManager(eventBus: any) {
  return new LLMManager(eventBus);
}

export function createOpenAIProvider(config: any) {
  return new OpenAIProvider(config);
}

export function createClaudeProvider(config: any) {
  return new ClaudeProvider(config);
}
