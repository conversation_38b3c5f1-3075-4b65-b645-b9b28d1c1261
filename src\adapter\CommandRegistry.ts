/**
 * Command Registry - 命令注册器
 * 
 * 负责VS Code命令的注册、管理和执行
 */

import * as vscode from 'vscode';
import { EventBus } from '@/core/EventBus';
import { CommandHandler } from './interfaces';

interface CommandDefinition {
  id: string;
  handler: CommandHandler;
  disposable: vscode.Disposable;
  metadata?: {
    title?: string;
    category?: string;
    description?: string;
    when?: string;
  };
}

export class CommandRegistry {
  private commands: Map<string, CommandDefinition> = new Map();
  private eventBus: EventBus;

  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
  }

  /**
   * 注册命令
   */
  registerCommand(
    id: string, 
    handler: CommandHandler,
    metadata?: CommandDefinition['metadata']
  ): vscode.Disposable {
    // 检查命令是否已存在
    if (this.commands.has(id)) {
      throw new Error(`Command ${id} is already registered`);
    }

    // 包装处理器以添加错误处理和事件发布
    const wrappedHandler = async (...args: any[]) => {
      try {
        // 发布命令开始事件
        await this.eventBus.emit({
          type: 'command.started',
          source: 'CommandRegistry',
          commandId: id,
          args,
        });

        const result = await handler(...args);

        // 发布命令完成事件
        await this.eventBus.emit({
          type: 'command.completed',
          source: 'CommandRegistry',
          commandId: id,
          result,
        });

        return result;
      } catch (error) {
        // 发布命令错误事件
        await this.eventBus.emit({
          type: 'command.failed',
          source: 'CommandRegistry',
          commandId: id,
          error: (error as Error).message,
        });

        // 显示错误消息
        vscode.window.showErrorMessage(`Command ${id} failed: ${(error as Error).message}`);
        throw error;
      }
    };

    // 注册到VS Code
    const disposable = vscode.commands.registerCommand(id, wrappedHandler);

    // 保存命令定义
    const commandDef: CommandDefinition = {
      id,
      handler,
      disposable,
      metadata,
    };

    this.commands.set(id, commandDef);

    // 发布命令注册事件
    this.eventBus.emit({
      type: 'command.registered',
      source: 'CommandRegistry',
      commandId: id,
      metadata,
    });

    // 返回取消注册函数
    return {
      dispose: () => {
        this.unregisterCommand(id);
      }
    };
  }

  /**
   * 取消注册命令
   */
  unregisterCommand(id: string): void {
    const commandDef = this.commands.get(id);
    if (!commandDef) {
      return;
    }

    // 销毁VS Code注册
    commandDef.disposable.dispose();

    // 从映射中移除
    this.commands.delete(id);

    // 发布命令取消注册事件
    this.eventBus.emit({
      type: 'command.unregistered',
      source: 'CommandRegistry',
      commandId: id,
    });
  }

  /**
   * 执行命令
   */
  async executeCommand<T = any>(command: string, ...args: any[]): Promise<T> {
    try {
      const result = await vscode.commands.executeCommand<T>(command, ...args);
      
      // 发布命令执行事件
      await this.eventBus.emit({
        type: 'command.executed',
        source: 'CommandRegistry',
        commandId: command,
        args,
        result,
      });

      return result;
    } catch (error) {
      // 发布命令执行失败事件
      await this.eventBus.emit({
        type: 'command.execution_failed',
        source: 'CommandRegistry',
        commandId: command,
        error: (error as Error).message,
      });

      throw error;
    }
  }

  /**
   * 获取已注册的命令
   */
  getRegisteredCommands(): string[] {
    return Array.from(this.commands.keys());
  }

  /**
   * 获取命令定义
   */
  getCommandDefinition(id: string): CommandDefinition | undefined {
    return this.commands.get(id);
  }

  /**
   * 检查命令是否已注册
   */
  isCommandRegistered(id: string): boolean {
    return this.commands.has(id);
  }

  /**
   * 批量注册命令
   */
  registerCommands(commands: Array<{
    id: string;
    handler: CommandHandler;
    metadata?: CommandDefinition['metadata'];
  }>): vscode.Disposable[] {
    const disposables: vscode.Disposable[] = [];

    for (const command of commands) {
      try {
        const disposable = this.registerCommand(command.id, command.handler, command.metadata);
        disposables.push(disposable);
      } catch (error) {
        // 如果某个命令注册失败，清理已注册的命令
        disposables.forEach(d => d.dispose());
        throw error;
      }
    }

    return disposables;
  }

  /**
   * 获取命令统计信息
   */
  getStats(): {
    totalCommands: number;
    commandsByCategory: Record<string, number>;
  } {
    const stats = {
      totalCommands: this.commands.size,
      commandsByCategory: {} as Record<string, number>,
    };

    for (const [, commandDef] of this.commands) {
      const category = commandDef.metadata?.category || 'uncategorized';
      stats.commandsByCategory[category] = (stats.commandsByCategory[category] || 0) + 1;
    }

    return stats;
  }

  /**
   * 销毁所有命令
   */
  dispose(): void {
    for (const [id] of this.commands) {
      this.unregisterCommand(id);
    }
    this.commands.clear();
  }
}
