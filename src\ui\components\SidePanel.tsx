/**
 * Side Panel - 侧边栏组件
 * 
 * 显示工具面板、上下文信息、RAG搜索结果、系统状态
 */

import React, { useState, useEffect } from 'react';
import { ToolCall } from '../../types';
import './SidePanel.css';

export interface SidePanelProps {
  width: number;
  systemStats?: {
    tokenUsage: number;
    maxTokens: number;
    responseTime: number;
    ragStats: any;
  };
  activeToolCall?: ToolCall | null;
  onToolSelect?: (toolName: string) => void;
}

export const SidePanel: React.FC<SidePanelProps> = ({
  width,
  systemStats,
  activeToolCall,
  onToolSelect
}) => {
  const [activeTab, setActiveTab] = useState<'tools' | 'context' | 'rag' | 'stats'>('tools');
  const [ragResults, setRagResults] = useState<any[]>([]);
  const [contextInfo, setContextInfo] = useState<any>(null);

  // 可用工具列表
  const availableTools = [
    {
      name: 'refactor',
      title: '代码重构',
      icon: '🔧',
      description: '重构和优化代码结构',
      shortcut: 'Ctrl+R'
    },
    {
      name: 'generateTests',
      title: '生成测试',
      icon: '🧪',
      description: '为代码生成单元测试',
      shortcut: 'Ctrl+T'
    },
    {
      name: 'explainCode',
      title: '代码解释',
      icon: '📖',
      description: '解释代码功能和逻辑',
      shortcut: 'Ctrl+E'
    },
    {
      name: 'executeTerminal',
      title: '终端执行',
      icon: '💻',
      description: '执行终端命令',
      shortcut: 'Ctrl+`'
    },
    {
      name: 'ragQuery',
      title: 'RAG搜索',
      icon: '🔍',
      description: '智能代码和知识库搜索',
      shortcut: 'Ctrl+F'
    },
    {
      name: 'optimize',
      title: '性能优化',
      icon: '⚡',
      description: '提供性能优化建议',
      shortcut: 'Ctrl+O'
    }
  ];

  // 模拟获取上下文信息
  useEffect(() => {
    // 这里应该从实际的上下文服务获取数据
    setContextInfo({
      currentFile: 'src/components/ChatInterface.tsx',
      language: 'TypeScript',
      lineCount: 245,
      functions: ['ChatInterface', 'handleSendMessage', 'renderMessages'],
      classes: ['ChatInterface'],
      imports: ['React', 'useState', 'useEffect'],
      gitBranch: 'feature/ui-enhancement',
      lastModified: '2 minutes ago'
    });
  }, []);

  // 渲染工具面板
  const renderToolsPanel = () => (
    <div className="tools-panel">
      <div className="panel-header">
        <h3>快速工具</h3>
        <span className="tool-count">{availableTools.length} 个工具</span>
      </div>
      
      <div className="tools-grid">
        {availableTools.map(tool => (
          <div
            key={tool.name}
            className={`tool-card ${activeToolCall?.name === tool.name ? 'active' : ''}`}
            onClick={() => onToolSelect?.(tool.name)}
          >
            <div className="tool-icon">{tool.icon}</div>
            <div className="tool-info">
              <h4 className="tool-title">{tool.title}</h4>
              <p className="tool-description">{tool.description}</p>
              <span className="tool-shortcut">{tool.shortcut}</span>
            </div>
          </div>
        ))}
      </div>
      
      <div className="tool-tips">
        <h4>使用提示</h4>
        <ul>
          <li>选中代码后使用工具可获得更精确的结果</li>
          <li>使用快捷键可快速调用工具</li>
          <li>工具执行过程中可以随时取消</li>
        </ul>
      </div>
    </div>
  );

  // 渲染上下文面板
  const renderContextPanel = () => (
    <div className="context-panel">
      <div className="panel-header">
        <h3>代码上下文</h3>
        <button className="refresh-button" title="刷新上下文">
          <i className="icon icon-refresh" />
        </button>
      </div>
      
      {contextInfo && (
        <div className="context-info">
          <div className="context-section">
            <h4>当前文件</h4>
            <div className="context-item">
              <i className="icon icon-file" />
              <span>{contextInfo.currentFile}</span>
            </div>
            <div className="context-item">
              <i className="icon icon-code" />
              <span>{contextInfo.language} • {contextInfo.lineCount} 行</span>
            </div>
          </div>
          
          <div className="context-section">
            <h4>代码结构</h4>
            {contextInfo.functions.length > 0 && (
              <div className="context-list">
                <span className="list-label">函数:</span>
                {contextInfo.functions.map((func: string, index: number) => (
                  <span key={index} className="context-tag">{func}</span>
                ))}
              </div>
            )}
            {contextInfo.classes.length > 0 && (
              <div className="context-list">
                <span className="list-label">类:</span>
                {contextInfo.classes.map((cls: string, index: number) => (
                  <span key={index} className="context-tag">{cls}</span>
                ))}
              </div>
            )}
          </div>
          
          <div className="context-section">
            <h4>依赖关系</h4>
            <div className="context-list">
              <span className="list-label">导入:</span>
              {contextInfo.imports.map((imp: string, index: number) => (
                <span key={index} className="context-tag">{imp}</span>
              ))}
            </div>
          </div>
          
          <div className="context-section">
            <h4>Git 信息</h4>
            <div className="context-item">
              <i className="icon icon-git-branch" />
              <span>{contextInfo.gitBranch}</span>
            </div>
            <div className="context-item">
              <i className="icon icon-clock" />
              <span>最后修改: {contextInfo.lastModified}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  // 渲染RAG面板
  const renderRagPanel = () => (
    <div className="rag-panel">
      <div className="panel-header">
        <h3>智能搜索</h3>
        <button className="search-button" title="新建搜索">
          <i className="icon icon-plus" />
        </button>
      </div>
      
      <div className="search-input">
        <input
          type="text"
          placeholder="搜索代码或知识库..."
          className="search-field"
        />
        <button className="search-submit">
          <i className="icon icon-search" />
        </button>
      </div>
      
      {ragResults.length > 0 ? (
        <div className="search-results">
          {ragResults.map((result, index) => (
            <div key={index} className="search-result">
              <div className="result-header">
                <span className="result-type">{result.type}</span>
                <span className="result-score">{result.score}%</span>
              </div>
              <h4 className="result-title">{result.title}</h4>
              <p className="result-snippet">{result.snippet}</p>
              <div className="result-meta">
                <span className="result-file">{result.file}</span>
                <span className="result-line">第 {result.line} 行</span>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="empty-state">
          <i className="icon icon-search" />
          <p>输入关键词开始搜索</p>
          <div className="search-suggestions">
            <h5>建议搜索:</h5>
            <div className="suggestion-tags">
              <span className="suggestion-tag">React hooks</span>
              <span className="suggestion-tag">异步处理</span>
              <span className="suggestion-tag">性能优化</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  // 渲染统计面板
  const renderStatsPanel = () => (
    <div className="stats-panel">
      <div className="panel-header">
        <h3>系统状态</h3>
        <div className="status-indicator online">
          <span className="status-dot"></span>
          在线
        </div>
      </div>
      
      {systemStats && (
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-icon">🎯</div>
            <div className="stat-info">
              <span className="stat-label">Token 使用</span>
              <span className="stat-value">
                {systemStats.tokenUsage.toLocaleString()} / {systemStats.maxTokens.toLocaleString()}
              </span>
              <div className="stat-progress">
                <div 
                  className="stat-progress-fill"
                  style={{ width: `${(systemStats.tokenUsage / systemStats.maxTokens) * 100}%` }}
                />
              </div>
            </div>
          </div>
          
          <div className="stat-card">
            <div className="stat-icon">⚡</div>
            <div className="stat-info">
              <span className="stat-label">响应时间</span>
              <span className="stat-value">{systemStats.responseTime}ms</span>
            </div>
          </div>
          
          {systemStats.ragStats && (
            <>
              <div className="stat-card">
                <div className="stat-icon">📚</div>
                <div className="stat-info">
                  <span className="stat-label">代码索引</span>
                  <span className="stat-value">{systemStats.ragStats.indexStats?.totalItems || 0}</span>
                </div>
              </div>
              
              <div className="stat-card">
                <div className="stat-icon">🔍</div>
                <div className="stat-info">
                  <span className="stat-label">搜索查询</span>
                  <span className="stat-value">{systemStats.ragStats.searchStats?.totalQueries || 0}</span>
                </div>
              </div>
            </>
          )}
        </div>
      )}
      
      <div className="system-info">
        <h4>系统信息</h4>
        <div className="info-list">
          <div className="info-item">
            <span className="info-label">模型:</span>
            <span className="info-value">DeepSeek R1</span>
          </div>
          <div className="info-item">
            <span className="info-label">版本:</span>
            <span className="info-value">v0.0.1</span>
          </div>
          <div className="info-item">
            <span className="info-label">运行时间:</span>
            <span className="info-value">2小时 15分钟</span>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div 
      className="side-panel"
      style={{ width: `${width}px` }}
    >
      {/* 标签页导航 */}
      <div className="panel-tabs">
        <button
          className={`tab-button ${activeTab === 'tools' ? 'active' : ''}`}
          onClick={() => setActiveTab('tools')}
        >
          <i className="icon icon-tool" />
          工具
        </button>
        <button
          className={`tab-button ${activeTab === 'context' ? 'active' : ''}`}
          onClick={() => setActiveTab('context')}
        >
          <i className="icon icon-code" />
          上下文
        </button>
        <button
          className={`tab-button ${activeTab === 'rag' ? 'active' : ''}`}
          onClick={() => setActiveTab('rag')}
        >
          <i className="icon icon-search" />
          搜索
        </button>
        <button
          className={`tab-button ${activeTab === 'stats' ? 'active' : ''}`}
          onClick={() => setActiveTab('stats')}
        >
          <i className="icon icon-chart" />
          状态
        </button>
      </div>

      {/* 面板内容 */}
      <div className="panel-content">
        {activeTab === 'tools' && renderToolsPanel()}
        {activeTab === 'context' && renderContextPanel()}
        {activeTab === 'rag' && renderRagPanel()}
        {activeTab === 'stats' && renderStatsPanel()}
      </div>
    </div>
  );
};
