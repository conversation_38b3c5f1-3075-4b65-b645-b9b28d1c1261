/**
 * Webview Manager - Webview管理器
 * 
 * 负责创建、管理和销毁Webview面板
 */

import * as vscode from 'vscode';
import * as path from 'path';
import { EventBus } from '@/core/EventBus';
import { WebviewConfig, IWebview } from './interfaces';

class WebviewWrapper implements IWebview {
  public readonly panel: vscode.WebviewPanel;
  public readonly webview: vscode.Webview;
  private eventBus: EventBus;
  private disposables: vscode.Disposable[] = [];

  constructor(panel: vscode.WebviewPanel, eventBus: EventBus) {
    this.panel = panel;
    this.webview = panel.webview;
    this.eventBus = eventBus;

    // 监听面板销毁事件
    this.panel.onDidDispose(() => {
      this.dispose();
    }, null, this.disposables);

    // 监听可见性变化
    this.panel.onDidChangeViewState((e) => {
      this.eventBus.emit({
        type: 'webview.visibility_changed',
        source: 'WebviewManager',
        viewType: this.panel.viewType,
        visible: e.webviewPanel.visible,
        active: e.webviewPanel.active,
      });
    }, null, this.disposables);
  }

  postMessage(message: any): Thenable<boolean> {
    return this.webview.postMessage(message);
  }

  onDidReceiveMessage(listener: (message: any) => void): vscode.Disposable {
    return this.webview.onDidReceiveMessage(listener);
  }

  onDidDispose(listener: () => void): vscode.Disposable {
    return this.panel.onDidDispose(listener);
  }

  reveal(viewColumn?: vscode.ViewColumn, preserveFocus?: boolean): void {
    this.panel.reveal(viewColumn, preserveFocus);
  }

  dispose(): void {
    this.disposables.forEach(d => d.dispose());
    this.disposables = [];
    
    if (!this.panel.disposed) {
      this.panel.dispose();
    }
  }
}

export class WebviewManager {
  private webviews: Map<string, WebviewWrapper> = new Map();
  private eventBus: EventBus;
  private extensionContext: vscode.ExtensionContext;

  constructor(eventBus: EventBus, extensionContext: vscode.ExtensionContext) {
    this.eventBus = eventBus;
    this.extensionContext = extensionContext;
  }

  /**
   * 创建Webview
   */
  createWebview(config: WebviewConfig): IWebview {
    // 检查是否已存在同类型的webview
    const existingWebview = this.webviews.get(config.viewType);
    if (existingWebview && !existingWebview.panel.disposed) {
      // 如果已存在，则显示现有的
      existingWebview.reveal();
      return existingWebview;
    }

    // 创建新的webview面板
    const panel = vscode.window.createWebviewPanel(
      config.viewType,
      config.title,
      config.showOptions || vscode.ViewColumn.One,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [
          vscode.Uri.file(path.join(this.extensionContext.extensionPath, 'dist')),
          vscode.Uri.file(path.join(this.extensionContext.extensionPath, 'media')),
        ],
        ...config.options,
      }
    );

    // 设置HTML内容
    panel.webview.html = this.getWebviewContent(config.viewType);

    // 创建包装器
    const wrapper = new WebviewWrapper(panel, this.eventBus);

    // 监听销毁事件
    wrapper.onDidDispose(() => {
      this.webviews.delete(config.viewType);
      this.eventBus.emit({
        type: 'webview.disposed',
        source: 'WebviewManager',
        viewType: config.viewType,
      });
    });

    // 保存到映射
    this.webviews.set(config.viewType, wrapper);

    // 发布创建事件
    this.eventBus.emit({
      type: 'webview.created',
      source: 'WebviewManager',
      viewType: config.viewType,
      title: config.title,
    });

    return wrapper;
  }

  /**
   * 获取Webview HTML内容
   */
  private getWebviewContent(viewType: string): string {
    const scriptUri = vscode.Uri.file(
      path.join(this.extensionContext.extensionPath, 'dist', 'webview.js')
    );
    const scriptSrc = scriptUri.with({ scheme: 'vscode-resource' });

    const styleUri = vscode.Uri.file(
      path.join(this.extensionContext.extensionPath, 'media', 'webview.css')
    );
    const styleSrc = styleUri.with({ scheme: 'vscode-resource' });

    // 生成nonce用于CSP
    const nonce = this.getNonce();

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src 'unsafe-inline' ${styleSrc}; script-src 'nonce-${nonce}';">
    <title>AI Agent</title>
    <link href="${styleSrc}" rel="stylesheet">
</head>
<body>
    <div id="root"></div>
    <script nonce="${nonce}" src="${scriptSrc}"></script>
    <script nonce="${nonce}">
        window.viewType = '${viewType}';
        window.vscode = acquireVsCodeApi();
    </script>
</body>
</html>`;
  }

  /**
   * 生成随机nonce
   */
  private getNonce(): string {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 32; i++) {
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
  }

  /**
   * 获取指定类型的Webview
   */
  getWebview(viewType: string): IWebview | undefined {
    return this.webviews.get(viewType);
  }

  /**
   * 获取所有活动的Webview
   */
  getActiveWebviews(): IWebview[] {
    return Array.from(this.webviews.values()).filter(w => !w.panel.disposed);
  }

  /**
   * 关闭指定类型的Webview
   */
  closeWebview(viewType: string): void {
    const webview = this.webviews.get(viewType);
    if (webview) {
      webview.dispose();
    }
  }

  /**
   * 向所有Webview广播消息
   */
  broadcastMessage(message: any): void {
    for (const webview of this.webviews.values()) {
      if (!webview.panel.disposed) {
        webview.postMessage(message);
      }
    }
  }

  /**
   * 向指定类型的Webview发送消息
   */
  sendMessage(viewType: string, message: any): boolean {
    const webview = this.webviews.get(viewType);
    if (webview && !webview.panel.disposed) {
      webview.postMessage(message);
      return true;
    }
    return false;
  }

  /**
   * 获取Webview统计信息
   */
  getStats(): {
    totalWebviews: number;
    activeWebviews: number;
    webviewsByType: Record<string, number>;
  } {
    const activeWebviews = this.getActiveWebviews();
    const webviewsByType: Record<string, number> = {};

    for (const webview of activeWebviews) {
      const type = webview.panel.viewType;
      webviewsByType[type] = (webviewsByType[type] || 0) + 1;
    }

    return {
      totalWebviews: this.webviews.size,
      activeWebviews: activeWebviews.length,
      webviewsByType,
    };
  }

  /**
   * 销毁所有Webview
   */
  dispose(): void {
    for (const webview of this.webviews.values()) {
      webview.dispose();
    }
    this.webviews.clear();
  }
}
