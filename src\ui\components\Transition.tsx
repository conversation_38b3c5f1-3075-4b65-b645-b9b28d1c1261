/**
 * 高性能过渡动画组件
 * 
 * 提供流畅的进入/退出动画，支持多种预设动画效果
 * 优化渲染性能，确保60fps流畅动画
 */

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { animationManager, Easing, EasingFunction } from '../utils/AnimationManager';
import { useTheme } from '../theme/ThemeProvider';

export type TransitionType = 
  | 'fade'
  | 'slide-up'
  | 'slide-down'
  | 'slide-left'
  | 'slide-right'
  | 'scale'
  | 'scale-fade'
  | 'rotate'
  | 'flip'
  | 'bounce'
  | 'elastic'
  | 'zoom'
  | 'blur'
  | 'custom';

export interface TransitionConfig {
  type: TransitionType;
  duration?: number;
  delay?: number;
  easing?: EasingFunction;
  distance?: number; // 用于slide动画的距离
  scale?: { from: number; to: number }; // 用于scale动画的缩放比例
  rotation?: { from: number; to: number }; // 用于rotate动画的旋转角度
  blur?: { from: number; to: number }; // 用于blur动画的模糊程度
  custom?: {
    from: React.CSSProperties;
    to: React.CSSProperties;
  };
}

export interface TransitionProps {
  show: boolean;
  children: React.ReactNode;
  config?: TransitionConfig;
  onEnter?: () => void;
  onEntered?: () => void;
  onExit?: () => void;
  onExited?: () => void;
  className?: string;
  style?: React.CSSProperties;
  unmountOnExit?: boolean;
  appear?: boolean; // 初始渲染时是否显示动画
}

interface TransitionState {
  status: 'entering' | 'entered' | 'exiting' | 'exited';
  shouldRender: boolean;
}

// 预设动画配置
const PRESET_CONFIGS: Record<TransitionType, Partial<TransitionConfig>> = {
  fade: {
    duration: 300,
    easing: Easing.easeInOutQuad,
  },
  'slide-up': {
    duration: 300,
    easing: Easing.easeOutCubic,
    distance: 20,
  },
  'slide-down': {
    duration: 300,
    easing: Easing.easeOutCubic,
    distance: 20,
  },
  'slide-left': {
    duration: 300,
    easing: Easing.easeOutCubic,
    distance: 20,
  },
  'slide-right': {
    duration: 300,
    easing: Easing.easeOutCubic,
    distance: 20,
  },
  scale: {
    duration: 300,
    easing: Easing.easeOutBack,
    scale: { from: 0.8, to: 1 },
  },
  'scale-fade': {
    duration: 300,
    easing: Easing.easeOutBack,
    scale: { from: 0.9, to: 1 },
  },
  rotate: {
    duration: 500,
    easing: Easing.easeOutElastic,
    rotation: { from: -180, to: 0 },
  },
  flip: {
    duration: 600,
    easing: Easing.easeInOutBack,
    rotation: { from: 90, to: 0 },
  },
  bounce: {
    duration: 600,
    easing: Easing.easeOutBounce,
    scale: { from: 0.3, to: 1 },
  },
  elastic: {
    duration: 800,
    easing: Easing.easeOutElastic,
    scale: { from: 0.1, to: 1 },
  },
  zoom: {
    duration: 300,
    easing: Easing.easeInOutQuad,
    scale: { from: 1.1, to: 1 },
  },
  blur: {
    duration: 300,
    easing: Easing.easeInOutQuad,
    blur: { from: 10, to: 0 },
  },
  custom: {
    duration: 300,
    easing: Easing.easeInOutQuad,
  },
};

export const Transition: React.FC<TransitionProps> = ({
  show,
  children,
  config = { type: 'fade' },
  onEnter,
  onEntered,
  onExit,
  onExited,
  className,
  style,
  unmountOnExit = false,
  appear = true,
}) => {
  const { colors } = useTheme();
  const [state, setState] = useState<TransitionState>(() => ({
    status: show ? 'entered' : 'exited',
    shouldRender: show || !unmountOnExit,
  }));
  
  const elementRef = useRef<HTMLDivElement>(null);
  const animationIdRef = useRef<string>('');
  const isFirstRender = useRef(true);

  // 合并配置
  const mergedConfig = {
    ...PRESET_CONFIGS[config.type],
    ...config,
  };

  // 获取动画样式
  const getAnimationStyles = useCallback((progress: number, isEntering: boolean): React.CSSProperties => {
    const { type, distance = 20, scale, rotation, blur, custom } = mergedConfig;
    
    // 根据进入/退出状态调整进度
    const animationProgress = isEntering ? progress : 1 - progress;
    
    switch (type) {
      case 'fade':
        return {
          opacity: animationProgress,
        };
        
      case 'slide-up':
        return {
          opacity: animationProgress,
          transform: `translateY(${(1 - animationProgress) * distance}px)`,
        };
        
      case 'slide-down':
        return {
          opacity: animationProgress,
          transform: `translateY(${(1 - animationProgress) * -distance}px)`,
        };
        
      case 'slide-left':
        return {
          opacity: animationProgress,
          transform: `translateX(${(1 - animationProgress) * distance}px)`,
        };
        
      case 'slide-right':
        return {
          opacity: animationProgress,
          transform: `translateX(${(1 - animationProgress) * -distance}px)`,
        };
        
      case 'scale':
        if (scale) {
          const currentScale = scale.from + (scale.to - scale.from) * animationProgress;
          return {
            transform: `scale(${currentScale})`,
          };
        }
        return {};
        
      case 'scale-fade':
        if (scale) {
          const currentScale = scale.from + (scale.to - scale.from) * animationProgress;
          return {
            opacity: animationProgress,
            transform: `scale(${currentScale})`,
          };
        }
        return { opacity: animationProgress };
        
      case 'rotate':
        if (rotation) {
          const currentRotation = rotation.from + (rotation.to - rotation.from) * animationProgress;
          return {
            opacity: animationProgress,
            transform: `rotate(${currentRotation}deg)`,
          };
        }
        return { opacity: animationProgress };
        
      case 'flip':
        if (rotation) {
          const currentRotation = rotation.from + (rotation.to - rotation.from) * animationProgress;
          return {
            opacity: animationProgress,
            transform: `rotateY(${currentRotation}deg)`,
          };
        }
        return { opacity: animationProgress };
        
      case 'bounce':
      case 'elastic':
        if (scale) {
          const currentScale = scale.from + (scale.to - scale.from) * animationProgress;
          return {
            opacity: animationProgress,
            transform: `scale(${currentScale})`,
          };
        }
        return { opacity: animationProgress };
        
      case 'zoom':
        if (scale) {
          const currentScale = scale.from + (scale.to - scale.from) * animationProgress;
          return {
            opacity: animationProgress,
            transform: `scale(${currentScale})`,
          };
        }
        return { opacity: animationProgress };
        
      case 'blur':
        if (blur) {
          const currentBlur = blur.from + (blur.to - blur.from) * animationProgress;
          return {
            opacity: animationProgress,
            filter: `blur(${currentBlur}px)`,
          };
        }
        return { opacity: animationProgress };
        
      case 'custom':
        if (custom) {
          const result: React.CSSProperties = {};
          
          // 插值计算自定义样式
          Object.keys(custom.from).forEach(key => {
            const fromValue = custom.from[key as keyof React.CSSProperties];
            const toValue = custom.to[key as keyof React.CSSProperties];
            
            if (typeof fromValue === 'number' && typeof toValue === 'number') {
              (result as any)[key] = fromValue + (toValue - fromValue) * animationProgress;
            } else {
              (result as any)[key] = animationProgress > 0.5 ? toValue : fromValue;
            }
          });
          
          return result;
        }
        return {};
        
      default:
        return { opacity: animationProgress };
    }
  }, [mergedConfig]);

  // 执行进入动画
  const enter = useCallback(() => {
    if (!elementRef.current) return;
    
    setState(prev => ({ ...prev, status: 'entering', shouldRender: true }));
    onEnter?.();
    
    const animationId = `transition-enter-${Date.now()}-${Math.random()}`;
    animationIdRef.current = animationId;
    
    animationManager.animate(
      animationId,
      0,
      1,
      {
        duration: mergedConfig.duration || 300,
        delay: mergedConfig.delay || 0,
        easing: mergedConfig.easing || Easing.easeInOutQuad,
        onUpdate: (progress) => {
          if (elementRef.current) {
            const styles = getAnimationStyles(progress, true);
            Object.assign(elementRef.current.style, styles);
          }
        },
        onComplete: () => {
          setState(prev => ({ ...prev, status: 'entered' }));
          onEntered?.();
        },
      }
    );
  }, [mergedConfig, getAnimationStyles, onEnter, onEntered]);

  // 执行退出动画
  const exit = useCallback(() => {
    if (!elementRef.current) return;
    
    setState(prev => ({ ...prev, status: 'exiting' }));
    onExit?.();
    
    const animationId = `transition-exit-${Date.now()}-${Math.random()}`;
    animationIdRef.current = animationId;
    
    animationManager.animate(
      animationId,
      0,
      1,
      {
        duration: mergedConfig.duration || 300,
        delay: mergedConfig.delay || 0,
        easing: mergedConfig.easing || Easing.easeInOutQuad,
        onUpdate: (progress) => {
          if (elementRef.current) {
            const styles = getAnimationStyles(progress, false);
            Object.assign(elementRef.current.style, styles);
          }
        },
        onComplete: () => {
          setState(prev => ({
            status: 'exited',
            shouldRender: !unmountOnExit,
          }));
          onExited?.();
        },
      }
    );
  }, [mergedConfig, getAnimationStyles, onExit, onExited, unmountOnExit]);

  // 处理显示状态变化
  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      
      if (show && appear) {
        // 初始渲染时显示进入动画
        setTimeout(enter, 0);
      } else if (show) {
        // 直接显示，不播放动画
        setState({ status: 'entered', shouldRender: true });
      }
      return;
    }
    
    // 取消当前动画
    if (animationIdRef.current) {
      animationManager.cancel(animationIdRef.current);
    }
    
    if (show) {
      enter();
    } else {
      exit();
    }
  }, [show, appear, enter, exit]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (animationIdRef.current) {
        animationManager.cancel(animationIdRef.current);
      }
    };
  }, []);

  // 如果不应该渲染，返回null
  if (!state.shouldRender) {
    return null;
  }

  const containerStyles: React.CSSProperties = {
    ...style,
    // 初始样式
    ...getAnimationStyles(state.status === 'entered' ? 1 : 0, true),
  };

  return (
    <div
      ref={elementRef}
      className={className}
      style={containerStyles}
      data-transition-status={state.status}
    >
      {children}
    </div>
  );
};

// 过渡组件组
export interface TransitionGroupProps {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

export const TransitionGroup: React.FC<TransitionGroupProps> = ({
  children,
  className,
  style,
}) => {
  return (
    <div className={className} style={style}>
      {children}
    </div>
  );
};

// 高阶组件：为任意组件添加过渡效果
export const withTransition = <P extends object>(
  Component: React.ComponentType<P>,
  defaultConfig?: TransitionConfig
) => {
  return React.forwardRef<any, P & { 
    show?: boolean; 
    transitionConfig?: TransitionConfig;
    onTransitionComplete?: () => void;
  }>((props, ref) => {
    const { show = true, transitionConfig, onTransitionComplete, ...componentProps } = props;
    
    return (
      <Transition
        show={show}
        config={transitionConfig || defaultConfig}
        onEntered={onTransitionComplete}
        onExited={onTransitionComplete}
      >
        <Component {...(componentProps as P)} ref={ref} />
      </Transition>
    );
  });
};
