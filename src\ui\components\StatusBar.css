/* Status Bar Styles */
.status-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 24px;
  background: var(--vscode-statusBar-background);
  color: var(--vscode-statusBar-foreground);
  border-top: 1px solid var(--vscode-statusBar-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  font-size: 12px;
  z-index: 1000;
}

.status-left,
.status-center,
.status-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: default;
}

.status-item.connection-status {
  font-weight: 500;
}

.status-item.loading-status {
  color: var(--vscode-statusBar-prominentForeground);
}

.status-item.token-usage {
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.status-item.token-usage:hover {
  background: var(--vscode-statusBar-hoverBackground);
}

.loading-spinner {
  width: 12px;
  height: 12px;
  border: 1px solid var(--vscode-progressBar-background);
  border-top: 1px solid var(--vscode-statusBar-foreground);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.token-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.token-text {
  font-size: 11px;
  line-height: 1;
}

.token-bar {
  width: 60px;
  height: 2px;
  background: var(--vscode-progressBar-background);
  border-radius: 1px;
  overflow: hidden;
}

.token-fill {
  height: 100%;
  transition: width 0.3s ease, background-color 0.3s ease;
}

.settings-button {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 2px;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.settings-button:hover {
  background: var(--vscode-statusBar-hoverBackground);
}

.status-details {
  position: fixed;
  bottom: 32px;
  right: 12px;
  width: 300px;
  background: var(--vscode-editorHoverWidget-background);
  border: 1px solid var(--vscode-editorHoverWidget-border);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 2000;
  animation: slideUp 0.2s ease-out;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--vscode-editorHoverWidget-border);
}

.details-header h4 {
  margin: 0;
  font-size: 13px;
  color: var(--vscode-editorHoverWidget-foreground);
}

.close-button {
  background: none;
  border: none;
  color: var(--vscode-icon-foreground);
  cursor: pointer;
  padding: 4px;
  border-radius: 3px;
}

.close-button:hover {
  background: var(--vscode-toolbar-hoverBackground);
}

.details-content {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 16px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section h5 {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: var(--vscode-editorHoverWidget-foreground);
  font-weight: 600;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-size: 11px;
}

.detail-label {
  color: var(--vscode-descriptionForeground);
}

.detail-value {
  color: var(--vscode-editorHoverWidget-foreground);
  font-weight: 500;
}

.details-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 1999;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .status-bar {
    padding: 0 8px;
    font-size: 11px;
  }
  
  .status-left,
  .status-center,
  .status-right {
    gap: 8px;
  }
  
  .token-bar {
    width: 40px;
  }
  
  .status-details {
    width: calc(100vw - 24px);
    right: 12px;
    left: 12px;
  }
}

/* 主题适配 */
[data-theme="dark"] .status-details {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

[data-theme="light"] .status-details {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
