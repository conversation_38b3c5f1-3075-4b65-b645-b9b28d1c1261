/**
 * 缓存管理器
 * 
 * 提供智能缓存策略，优化对话历史和上下文窗口管理
 * 目标：查询响应时间<500ms
 */

export interface CacheConfig {
  maxSize: number; // 最大缓存大小（MB）
  maxAge: number; // 最大缓存时间（毫秒）
  compressionEnabled: boolean; // 是否启用压缩
  persistToDisk: boolean; // 是否持久化到磁盘
  cleanupInterval: number; // 清理间隔（毫秒）
}

export interface CacheEntry<T = any> {
  key: string;
  value: T;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
  size: number; // 字节大小
  compressed: boolean;
  ttl?: number; // 生存时间
}

export interface CacheStats {
  totalEntries: number;
  totalSize: number; // 字节
  hitRate: number;
  missRate: number;
  totalHits: number;
  totalMisses: number;
  averageAccessTime: number;
}

export class CacheManager {
  private cache: Map<string, CacheEntry> = new Map();
  private config: CacheConfig;
  private stats: CacheStats = {
    totalEntries: 0,
    totalSize: 0,
    hitRate: 0,
    missRate: 0,
    totalHits: 0,
    totalMisses: 0,
    averageAccessTime: 0,
  };
  private cleanupTimer: NodeJS.Timeout | null = null;
  private accessTimes: number[] = [];

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxSize: 50 * 1024 * 1024, // 50MB
      maxAge: 24 * 60 * 60 * 1000, // 24小时
      compressionEnabled: true,
      persistToDisk: true,
      cleanupInterval: 5 * 60 * 1000, // 5分钟
      ...config,
    };

    this.startCleanupTimer();
    this.loadFromDisk();
  }

  /**
   * 设置缓存项
   */
  public async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    const startTime = performance.now();

    try {
      // 序列化值
      const serialized = JSON.stringify(value);
      let finalValue = serialized;
      let compressed = false;

      // 压缩大数据
      if (this.config.compressionEnabled && serialized.length > 1024) {
        finalValue = await this.compress(serialized);
        compressed = true;
      }

      const size = new Blob([finalValue]).size;
      const now = Date.now();

      const entry: CacheEntry<string> = {
        key,
        value: finalValue,
        timestamp: now,
        accessCount: 0,
        lastAccessed: now,
        size,
        compressed,
        ttl,
      };

      // 检查缓存大小限制
      await this.ensureCapacity(size);

      // 添加到缓存
      this.cache.set(key, entry);
      this.updateStats();

      // 持久化到磁盘
      if (this.config.persistToDisk) {
        await this.saveToDisk(key, entry);
      }

      const accessTime = performance.now() - startTime;
      this.recordAccessTime(accessTime);
    } catch (error) {
      console.error('Cache set error:', error);
    }
  }

  /**
   * 获取缓存项
   */
  public async get<T>(key: string): Promise<T | null> {
    const startTime = performance.now();

    try {
      const entry = this.cache.get(key);
      
      if (!entry) {
        this.stats.totalMisses++;
        this.updateHitRate();
        return null;
      }

      // 检查过期
      if (this.isExpired(entry)) {
        this.cache.delete(key);
        this.stats.totalMisses++;
        this.updateHitRate();
        return null;
      }

      // 更新访问信息
      entry.accessCount++;
      entry.lastAccessed = Date.now();

      // 解压缩和反序列化
      let value = entry.value;
      if (entry.compressed) {
        value = await this.decompress(value);
      }

      const result = JSON.parse(value) as T;
      
      this.stats.totalHits++;
      this.updateHitRate();

      const accessTime = performance.now() - startTime;
      this.recordAccessTime(accessTime);

      return result;
    } catch (error) {
      console.error('Cache get error:', error);
      this.stats.totalMisses++;
      this.updateHitRate();
      return null;
    }
  }

  /**
   * 删除缓存项
   */
  public delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.updateStats();
      if (this.config.persistToDisk) {
        this.removeFromDisk(key);
      }
    }
    return deleted;
  }

  /**
   * 清空缓存
   */
  public clear(): void {
    this.cache.clear();
    this.updateStats();
    if (this.config.persistToDisk) {
      this.clearDisk();
    }
  }

  /**
   * 检查是否存在
   */
  public has(key: string): boolean {
    const entry = this.cache.get(key);
    return entry ? !this.isExpired(entry) : false;
  }

  /**
   * 获取缓存统计
   */
  public getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * 获取缓存大小（字节）
   */
  public getSize(): number {
    return this.stats.totalSize;
  }

  /**
   * 获取缓存项数量
   */
  public getCount(): number {
    return this.cache.size;
  }

  /**
   * 压缩数据
   */
  private async compress(data: string): Promise<string> {
    try {
      // 使用CompressionStream API（如果可用）
      if (typeof CompressionStream !== 'undefined') {
        const stream = new CompressionStream('gzip');
        const writer = stream.writable.getWriter();
        const reader = stream.readable.getReader();
        
        writer.write(new TextEncoder().encode(data));
        writer.close();
        
        const chunks: Uint8Array[] = [];
        let done = false;
        
        while (!done) {
          const { value, done: readerDone } = await reader.read();
          done = readerDone;
          if (value) chunks.push(value);
        }
        
        const compressed = new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0));
        let offset = 0;
        for (const chunk of chunks) {
          compressed.set(chunk, offset);
          offset += chunk.length;
        }
        
        return btoa(String.fromCharCode(...compressed));
      }
      
      // 降级：简单的字符串压缩
      return this.simpleCompress(data);
    } catch (error) {
      console.warn('Compression failed, using original data:', error);
      return data;
    }
  }

  /**
   * 解压缩数据
   */
  private async decompress(data: string): Promise<string> {
    try {
      // 使用DecompressionStream API（如果可用）
      if (typeof DecompressionStream !== 'undefined') {
        const compressed = Uint8Array.from(atob(data), c => c.charCodeAt(0));
        const stream = new DecompressionStream('gzip');
        const writer = stream.writable.getWriter();
        const reader = stream.readable.getReader();
        
        writer.write(compressed);
        writer.close();
        
        const chunks: Uint8Array[] = [];
        let done = false;
        
        while (!done) {
          const { value, done: readerDone } = await reader.read();
          done = readerDone;
          if (value) chunks.push(value);
        }
        
        const decompressed = new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0));
        let offset = 0;
        for (const chunk of chunks) {
          decompressed.set(chunk, offset);
          offset += chunk.length;
        }
        
        return new TextDecoder().decode(decompressed);
      }
      
      // 降级：简单的字符串解压缩
      return this.simpleDecompress(data);
    } catch (error) {
      console.warn('Decompression failed, using original data:', error);
      return data;
    }
  }

  /**
   * 简单压缩（降级方案）
   */
  private simpleCompress(data: string): string {
    // 简单的RLE压缩
    return data.replace(/(.)\1+/g, (match, char) => {
      return match.length > 3 ? `${char}${match.length}` : match;
    });
  }

  /**
   * 简单解压缩（降级方案）
   */
  private simpleDecompress(data: string): string {
    // 简单的RLE解压缩
    return data.replace(/(.)\d+/g, (match, char) => {
      const count = parseInt(match.slice(1));
      return char.repeat(count);
    });
  }

  /**
   * 检查是否过期
   */
  private isExpired(entry: CacheEntry): boolean {
    const now = Date.now();
    
    // 检查TTL
    if (entry.ttl && now - entry.timestamp > entry.ttl) {
      return true;
    }
    
    // 检查最大年龄
    if (now - entry.timestamp > this.config.maxAge) {
      return true;
    }
    
    return false;
  }

  /**
   * 确保缓存容量
   */
  private async ensureCapacity(newEntrySize: number): Promise<void> {
    const currentSize = this.stats.totalSize;
    const maxSize = this.config.maxSize;
    
    if (currentSize + newEntrySize <= maxSize) {
      return;
    }
    
    // 需要清理空间
    const targetSize = maxSize * 0.8; // 清理到80%容量
    const needToFree = currentSize + newEntrySize - targetSize;
    
    await this.evictEntries(needToFree);
  }

  /**
   * 驱逐缓存项（LRU策略）
   */
  private async evictEntries(bytesToFree: number): Promise<void> {
    const entries = Array.from(this.cache.entries());
    
    // 按最后访问时间排序（LRU）
    entries.sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);
    
    let freedBytes = 0;
    for (const [key, entry] of entries) {
      if (freedBytes >= bytesToFree) break;
      
      this.cache.delete(key);
      freedBytes += entry.size;
      
      if (this.config.persistToDisk) {
        await this.removeFromDisk(key);
      }
    }
    
    this.updateStats();
  }

  /**
   * 更新统计信息
   */
  private updateStats(): void {
    this.stats.totalEntries = this.cache.size;
    this.stats.totalSize = Array.from(this.cache.values())
      .reduce((total, entry) => total + entry.size, 0);
  }

  /**
   * 更新命中率
   */
  private updateHitRate(): void {
    const total = this.stats.totalHits + this.stats.totalMisses;
    this.stats.hitRate = total > 0 ? this.stats.totalHits / total : 0;
    this.stats.missRate = 1 - this.stats.hitRate;
  }

  /**
   * 记录访问时间
   */
  private recordAccessTime(time: number): void {
    this.accessTimes.push(time);
    
    // 保留最近100次访问时间
    if (this.accessTimes.length > 100) {
      this.accessTimes.shift();
    }
    
    // 计算平均访问时间
    this.stats.averageAccessTime = this.accessTimes.reduce((sum, t) => sum + t, 0) / this.accessTimes.length;
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * 清理过期项
   */
  private cleanup(): void {
    const expiredKeys: string[] = [];
    
    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        expiredKeys.push(key);
      }
    }
    
    for (const key of expiredKeys) {
      this.delete(key);
    }
  }

  /**
   * 从磁盘加载
   */
  private async loadFromDisk(): Promise<void> {
    if (!this.config.persistToDisk || typeof localStorage === 'undefined') {
      return;
    }
    
    try {
      const cacheData = localStorage.getItem('ai-agent-cache');
      if (cacheData) {
        const entries = JSON.parse(cacheData) as Array<[string, CacheEntry]>;
        for (const [key, entry] of entries) {
          if (!this.isExpired(entry)) {
            this.cache.set(key, entry);
          }
        }
        this.updateStats();
      }
    } catch (error) {
      console.warn('Failed to load cache from disk:', error);
    }
  }

  /**
   * 保存到磁盘
   */
  private async saveToDisk(key: string, entry: CacheEntry): Promise<void> {
    // 这里可以实现更复杂的持久化逻辑
    // 为了演示，我们使用localStorage
  }

  /**
   * 从磁盘移除
   */
  private async removeFromDisk(key: string): Promise<void> {
    // 实现磁盘移除逻辑
  }

  /**
   * 清空磁盘缓存
   */
  private clearDisk(): void {
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem('ai-agent-cache');
    }
  }

  /**
   * 销毁缓存管理器
   */
  public destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    
    // 保存到磁盘
    if (this.config.persistToDisk && typeof localStorage !== 'undefined') {
      try {
        const entries = Array.from(this.cache.entries());
        localStorage.setItem('ai-agent-cache', JSON.stringify(entries));
      } catch (error) {
        console.warn('Failed to save cache to disk:', error);
      }
    }
    
    this.clear();
  }
}

// 全局缓存管理器实例
export const cacheManager = new CacheManager();
