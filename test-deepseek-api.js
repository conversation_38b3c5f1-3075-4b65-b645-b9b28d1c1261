/**
 * 测试DeepSeek API连接
 */

const OpenAI = require('openai');

async function testDeepSeekAPI() {
  const client = new OpenAI({
    baseURL: 'https://api.deepseek.com',
    apiKey: '***********************************'
  });

  try {
    console.log('Testing DeepSeek API connection...');
    
    const response = await client.chat.completions.create({
      model: 'deepseek-reasoner',
      messages: [
        { role: 'user', content: 'Hello, can you respond with just "API test successful"?' }
      ],
      max_tokens: 50
    });

    console.log('✅ API test successful!');
    console.log('Response:', response.choices[0].message.content);
    console.log('Model:', response.model);
    console.log('Usage:', response.usage);
    
  } catch (error) {
    console.error('❌ API test failed:');
    console.error('Error:', error.message);
    console.error('Status:', error.status);
    console.error('Details:', error.error);
  }
}

testDeepSeekAPI();
