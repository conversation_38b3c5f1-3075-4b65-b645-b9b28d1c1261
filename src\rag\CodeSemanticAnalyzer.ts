/**
 * Code Semantic Analyzer - 代码语义分析器
 * 
 * 实现AST级别的代码分析和语义提取，支持TypeScript/JavaScript代码的深度理解
 */

import * as ts from 'typescript';
import * as fs from 'fs';
import * as path from 'path';
import { EventBus } from '@/core/EventBus';

export interface SymbolInfo {
  name: string;
  kind: ts.SyntaxKind;
  type: string;
  location: {
    file: string;
    line: number;
    column: number;
    endLine: number;
    endColumn: number;
  };
  documentation?: string;
  modifiers: string[];
  parameters?: ParameterInfo[];
  returnType?: string;
  references: ReferenceInfo[];
}

export interface ParameterInfo {
  name: string;
  type: string;
  optional: boolean;
  defaultValue?: string;
}

export interface ReferenceInfo {
  file: string;
  line: number;
  column: number;
  kind: 'definition' | 'reference' | 'write';
}

export interface DependencyGraph {
  nodes: DependencyNode[];
  edges: DependencyEdge[];
  clusters: DependencyCluster[];
}

export interface DependencyNode {
  id: string;
  name: string;
  type: 'file' | 'class' | 'function' | 'variable' | 'interface';
  file: string;
  exports: string[];
  imports: ImportInfo[];
}

export interface DependencyEdge {
  from: string;
  to: string;
  type: 'import' | 'call' | 'inheritance' | 'composition';
  weight: number;
}

export interface DependencyCluster {
  id: string;
  nodes: string[];
  cohesion: number;
  coupling: number;
}

export interface ImportInfo {
  module: string;
  imports: string[];
  isDefault: boolean;
  isNamespace: boolean;
}

export interface CodeMetrics {
  complexity: number;
  maintainability: number;
  testability: number;
  lines: number;
  functions: number;
  classes: number;
  interfaces: number;
  dependencies: number;
}

export interface CodeSemanticInfo {
  filePath: string;
  ast: ts.SourceFile;
  symbols: SymbolInfo[];
  dependencies: DependencyGraph;
  semanticEmbeddings: number[];
  codeMetrics: CodeMetrics;
  lastAnalyzed: Date;
  version: string;
}

export class CodeSemanticAnalyzer {
  private eventBus: EventBus;
  private program: ts.Program | null = null;
  private typeChecker: ts.TypeChecker | null = null;
  private compilerOptions: ts.CompilerOptions;

  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
    this.compilerOptions = {
      target: ts.ScriptTarget.ES2020,
      module: ts.ModuleKind.CommonJS,
      allowJs: true,
      checkJs: false,
      declaration: false,
      strict: false,
      esModuleInterop: true,
      skipLibCheck: true,
      forceConsistentCasingInFileNames: true,
    };
  }

  /**
   * 初始化TypeScript程序
   */
  async initializeProgram(rootFiles: string[]): Promise<void> {
    try {
      this.program = ts.createProgram(rootFiles, this.compilerOptions);
      this.typeChecker = this.program.getTypeChecker();
      
      this.eventBus.emit({
        type: 'semantic_analyzer.program_initialized',
        source: 'CodeSemanticAnalyzer',
        fileCount: rootFiles.length
      });
    } catch (error) {
      this.eventBus.emit({
        type: 'semantic_analyzer.initialization_failed',
        source: 'CodeSemanticAnalyzer',
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * 分析单个文件的语义信息
   */
  async analyzeFile(filePath: string): Promise<CodeSemanticInfo> {
    if (!this.program || !this.typeChecker) {
      throw new Error('TypeScript program not initialized');
    }

    const sourceFile = this.program.getSourceFile(filePath);
    if (!sourceFile) {
      throw new Error(`Source file not found: ${filePath}`);
    }

    const symbols = await this.extractSymbols(sourceFile);
    const dependencies = await this.buildFileDependencyGraph(sourceFile);
    const codeMetrics = await this.calculateCodeMetrics(sourceFile);
    const semanticEmbeddings = await this.generateSemanticEmbeddings(sourceFile);

    const semanticInfo: CodeSemanticInfo = {
      filePath,
      ast: sourceFile,
      symbols,
      dependencies,
      semanticEmbeddings,
      codeMetrics,
      lastAnalyzed: new Date(),
      version: this.getFileVersion(filePath)
    };

    this.eventBus.emit({
      type: 'semantic_analyzer.file_analyzed',
      source: 'CodeSemanticAnalyzer',
      filePath,
      symbolCount: symbols.length,
      complexity: codeMetrics.complexity
    });

    return semanticInfo;
  }

  /**
   * 提取文件中的符号信息
   */
  async extractSymbols(sourceFile: ts.SourceFile): Promise<SymbolInfo[]> {
    const symbols: SymbolInfo[] = [];
    
    const visit = (node: ts.Node) => {
      if (this.isSymbolNode(node)) {
        const symbol = this.extractSymbolInfo(node, sourceFile);
        if (symbol) {
          symbols.push(symbol);
        }
      }
      ts.forEachChild(node, visit);
    };

    visit(sourceFile);
    return symbols;
  }

  /**
   * 判断节点是否为符号节点
   */
  private isSymbolNode(node: ts.Node): boolean {
    return ts.isFunctionDeclaration(node) ||
           ts.isClassDeclaration(node) ||
           ts.isInterfaceDeclaration(node) ||
           ts.isVariableDeclaration(node) ||
           ts.isMethodDeclaration(node) ||
           ts.isPropertyDeclaration(node) ||
           ts.isEnumDeclaration(node) ||
           ts.isTypeAliasDeclaration(node);
  }

  /**
   * 提取符号详细信息
   */
  private extractSymbolInfo(node: ts.Node, sourceFile: ts.SourceFile): SymbolInfo | null {
    if (!this.typeChecker) return null;

    const symbol = this.typeChecker.getSymbolAtLocation(node);
    if (!symbol) return null;

    const location = this.getNodeLocation(node, sourceFile);
    const type = this.typeChecker.typeToString(this.typeChecker.getTypeOfSymbolAtLocation(symbol, node));
    
    const symbolInfo: SymbolInfo = {
      name: symbol.getName(),
      kind: node.kind,
      type,
      location,
      documentation: this.getDocumentation(symbol),
      modifiers: this.getModifiers(node),
      references: []
    };

    // 为函数添加参数信息
    if (ts.isFunctionDeclaration(node) || ts.isMethodDeclaration(node)) {
      symbolInfo.parameters = this.extractParameters(node);
      symbolInfo.returnType = this.getReturnType(node);
    }

    return symbolInfo;
  }

  /**
   * 获取节点位置信息
   */
  private getNodeLocation(node: ts.Node, sourceFile: ts.SourceFile) {
    const start = sourceFile.getLineAndCharacterOfPosition(node.getStart());
    const end = sourceFile.getLineAndCharacterOfPosition(node.getEnd());
    
    return {
      file: sourceFile.fileName,
      line: start.line + 1,
      column: start.character + 1,
      endLine: end.line + 1,
      endColumn: end.character + 1
    };
  }

  /**
   * 获取符号文档
   */
  private getDocumentation(symbol: ts.Symbol): string | undefined {
    const docs = symbol.getDocumentationComment(this.typeChecker!);
    return docs.map(doc => doc.text).join('\n') || undefined;
  }

  /**
   * 获取节点修饰符
   */
  private getModifiers(node: ts.Node): string[] {
    const modifiers: string[] = [];
    
    if (ts.canHaveModifiers(node)) {
      const nodeModifiers = ts.getModifiers(node);
      if (nodeModifiers) {
        nodeModifiers.forEach(modifier => {
          modifiers.push(ts.SyntaxKind[modifier.kind]);
        });
      }
    }
    
    return modifiers;
  }

  /**
   * 提取函数参数信息
   */
  private extractParameters(node: ts.FunctionDeclaration | ts.MethodDeclaration): ParameterInfo[] {
    return node.parameters.map(param => ({
      name: param.name.getText(),
      type: param.type ? param.type.getText() : 'any',
      optional: !!param.questionToken,
      defaultValue: param.initializer ? param.initializer.getText() : undefined
    }));
  }

  /**
   * 获取函数返回类型
   */
  private getReturnType(node: ts.FunctionDeclaration | ts.MethodDeclaration): string {
    return node.type ? node.type.getText() : 'void';
  }

  /**
   * 构建文件依赖关系图
   */
  async buildFileDependencyGraph(sourceFile: ts.SourceFile): Promise<DependencyGraph> {
    const nodes: DependencyNode[] = [];
    const edges: DependencyEdge[] = [];
    
    // 分析import语句
    const imports = this.extractImports(sourceFile);
    const exports = this.extractExports(sourceFile);
    
    const fileNode: DependencyNode = {
      id: sourceFile.fileName,
      name: path.basename(sourceFile.fileName),
      type: 'file',
      file: sourceFile.fileName,
      exports,
      imports
    };
    
    nodes.push(fileNode);
    
    // 构建依赖边
    imports.forEach(imp => {
      edges.push({
        from: sourceFile.fileName,
        to: imp.module,
        type: 'import',
        weight: imp.imports.length
      });
    });

    return {
      nodes,
      edges,
      clusters: []
    };
  }

  /**
   * 提取import语句
   */
  private extractImports(sourceFile: ts.SourceFile): ImportInfo[] {
    const imports: ImportInfo[] = [];
    
    sourceFile.statements.forEach(statement => {
      if (ts.isImportDeclaration(statement)) {
        const moduleSpecifier = statement.moduleSpecifier;
        if (ts.isStringLiteral(moduleSpecifier)) {
          const importClause = statement.importClause;
          const importInfo: ImportInfo = {
            module: moduleSpecifier.text,
            imports: [],
            isDefault: false,
            isNamespace: false
          };
          
          if (importClause) {
            if (importClause.name) {
              importInfo.isDefault = true;
              importInfo.imports.push(importClause.name.text);
            }
            
            if (importClause.namedBindings) {
              if (ts.isNamespaceImport(importClause.namedBindings)) {
                importInfo.isNamespace = true;
                importInfo.imports.push(importClause.namedBindings.name.text);
              } else if (ts.isNamedImports(importClause.namedBindings)) {
                importClause.namedBindings.elements.forEach(element => {
                  importInfo.imports.push(element.name.text);
                });
              }
            }
          }
          
          imports.push(importInfo);
        }
      }
    });
    
    return imports;
  }

  /**
   * 提取export语句
   */
  private extractExports(sourceFile: ts.SourceFile): string[] {
    const exports: string[] = [];
    
    sourceFile.statements.forEach(statement => {
      if (ts.isExportDeclaration(statement)) {
        // 处理export语句
        if (statement.exportClause && ts.isNamedExports(statement.exportClause)) {
          statement.exportClause.elements.forEach(element => {
            exports.push(element.name.text);
          });
        }
      } else if (ts.canHaveModifiers(statement)) {
        const modifiers = ts.getModifiers(statement);
        if (modifiers?.some(mod => mod.kind === ts.SyntaxKind.ExportKeyword)) {
          // 处理export修饰的声明
          if (ts.isFunctionDeclaration(statement) || ts.isClassDeclaration(statement)) {
            if (statement.name) {
              exports.push(statement.name.text);
            }
          }
        }
      }
    });
    
    return exports;
  }

  /**
   * 计算代码指标
   */
  async calculateCodeMetrics(sourceFile: ts.SourceFile): Promise<CodeMetrics> {
    let complexity = 0;
    let functions = 0;
    let classes = 0;
    let interfaces = 0;
    
    const visit = (node: ts.Node) => {
      // 计算圈复杂度
      if (this.isComplexityNode(node)) {
        complexity++;
      }
      
      // 统计各种声明
      if (ts.isFunctionDeclaration(node)) functions++;
      if (ts.isClassDeclaration(node)) classes++;
      if (ts.isInterfaceDeclaration(node)) interfaces++;
      
      ts.forEachChild(node, visit);
    };
    
    visit(sourceFile);
    
    const lines = sourceFile.getLineAndCharacterOfPosition(sourceFile.getEnd()).line + 1;
    
    return {
      complexity,
      maintainability: this.calculateMaintainability(complexity, lines),
      testability: this.calculateTestability(complexity, functions),
      lines,
      functions,
      classes,
      interfaces,
      dependencies: 0 // 将在依赖分析中计算
    };
  }

  /**
   * 判断是否为复杂度节点
   */
  private isComplexityNode(node: ts.Node): boolean {
    return ts.isIfStatement(node) ||
           ts.isWhileStatement(node) ||
           ts.isForStatement(node) ||
           ts.isForInStatement(node) ||
           ts.isForOfStatement(node) ||
           ts.isSwitchStatement(node) ||
           ts.isConditionalExpression(node) ||
           ts.isCatchClause(node);
  }

  /**
   * 计算可维护性指数
   */
  private calculateMaintainability(complexity: number, lines: number): number {
    // 简化的可维护性指数计算
    const halsteadVolume = Math.log2(lines) * lines;
    const maintainability = Math.max(0, 171 - 5.2 * Math.log(halsteadVolume) - 0.23 * complexity - 16.2 * Math.log(lines));
    return Math.round(maintainability);
  }

  /**
   * 计算可测试性指数
   */
  private calculateTestability(complexity: number, functions: number): number {
    if (functions === 0) return 100;
    const avgComplexity = complexity / functions;
    return Math.max(0, 100 - avgComplexity * 10);
  }

  /**
   * 生成语义嵌入向量（占位实现）
   */
  async generateSemanticEmbeddings(sourceFile: ts.SourceFile): Promise<number[]> {
    // TODO: 集成CodeBERT或类似模型生成真实的语义嵌入
    // 这里返回一个占位向量
    const text = sourceFile.getFullText();
    const hash = this.simpleHash(text);
    
    // 生成384维的占位向量
    const embeddings: number[] = [];
    for (let i = 0; i < 384; i++) {
      embeddings.push(Math.sin(hash + i) * 0.1);
    }
    
    return embeddings;
  }

  /**
   * 简单哈希函数
   */
  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash;
  }

  /**
   * 获取文件版本（基于修改时间）
   */
  private getFileVersion(filePath: string): string {
    try {
      const stats = fs.statSync(filePath);
      return stats.mtime.getTime().toString();
    } catch {
      return Date.now().toString();
    }
  }
}
