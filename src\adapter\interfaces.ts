/**
 * VS Code Adapter Interfaces
 * 
 * 定义VS Code适配器的接口和类型
 */

import * as vscode from 'vscode';

// 命令处理器类型
export type CommandHandler = (...args: any[]) => any;

// Webview配置
export interface WebviewConfig {
  viewType: string;
  title: string;
  showOptions?: vscode.ViewColumn | { viewColumn: vscode.ViewColumn; preserveFocus?: boolean };
  options?: vscode.WebviewPanelOptions & vscode.WebviewOptions;
}

// Webview接口
export interface IWebview {
  readonly panel: vscode.WebviewPanel;
  readonly webview: vscode.Webview;
  
  postMessage(message: any): Thenable<boolean>;
  onDidReceiveMessage(listener: (message: any) => void): vscode.Disposable;
  onDidDispose(listener: () => void): vscode.Disposable;
  reveal(viewColumn?: vscode.ViewColumn, preserveFocus?: boolean): void;
  dispose(): void;
}

// 文档变更事件
export interface DocumentChangeEvent {
  document: vscode.TextDocument;
  changes: readonly vscode.TextDocumentContentChangeEvent[];
}

// 选择变更事件
export interface SelectionChangeEvent {
  textEditor: vscode.TextEditor;
  selections: readonly vscode.Selection[];
}

// 工作区变更事件
export interface WorkspaceChangeEvent {
  added: readonly vscode.WorkspaceFolder[];
  removed: readonly vscode.WorkspaceFolder[];
}

// 事件处理器类型
export type DocumentChangeHandler = (event: DocumentChangeEvent) => void;
export type SelectionChangeHandler = (event: SelectionChangeEvent) => void;
export type WorkspaceChangeHandler = (event: WorkspaceChangeEvent) => void;

// VS Code适配器主接口
export interface IVSCodeAdapter {
  // 命令管理
  registerCommand(id: string, handler: CommandHandler): vscode.Disposable;
  executeCommand<T = any>(command: string, ...args: any[]): Thenable<T>;
  
  // UI管理
  createWebview(config: WebviewConfig): IWebview;
  showInformationMessage(message: string, ...items: string[]): Thenable<string | undefined>;
  showWarningMessage(message: string, ...items: string[]): Thenable<string | undefined>;
  showErrorMessage(message: string, ...items: string[]): Thenable<string | undefined>;
  showStatusMessage(message: string, hideAfterTimeout?: number): vscode.Disposable;
  
  // 编辑器管理
  getActiveTextEditor(): vscode.TextEditor | undefined;
  showTextDocument(document: vscode.TextDocument, options?: vscode.TextDocumentShowOptions): Thenable<vscode.TextEditor>;
  
  // 工作区管理
  getWorkspaceFolders(): readonly vscode.WorkspaceFolder[] | undefined;
  getConfiguration(section?: string, scope?: vscode.ConfigurationScope): vscode.WorkspaceConfiguration;
  
  // 文件系统
  readFile(uri: vscode.Uri): Thenable<Uint8Array>;
  writeFile(uri: vscode.Uri, content: Uint8Array): Thenable<void>;
  
  // 事件监听
  onDidChangeActiveTextEditor(handler: (editor: vscode.TextEditor | undefined) => void): vscode.Disposable;
  onDidChangeTextDocument(handler: DocumentChangeHandler): vscode.Disposable;
  onDidChangeTextEditorSelection(handler: SelectionChangeHandler): vscode.Disposable;
  onDidChangeWorkspaceFolders(handler: WorkspaceChangeHandler): vscode.Disposable;
  
  // 生命周期
  dispose(): void;
}

// 状态栏项配置
export interface StatusBarConfig {
  alignment?: vscode.StatusBarAlignment;
  priority?: number;
  text: string;
  tooltip?: string;
  command?: string;
  color?: string | vscode.ThemeColor;
  backgroundColor?: vscode.ThemeColor;
}

// 状态栏项接口
export interface IStatusBarItem {
  readonly item: vscode.StatusBarItem;
  
  update(config: Partial<StatusBarConfig>): void;
  show(): void;
  hide(): void;
  dispose(): void;
}

// 树视图数据提供者接口
export interface ITreeDataProvider<T> extends vscode.TreeDataProvider<T> {
  refresh(): void;
  getParent?(element: T): vscode.ProviderResult<T>;
}

// 快速选择项
export interface QuickPickConfig {
  items: vscode.QuickPickItem[];
  options?: vscode.QuickPickOptions;
}

// 输入框配置
export interface InputBoxConfig {
  options?: vscode.InputBoxOptions;
}

// 进度配置
export interface ProgressConfig {
  location: vscode.ProgressLocation;
  title?: string;
  cancellable?: boolean;
}

// 诊断信息
export interface DiagnosticInfo {
  uri: vscode.Uri;
  diagnostics: vscode.Diagnostic[];
}

// VS Code适配器扩展接口
export interface IVSCodeAdapterExtended extends IVSCodeAdapter {
  // 状态栏管理
  createStatusBarItem(config: StatusBarConfig): IStatusBarItem;
  
  // 快速选择和输入
  showQuickPick(config: QuickPickConfig): Thenable<vscode.QuickPickItem | undefined>;
  showInputBox(config: InputBoxConfig): Thenable<string | undefined>;
  
  // 进度显示
  withProgress<R>(
    config: ProgressConfig,
    task: (progress: vscode.Progress<{ message?: string; increment?: number }>, token: vscode.CancellationToken) => Thenable<R>
  ): Thenable<R>;
  
  // 诊断管理
  setDiagnostics(collection: vscode.DiagnosticCollection, diagnostics: DiagnosticInfo[]): void;
  
  // 树视图
  createTreeView<T>(viewId: string, options: vscode.TreeViewOptions<T>): vscode.TreeView<T>;
  
  // 终端管理
  createTerminal(options?: vscode.TerminalOptions): vscode.Terminal;
  
  // 输出通道
  createOutputChannel(name: string): vscode.OutputChannel;
}
