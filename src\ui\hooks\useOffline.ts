/**
 * 离线模式React Hook
 * 
 * 提供离线状态管理和网络状态监控
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { offlineManager, OfflineStats, NetworkStatus } from '../utils/OfflineManager';

export interface UseOfflineOptions {
  enableNotifications?: boolean;
  autoRetry?: boolean;
  retryInterval?: number;
}

export interface OfflineState extends OfflineStats {
  networkQuality: 'excellent' | 'good' | 'fair' | 'poor';
  shouldUseOfflineFirst: boolean;
}

/**
 * 离线模式Hook
 */
export const useOffline = (options: UseOfflineOptions = {}) => {
  const {
    enableNotifications = true,
    autoRetry = true,
    retryInterval = 30000, // 30秒
  } = options;

  const [state, setState] = useState<OfflineState>(() => {
    const stats = offlineManager.getStatus();
    return {
      ...stats,
      networkQuality: offlineManager.getNetworkQuality(),
      shouldUseOfflineFirst: offlineManager.shouldUseOfflineFirst(),
    };
  });

  const retryTimerRef = useRef<NodeJS.Timeout>();
  const notificationShownRef = useRef(false);

  // 更新状态
  const updateState = useCallback((stats: OfflineStats) => {
    setState({
      ...stats,
      networkQuality: offlineManager.getNetworkQuality(),
      shouldUseOfflineFirst: offlineManager.shouldUseOfflineFirst(),
    });
  }, []);

  // 显示离线通知
  const showOfflineNotification = useCallback(() => {
    if (!enableNotifications || notificationShownRef.current) return;

    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('AI编程助手', {
        body: '当前处于离线模式，部分功能可能受限',
        icon: '/icon-192x192.png',
        tag: 'offline-mode',
      });
      notificationShownRef.current = true;
    }
  }, [enableNotifications]);

  // 显示在线通知
  const showOnlineNotification = useCallback(() => {
    if (!enableNotifications) return;

    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('AI编程助手', {
        body: '网络连接已恢复，正在同步数据...',
        icon: '/icon-192x192.png',
        tag: 'online-mode',
      });
    }
    notificationShownRef.current = false;
  }, [enableNotifications]);

  // 自动重试连接
  const startAutoRetry = useCallback(() => {
    if (!autoRetry || state.isOfflineMode === false) return;

    retryTimerRef.current = setInterval(() => {
      if (navigator.onLine) {
        offlineManager.forceSync();
      }
    }, retryInterval);
  }, [autoRetry, retryInterval, state.isOfflineMode]);

  const stopAutoRetry = useCallback(() => {
    if (retryTimerRef.current) {
      clearInterval(retryTimerRef.current);
      retryTimerRef.current = undefined;
    }
  }, []);

  // 监听离线状态变化
  useEffect(() => {
    const unsubscribe = offlineManager.addStatusListener((stats) => {
      updateState(stats);
      
      // 处理状态变化
      if (stats.isOfflineMode) {
        showOfflineNotification();
        startAutoRetry();
      } else {
        showOnlineNotification();
        stopAutoRetry();
      }
    });

    return unsubscribe;
  }, [updateState, showOfflineNotification, showOnlineNotification, startAutoRetry, stopAutoRetry]);

  // 请求通知权限
  useEffect(() => {
    if (enableNotifications && 'Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, [enableNotifications]);

  // 清理定时器
  useEffect(() => {
    return () => {
      stopAutoRetry();
    };
  }, [stopAutoRetry]);

  // 添加离线操作
  const addOfflineAction = useCallback((
    type: string,
    data: any,
    priority: 'low' | 'medium' | 'high' = 'medium'
  ) => {
    return offlineManager.addOfflineAction(type, data, priority);
  }, []);

  // 强制同步
  const forceSync = useCallback(async () => {
    await offlineManager.forceSync();
  }, []);

  // 清除离线数据
  const clearOfflineData = useCallback(() => {
    offlineManager.clearOfflineData();
  }, []);

  return {
    ...state,
    addOfflineAction,
    forceSync,
    clearOfflineData,
  };
};

/**
 * 网络状态Hook
 */
export const useNetworkStatus = () => {
  const [status, setStatus] = useState<NetworkStatus>(() => {
    return offlineManager.getStatus().networkStatus;
  });

  useEffect(() => {
    const unsubscribe = offlineManager.addStatusListener((stats) => {
      setStatus(stats.networkStatus);
    });

    return unsubscribe;
  }, []);

  return status;
};

/**
 * 离线优先Hook
 */
export const useOfflineFirst = () => {
  const [shouldUseOfflineFirst, setShouldUseOfflineFirst] = useState(() => {
    return offlineManager.shouldUseOfflineFirst();
  });

  useEffect(() => {
    const unsubscribe = offlineManager.addStatusListener(() => {
      setShouldUseOfflineFirst(offlineManager.shouldUseOfflineFirst());
    });

    return unsubscribe;
  }, []);

  return shouldUseOfflineFirst;
};

/**
 * 离线操作Hook
 */
export const useOfflineActions = () => {
  const [pendingActions, setPendingActions] = useState(0);
  const [syncedActions, setSyncedActions] = useState(0);
  const [failedActions, setFailedActions] = useState(0);

  useEffect(() => {
    const unsubscribe = offlineManager.addStatusListener((stats) => {
      setPendingActions(stats.pendingActions);
      setSyncedActions(stats.syncedActions);
      setFailedActions(stats.failedActions);
    });

    return unsubscribe;
  }, []);

  const addAction = useCallback((
    type: string,
    data: any,
    priority: 'low' | 'medium' | 'high' = 'medium'
  ) => {
    return offlineManager.addOfflineAction(type, data, priority);
  }, []);

  const forceSync = useCallback(async () => {
    await offlineManager.forceSync();
  }, []);

  const clearActions = useCallback(() => {
    offlineManager.clearOfflineData();
  }, []);

  return {
    pendingActions,
    syncedActions,
    failedActions,
    addAction,
    forceSync,
    clearActions,
  };
};

/**
 * 网络质量Hook
 */
export const useNetworkQuality = () => {
  const [quality, setQuality] = useState<'excellent' | 'good' | 'fair' | 'poor'>(() => {
    return offlineManager.getNetworkQuality();
  });

  useEffect(() => {
    const unsubscribe = offlineManager.addStatusListener(() => {
      setQuality(offlineManager.getNetworkQuality());
    });

    return unsubscribe;
  }, []);

  return quality;
};

/**
 * 离线存储Hook
 */
export const useOfflineStorage = <T>(key: string, defaultValue: T) => {
  const [value, setValue] = useState<T>(() => {
    try {
      const item = localStorage.getItem(`offline-${key}`);
      return item ? JSON.parse(item) : defaultValue;
    } catch {
      return defaultValue;
    }
  });

  const setStoredValue = useCallback((newValue: T) => {
    try {
      setValue(newValue);
      localStorage.setItem(`offline-${key}`, JSON.stringify(newValue));
    } catch (error) {
      console.error('Failed to store offline data:', error);
    }
  }, [key]);

  const removeStoredValue = useCallback(() => {
    try {
      setValue(defaultValue);
      localStorage.removeItem(`offline-${key}`);
    } catch (error) {
      console.error('Failed to remove offline data:', error);
    }
  }, [key, defaultValue]);

  return [value, setStoredValue, removeStoredValue] as const;
};
