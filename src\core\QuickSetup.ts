/**
 * Quick Setup - 快速配置工具
 * 
 * 提供快速配置DeepSeek API的功能
 */

import { ConfigManager } from './ConfigManager';

export interface QuickSetupConfig {
  apiKey: string;
  provider: 'deepseek' | 'openai';
  model?: string;
  baseUrl?: string;
}

export class QuickSetup {
  constructor(private configManager: ConfigManager) {}

  /**
   * 快速设置DeepSeek配置
   */
  async setupDeepSeek(apiKey: string): Promise<void> {
    const config: QuickSetupConfig = {
      apiKey,
      provider: 'deepseek',
      model: 'deepseek-reasoner',
      baseUrl: 'https://api.deepseek.com'
    };

    await this.applyConfig(config);
  }

  /**
   * 快速设置OpenAI配置
   */
  async setupOpenAI(apiKey: string): Promise<void> {
    const config: QuickSetupConfig = {
      apiKey,
      provider: 'openai',
      model: 'gpt-4o',
      baseUrl: 'https://api.openai.com/v1'
    };

    await this.applyConfig(config);
  }

  /**
   * 应用配置
   */
  private async applyConfig(config: QuickSetupConfig): Promise<void> {
    await this.configManager.updateConfig({
      apiKey: config.apiKey,
      baseUrl: config.baseUrl!,
      model: config.model!,
      provider: config.provider as any,
      temperature: 0.2,
      maxTokens: 4096
    });
  }

  /**
   * 验证配置
   */
  async validateSetup(): Promise<{ isValid: boolean; errors: string[] }> {
    return this.configManager.validateConfig();
  }
}
