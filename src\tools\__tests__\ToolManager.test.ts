/**
 * ToolManager Tests
 */

import { ToolManager } from '../ToolManager';
import { EventBus } from '@/core/EventBus';
import {
  ITool,
  ToolDefinition,
  ToolExecutionContext,
  ToolExecutionResult,
  ValidationResult,
  ToolUsageStats,
  ToolError,
  ToolValidationError,
} from '../interfaces';

// Mock Tool Implementation
class MockTool implements ITool {
  public readonly definition: ToolDefinition = {
    name: 'mock_tool',
    description: 'A mock tool for testing',
    category: 'utility',
    parameters: {
      type: 'object',
      properties: {
        input: {
          type: 'string',
          description: 'Input parameter',
        },
        required_param: {
          type: 'string',
          description: 'Required parameter',
        },
      },
      required: ['required_param'],
    },
    permissions: ['read-files'],
  };

  private usageStats: ToolUsageStats = {
    totalExecutions: 0,
    successfulExecutions: 0,
    failedExecutions: 0,
    averageExecutionTime: 0,
  };

  private shouldFail = false;

  async execute(
    parameters: Record<string, any>,
    context: ToolExecutionContext
  ): Promise<ToolExecutionResult> {
    this.usageStats.totalExecutions++;

    if (this.shouldFail) {
      this.usageStats.failedExecutions++;
      return {
        success: false,
        error: 'Mock tool failure',
        metadata: { duration: 100 },
      };
    }

    this.usageStats.successfulExecutions++;
    return {
      success: true,
      result: { output: `Processed: ${parameters.input || 'default'}` },
      metadata: { duration: 100 },
    };
  }

  validate(parameters: Record<string, any>): ValidationResult {
    const errors: string[] = [];
    
    if (!parameters.required_param) {
      errors.push('required_param is required');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings: [],
    };
  }

  getUsageStats(): ToolUsageStats {
    return { ...this.usageStats };
  }

  setShouldFail(shouldFail: boolean) {
    this.shouldFail = shouldFail;
  }
}

describe('ToolManager', () => {
  let toolManager: ToolManager;
  let eventBus: EventBus;
  let mockTool: MockTool;

  beforeEach(() => {
    eventBus = new EventBus();
    toolManager = new ToolManager(eventBus);
    mockTool = new MockTool();
  });

  afterEach(() => {
    toolManager.dispose();
    eventBus.destroy();
  });

  describe('tool registration', () => {
    it('should register a tool successfully', () => {
      toolManager.registerTool(mockTool);
      
      const tool = toolManager.getTool('mock_tool');
      expect(tool).toBe(mockTool);
      
      const tools = toolManager.listTools();
      expect(tools).toHaveLength(1);
      expect(tools[0].name).toBe('mock_tool');
    });

    it('should throw error when registering duplicate tool', () => {
      toolManager.registerTool(mockTool);
      
      expect(() => {
        toolManager.registerTool(mockTool);
      }).toThrow('Tool mock_tool is already registered');
    });

    it('should unregister a tool', () => {
      toolManager.registerTool(mockTool);
      toolManager.unregisterTool('mock_tool');
      
      const tool = toolManager.getTool('mock_tool');
      expect(tool).toBeUndefined();
      
      const tools = toolManager.listTools();
      expect(tools).toHaveLength(0);
    });

    it('should emit events when registering/unregistering tools', async () => {
      const eventListener = jest.fn();
      eventBus.subscribe('tool.registered', eventListener);
      eventBus.subscribe('tool.unregistered', eventListener);

      toolManager.registerTool(mockTool);
      toolManager.unregisterTool('mock_tool');

      // Wait for events to be processed
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(eventListener).toHaveBeenCalledTimes(2);
    });
  });

  describe('tool execution', () => {
    beforeEach(() => {
      toolManager.registerTool(mockTool);
    });

    it('should execute tool successfully', async () => {
      const context: ToolExecutionContext = {
        workspaceRoot: '/test/workspace',
      };

      const result = await toolManager.executeTool(
        'mock_tool',
        { required_param: 'test', input: 'hello' },
        context
      );

      expect(result.success).toBe(true);
      expect(result.result).toEqual({ output: 'Processed: hello' });
      expect(result.metadata?.duration).toBeGreaterThan(0);
    });

    it('should handle tool execution failure', async () => {
      mockTool.setShouldFail(true);
      
      const context: ToolExecutionContext = {};

      const result = await toolManager.executeTool(
        'mock_tool',
        { required_param: 'test' },
        context
      );

      expect(result.success).toBe(false);
      expect(result.error).toBe('Mock tool failure');
    });

    it('should validate parameters before execution', async () => {
      const context: ToolExecutionContext = {};

      await expect(
        toolManager.executeTool('mock_tool', { input: 'test' }, context)
      ).rejects.toThrow(ToolValidationError);
    });

    it('should throw error for unknown tool', async () => {
      const context: ToolExecutionContext = {};

      await expect(
        toolManager.executeTool('unknown_tool', {}, context)
      ).rejects.toThrow('Tool unknown_tool not found');
    });

    it('should emit events during tool execution', async () => {
      const eventListener = jest.fn();
      eventBus.subscribe('tool.execution_started', eventListener);
      eventBus.subscribe('tool.execution_completed', eventListener);

      const context: ToolExecutionContext = {};

      await toolManager.executeTool(
        'mock_tool',
        { required_param: 'test' },
        context
      );

      // Wait for events to be processed
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(eventListener).toHaveBeenCalledTimes(2);
    });
  });

  describe('tool chain execution', () => {
    beforeEach(() => {
      toolManager.registerTool(mockTool);
    });

    it('should execute tool chain successfully', async () => {
      const calls = [
        { name: 'mock_tool', parameters: { required_param: 'test1', input: 'first' } },
        { name: 'mock_tool', parameters: { required_param: 'test2', input: 'second' } },
      ];

      const context: ToolExecutionContext = {};
      const results = await toolManager.executeToolChain(calls, context);

      expect(results).toHaveLength(2);
      expect(results[0].success).toBe(true);
      expect(results[1].success).toBe(true);
    });

    it('should stop chain execution on failure', async () => {
      mockTool.setShouldFail(true);
      
      const calls = [
        { name: 'mock_tool', parameters: { required_param: 'test1' } },
        { name: 'mock_tool', parameters: { required_param: 'test2' } },
      ];

      const context: ToolExecutionContext = {};
      const results = await toolManager.executeToolChain(calls, context);

      expect(results).toHaveLength(1);
      expect(results[0].success).toBe(false);
    });
  });

  describe('tool discovery', () => {
    beforeEach(() => {
      toolManager.registerTool(mockTool);
    });

    it('should search tools by name', () => {
      const results = toolManager.searchTools('mock');
      expect(results).toHaveLength(1);
      expect(results[0].name).toBe('mock_tool');
    });

    it('should search tools by description', () => {
      const results = toolManager.searchTools('testing');
      expect(results).toHaveLength(1);
      expect(results[0].name).toBe('mock_tool');
    });

    it('should return empty results for no matches', () => {
      const results = toolManager.searchTools('nonexistent');
      expect(results).toHaveLength(0);
    });

    it('should suggest tools based on context', () => {
      const context: ToolExecutionContext = {
        activeFile: 'test.js',
        selectedText: 'some code',
      };

      const suggestions = toolManager.suggestTools(context);
      // Mock tool doesn't have specific suggestions, so this tests the basic functionality
      expect(Array.isArray(suggestions)).toBe(true);
    });
  });

  describe('statistics and monitoring', () => {
    beforeEach(() => {
      toolManager.registerTool(mockTool);
    });

    it('should provide usage statistics', async () => {
      const context: ToolExecutionContext = {};
      
      await toolManager.executeTool(
        'mock_tool',
        { required_param: 'test' },
        context
      );

      const stats = toolManager.getUsageStats();
      expect(stats.mock_tool).toBeDefined();
      expect(stats.mock_tool.totalExecutions).toBe(1);
      expect(stats.mock_tool.successfulExecutions).toBe(1);
    });

    it('should provide health status', async () => {
      const health = await toolManager.getToolHealth();
      expect(health.mock_tool).toBeDefined();
      expect(health.mock_tool.status).toBe('healthy');
    });

    it('should track execution history', async () => {
      const context: ToolExecutionContext = {};
      
      await toolManager.executeTool(
        'mock_tool',
        { required_param: 'test' },
        context
      );

      const history = toolManager.getExecutionHistory();
      expect(history).toHaveLength(1);
      expect(history[0].toolName).toBe('mock_tool');
      expect(history[0].success).toBe(true);
    });

    it('should filter execution history by tool name', async () => {
      const context: ToolExecutionContext = {};
      
      await toolManager.executeTool(
        'mock_tool',
        { required_param: 'test' },
        context
      );

      const history = toolManager.getExecutionHistory('mock_tool');
      expect(history).toHaveLength(1);
      expect(history[0].toolName).toBe('mock_tool');

      const otherHistory = toolManager.getExecutionHistory('other_tool');
      expect(otherHistory).toHaveLength(0);
    });
  });

  describe('permission management', () => {
    it('should grant and revoke permissions', () => {
      expect(toolManager.hasPermission('read-files')).toBe(true);
      
      toolManager.revokePermission('read-files');
      expect(toolManager.hasPermission('read-files')).toBe(false);
      
      toolManager.grantPermission('read-files');
      expect(toolManager.hasPermission('read-files')).toBe(true);
    });

    it('should validate tool permissions during registration', () => {
      toolManager.revokePermission('read-files');
      
      expect(() => {
        toolManager.registerTool(mockTool);
      }).toThrow('Permission denied');
    });
  });

  describe('disposal', () => {
    it('should dispose cleanly', () => {
      toolManager.registerTool(mockTool);
      
      expect(() => {
        toolManager.dispose();
      }).not.toThrow();

      expect(toolManager.listTools()).toHaveLength(0);
    });
  });
});
