/* Tool Call Panel Styles */
.tool-call-panel {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 500px;
  max-width: 90vw;
  max-height: 80vh;
  background: var(--vscode-panel-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  z-index: 2000;
  overflow: hidden;
  animation: panelSlideIn 0.3s ease-out;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: var(--vscode-panel-background);
  border-top: 4px solid;
  border-bottom: 1px solid var(--vscode-panel-border);
}

.tool-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.tool-icon {
  font-size: 24px;
}

.tool-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: var(--vscode-foreground);
}

.tool-status {
  font-size: 12px;
}

.status.running {
  color: var(--vscode-progressBar-foreground);
}

.status.completed {
  color: var(--vscode-testing-iconPassed);
}

.status.error {
  color: var(--vscode-errorForeground);
}

.panel-actions {
  display: flex;
  gap: 8px;
}

.cancel-button,
.minimize-button {
  background: none;
  border: none;
  color: var(--vscode-icon-foreground);
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.cancel-button:hover,
.minimize-button:hover {
  background: var(--vscode-toolbar-hoverBackground);
}

.progress-section {
  padding: 16px 20px;
  border-bottom: 1px solid var(--vscode-panel-border);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.current-step {
  font-size: 13px;
  color: var(--vscode-foreground);
  font-weight: 500;
}

.progress-percentage {
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
  font-weight: 600;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: var(--vscode-progressBar-background);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  transition: width 0.5s ease, background-color 0.3s ease;
  border-radius: 3px;
}

.steps-section {
  padding: 16px 20px;
  border-bottom: 1px solid var(--vscode-panel-border);
  max-height: 200px;
  overflow-y: auto;
}

.steps-section h4 {
  margin: 0 0 12px 0;
  font-size: 13px;
  color: var(--vscode-foreground);
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.step-item.active {
  background: var(--vscode-list-activeSelectionBackground);
  color: var(--vscode-list-activeSelectionForeground);
}

.step-item.completed {
  opacity: 0.7;
}

.step-indicator {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
  font-size: 10px;
  flex-shrink: 0;
}

.step-item.active .step-indicator {
  background: var(--vscode-progressBar-foreground);
  color: white;
}

.step-item.completed .step-indicator {
  background: var(--vscode-testing-iconPassed);
  color: white;
}

.step-text {
  font-size: 12px;
  flex: 1;
}

.step-progress {
  width: 100%;
  height: 2px;
  background: var(--vscode-progressBar-background);
  border-radius: 1px;
  overflow: hidden;
  margin-top: 4px;
}

.step-progress-fill {
  height: 100%;
  background: var(--vscode-progressBar-foreground);
  transition: width 0.3s ease;
}

.spinner-small {
  width: 12px;
  height: 12px;
  border: 1px solid var(--vscode-progressBar-background);
  border-top: 1px solid var(--vscode-progressBar-foreground);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.logs-section {
  padding: 16px 20px;
  border-bottom: 1px solid var(--vscode-panel-border);
  max-height: 150px;
  overflow-y: auto;
}

.logs-section h4 {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: var(--vscode-foreground);
}

.logs-container {
  background: var(--vscode-terminal-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  padding: 8px;
  font-family: var(--vscode-editor-font-family);
  font-size: 11px;
  max-height: 100px;
  overflow-y: auto;
}

.log-entry {
  color: var(--vscode-terminal-foreground);
  margin-bottom: 2px;
  line-height: 1.3;
}

.log-entry.error {
  color: var(--vscode-errorForeground);
}

.parameters-section {
  padding: 16px 20px;
  border-bottom: 1px solid var(--vscode-panel-border);
}

.parameters-section h4 {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: var(--vscode-foreground);
}

.parameters-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.parameter-item {
  display: flex;
  gap: 8px;
  font-size: 11px;
}

.parameter-key {
  color: var(--vscode-descriptionForeground);
  min-width: 80px;
}

.parameter-value {
  color: var(--vscode-foreground);
  word-break: break-all;
}

.panel-footer {
  padding: 16px 20px;
}

.completion-actions,
.error-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.action-button {
  padding: 8px 16px;
  border: 1px solid var(--vscode-button-border);
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.action-button.primary {
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border-color: var(--vscode-button-background);
}

.action-button.secondary {
  background: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
  border-color: var(--vscode-button-secondaryBackground);
}

.action-button.danger {
  background: var(--vscode-errorForeground);
  color: white;
  border-color: var(--vscode-errorForeground);
}

.action-button:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

@keyframes panelSlideIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 滚动条样式 */
.steps-section::-webkit-scrollbar,
.logs-container::-webkit-scrollbar {
  width: 6px;
}

.steps-section::-webkit-scrollbar-track,
.logs-container::-webkit-scrollbar-track {
  background: var(--vscode-scrollbarSlider-background);
}

.steps-section::-webkit-scrollbar-thumb,
.logs-container::-webkit-scrollbar-thumb {
  background: var(--vscode-scrollbarSlider-hoverBackground);
  border-radius: 3px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tool-call-panel {
    width: 95vw;
    max-height: 90vh;
  }
  
  .panel-header {
    padding: 12px 16px;
  }
  
  .tool-icon {
    font-size: 20px;
  }
  
  .tool-name {
    font-size: 14px;
  }
  
  .progress-section,
  .steps-section,
  .logs-section,
  .parameters-section,
  .panel-footer {
    padding: 12px 16px;
  }
}
