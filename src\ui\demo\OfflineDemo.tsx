/**
 * 离线模式演示页面
 * 
 * 展示离线功能、网络状态监控和数据同步
 */

import React, { useState, useCallback } from 'react';
import { useTheme } from '../theme/ThemeProvider';
import { Button } from '../components/Button';
import { OfflineIndicator, NetworkStatusBadge } from '../components/OfflineIndicator';
import { 
  useOffline, 
  useNetworkStatus, 
  useOfflineActions, 
  useNetworkQuality,
  useOfflineStorage 
} from '../hooks/useOffline';

export const OfflineDemo: React.FC = () => {
  const { colors, spacing, typography, borderRadius } = useTheme();
  const offlineState = useOffline();
  const networkStatus = useNetworkStatus();
  const { pendingActions, syncedActions, failedActions, addAction, forceSync, clearActions } = useOfflineActions();
  const networkQuality = useNetworkQuality();
  
  const [offlineNotes, setOfflineNotes, clearOfflineNotes] = useOfflineStorage('demo-notes', []);
  const [newNote, setNewNote] = useState('');
  const [simulateOffline, setSimulateOffline] = useState(false);

  const containerStyles: React.CSSProperties = {
    padding: spacing.lg,
    maxWidth: '1200px',
    margin: '0 auto',
    fontFamily: typography.fontFamily,
    color: colors.text,
    backgroundColor: colors.background,
    minHeight: '100vh',
  };

  const sectionStyles: React.CSSProperties = {
    marginBottom: spacing.xl,
    padding: spacing.lg,
    backgroundColor: colors.surface,
    borderRadius: borderRadius.lg,
    border: `1px solid ${colors.border}`,
  };

  const titleStyles: React.CSSProperties = {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.md,
    color: colors.text,
  };

  const subtitleStyles: React.CSSProperties = {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.sm,
    color: colors.text,
  };

  const statusGridStyles: React.CSSProperties = {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
    gap: spacing.md,
    marginBottom: spacing.lg,
  };

  const statusCardStyles: React.CSSProperties = {
    padding: spacing.md,
    backgroundColor: colors.background,
    borderRadius: borderRadius.md,
    border: `1px solid ${colors.border}`,
  };

  const inputStyles: React.CSSProperties = {
    width: '100%',
    padding: spacing.sm,
    border: `1px solid ${colors.border}`,
    borderRadius: borderRadius.sm,
    backgroundColor: colors.background,
    color: colors.text,
    fontSize: typography.fontSize.sm,
    marginBottom: spacing.sm,
  };

  const noteStyles: React.CSSProperties = {
    padding: spacing.sm,
    marginBottom: spacing.xs,
    backgroundColor: colors.background,
    border: `1px solid ${colors.border}`,
    borderRadius: borderRadius.sm,
    fontSize: typography.fontSize.sm,
  };

  // 添加笔记
  const handleAddNote = useCallback(() => {
    if (!newNote.trim()) return;

    const note = {
      id: Date.now().toString(),
      content: newNote,
      timestamp: Date.now(),
      synced: !offlineState.isOfflineMode,
    };

    setOfflineNotes([...offlineNotes, note]);
    setNewNote('');

    // 如果离线，添加到同步队列
    if (offlineState.isOfflineMode || simulateOffline) {
      addAction('save-note', note, 'medium');
    }
  }, [newNote, offlineNotes, setOfflineNotes, offlineState.isOfflineMode, simulateOffline, addAction]);

  // 模拟API调用
  const handleSimulateAPICall = useCallback(() => {
    const data = {
      action: 'test-api-call',
      timestamp: Date.now(),
      data: { message: '这是一个测试API调用' },
    };

    addAction('api-call', data, 'high');
  }, [addAction]);

  // 模拟离线模式
  const handleToggleSimulateOffline = useCallback(() => {
    setSimulateOffline(!simulateOffline);
    
    if (!simulateOffline) {
      // 模拟进入离线模式
      Object.defineProperty(navigator, 'onLine', {
        writable: true,
        value: false,
      });
      window.dispatchEvent(new Event('offline'));
    } else {
      // 模拟恢复在线
      Object.defineProperty(navigator, 'onLine', {
        writable: true,
        value: true,
      });
      window.dispatchEvent(new Event('online'));
    }
  }, [simulateOffline]);

  // 获取网络质量颜色
  const getNetworkQualityColor = () => {
    switch (networkQuality) {
      case 'excellent': return colors.success;
      case 'good': return colors.info;
      case 'fair': return colors.warning;
      case 'poor': return colors.error;
      default: return colors.textSecondary;
    }
  };

  return (
    <div style={containerStyles}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: spacing.lg }}>
        <h1 style={titleStyles}>AI编程助手 - 离线模式演示</h1>
        <NetworkStatusBadge showText={true} size="md" />
      </div>

      {/* 网络状态概览 */}
      <section style={sectionStyles}>
        <h2 style={subtitleStyles}>网络状态概览</h2>
        
        <div style={statusGridStyles}>
          <div style={statusCardStyles}>
            <div style={{ 
              fontSize: typography.fontSize.lg, 
              fontWeight: 'bold', 
              color: offlineState.isOfflineMode ? colors.error : colors.success,
              marginBottom: spacing.xs,
            }}>
              {offlineState.isOfflineMode ? '离线模式' : '在线模式'}
            </div>
            <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
              当前连接状态
            </div>
          </div>
          
          <div style={statusCardStyles}>
            <div style={{ 
              fontSize: typography.fontSize.lg, 
              fontWeight: 'bold', 
              color: getNetworkQualityColor(),
              marginBottom: spacing.xs,
            }}>
              {networkQuality}
            </div>
            <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
              网络质量
            </div>
          </div>
          
          <div style={statusCardStyles}>
            <div style={{ 
              fontSize: typography.fontSize.lg, 
              fontWeight: 'bold', 
              color: colors.primary,
              marginBottom: spacing.xs,
            }}>
              {networkStatus.connectionType}
            </div>
            <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
              连接类型
            </div>
          </div>
          
          <div style={statusCardStyles}>
            <div style={{ 
              fontSize: typography.fontSize.lg, 
              fontWeight: 'bold', 
              color: colors.info,
              marginBottom: spacing.xs,
            }}>
              {networkStatus.downlink > 0 ? `${networkStatus.downlink}Mbps` : 'N/A'}
            </div>
            <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
              下行带宽
            </div>
          </div>
        </div>

        <div style={{ display: 'flex', gap: spacing.sm, flexWrap: 'wrap' }}>
          <Button 
            onClick={handleToggleSimulateOffline}
            variant={simulateOffline ? 'secondary' : 'outline'}
          >
            {simulateOffline ? '停止模拟离线' : '模拟离线模式'}
          </Button>
          
          <Button onClick={forceSync} disabled={offlineState.isOfflineMode}>
            强制同步
          </Button>
        </div>
      </section>

      {/* 离线操作队列 */}
      <section style={sectionStyles}>
        <h2 style={subtitleStyles}>离线操作队列</h2>
        
        <div style={statusGridStyles}>
          <div style={statusCardStyles}>
            <div style={{ 
              fontSize: typography.fontSize.lg, 
              fontWeight: 'bold', 
              color: colors.warning,
              marginBottom: spacing.xs,
            }}>
              {pendingActions}
            </div>
            <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
              待同步操作
            </div>
          </div>
          
          <div style={statusCardStyles}>
            <div style={{ 
              fontSize: typography.fontSize.lg, 
              fontWeight: 'bold', 
              color: colors.success,
              marginBottom: spacing.xs,
            }}>
              {syncedActions}
            </div>
            <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
              已同步操作
            </div>
          </div>
          
          <div style={statusCardStyles}>
            <div style={{ 
              fontSize: typography.fontSize.lg, 
              fontWeight: 'bold', 
              color: colors.error,
              marginBottom: spacing.xs,
            }}>
              {failedActions}
            </div>
            <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
              失败操作
            </div>
          </div>
        </div>

        <div style={{ display: 'flex', gap: spacing.sm, flexWrap: 'wrap' }}>
          <Button onClick={handleSimulateAPICall}>
            模拟API调用
          </Button>
          
          <Button onClick={clearActions} variant="outline">
            清除操作队列
          </Button>
        </div>
      </section>

      {/* 离线笔记功能 */}
      <section style={sectionStyles}>
        <h2 style={subtitleStyles}>离线笔记功能</h2>
        <p style={{ color: colors.textSecondary, marginBottom: spacing.md }}>
          在离线模式下，您可以继续添加笔记，它们将在网络恢复后自动同步。
        </p>
        
        <div style={{ marginBottom: spacing.md }}>
          <input
            type="text"
            value={newNote}
            onChange={(e) => setNewNote(e.target.value)}
            placeholder="输入新笔记..."
            style={inputStyles}
            onKeyPress={(e) => e.key === 'Enter' && handleAddNote()}
          />
          <Button onClick={handleAddNote} disabled={!newNote.trim()}>
            添加笔记
          </Button>
        </div>

        <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
          {offlineNotes.length === 0 ? (
            <div style={{ 
              textAlign: 'center', 
              color: colors.textSecondary,
              padding: spacing.lg,
            }}>
              暂无笔记
            </div>
          ) : (
            offlineNotes.map((note: any) => (
              <div key={note.id} style={noteStyles}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <div style={{ flex: 1 }}>
                    {note.content}
                  </div>
                  <div style={{ 
                    fontSize: typography.fontSize.xs,
                    color: note.synced ? colors.success : colors.warning,
                    marginLeft: spacing.sm,
                    whiteSpace: 'nowrap',
                  }}>
                    {note.synced ? '✓ 已同步' : '⏳ 待同步'}
                  </div>
                </div>
                <div style={{ 
                  fontSize: typography.fontSize.xs,
                  color: colors.textSecondary,
                  marginTop: spacing.xs,
                }}>
                  {new Date(note.timestamp).toLocaleString()}
                </div>
              </div>
            ))
          )}
        </div>

        {offlineNotes.length > 0 && (
          <div style={{ marginTop: spacing.md }}>
            <Button onClick={clearOfflineNotes} variant="outline" size="sm">
              清除所有笔记
            </Button>
          </div>
        )}
      </section>

      {/* 离线功能说明 */}
      <section style={sectionStyles}>
        <h2 style={subtitleStyles}>离线功能特性</h2>
        <ul style={{ color: colors.textSecondary, lineHeight: 1.6 }}>
          <li>🔄 自动检测网络状态变化</li>
          <li>📱 支持Service Worker离线缓存</li>
          <li>⚡ 智能操作队列管理</li>
          <li>🔄 网络恢复后自动同步</li>
          <li>💾 本地数据持久化存储</li>
          <li>📊 实时网络质量监控</li>
          <li>🎯 优先级队列处理</li>
          <li>🔔 离线状态通知提醒</li>
          <li>🛠️ 优雅降级用户体验</li>
          <li>📈 详细的同步统计信息</li>
        </ul>
      </section>

      {/* 离线状态指示器 */}
      <OfflineIndicator 
        position="bottom-right"
        compact={false}
        showDetails={true}
      />
    </div>
  );
};
