<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#357ABD;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="64" cy="64" r="60" fill="url(#bgGradient)" stroke="#2C5282" stroke-width="2"/>
  
  <!-- AI Brain/Circuit Pattern -->
  <g fill="white" opacity="0.9">
    <!-- Central processor -->
    <rect x="54" y="54" width="20" height="20" rx="2" fill="white"/>
    
    <!-- Connection lines -->
    <line x1="34" y1="44" x2="54" y2="54" stroke="white" stroke-width="2"/>
    <line x1="74" y1="54" x2="94" y2="44" stroke="white" stroke-width="2"/>
    <line x1="34" y1="84" x2="54" y2="74" stroke="white" stroke-width="2"/>
    <line x1="74" y1="74" x2="94" y2="84" stroke="white" stroke-width="2"/>
    
    <!-- Neural nodes -->
    <circle cx="34" cy="44" r="4" fill="white"/>
    <circle cx="94" cy="44" r="4" fill="white"/>
    <circle cx="34" cy="84" r="4" fill="white"/>
    <circle cx="94" cy="84" r="4" fill="white"/>
    
    <!-- Additional connections -->
    <circle cx="24" cy="34" r="2" fill="white"/>
    <circle cx="104" cy="34" r="2" fill="white"/>
    <circle cx="24" cy="94" r="2" fill="white"/>
    <circle cx="104" cy="94" r="2" fill="white"/>
    
    <!-- Chat bubble indicator -->
    <path d="M 44 34 L 84 34 Q 90 34 90 40 L 90 50 Q 90 56 84 56 L 50 56 L 44 62 L 44 56 Q 44 50 44 44 Z" 
          fill="white" opacity="0.8"/>
    
    <!-- Dots in chat bubble -->
    <circle cx="54" cy="45" r="1.5" fill="#4A90E2"/>
    <circle cx="64" cy="45" r="1.5" fill="#4A90E2"/>
    <circle cx="74" cy="45" r="1.5" fill="#4A90E2"/>
  </g>
</svg> 