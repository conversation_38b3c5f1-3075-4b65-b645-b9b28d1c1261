/**
 * Chat Interface Styles - 聊天界面样式
 */

.chat-interface {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--vscode-editor-background);
  color: var(--vscode-editor-foreground);
  font-family: var(--vscode-font-family);
  position: relative;
}

/* 主聊天区域 */
.chat-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  transition: margin-right 0.3s ease;
}

/* 聊天头部 */
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--vscode-panel-border);
  background: var(--vscode-panel-background);
}

.chat-title h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--vscode-foreground);
}

.chat-subtitle {
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
  margin-left: 8px;
}

.chat-controls {
  display: flex;
  gap: 8px;
}

.control-button {
  background: transparent;
  border: 1px solid var(--vscode-button-border);
  color: var(--vscode-button-foreground);
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.control-button:hover {
  background: var(--vscode-button-hoverBackground);
}

/* 消息容器 */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  scroll-behavior: smooth;
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 800px;
  margin: 0 auto;
}

/* 加载指示器 */
.loading-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--vscode-panel-background);
  border-radius: 8px;
  border: 1px solid var(--vscode-panel-border);
}

.loading-dots {
  display: flex;
  gap: 4px;
}

.loading-dots span {
  width: 6px;
  height: 6px;
  background: var(--vscode-progressBar-background);
  border-radius: 50%;
  animation: loading-bounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.loading-text {
  color: var(--vscode-descriptionForeground);
  font-size: 14px;
}

/* 侧边栏调整器 */
.side-panel-resizer {
  position: fixed;
  top: 0;
  right: var(--side-panel-width, 300px);
  width: 4px;
  height: 100vh;
  background: transparent;
  cursor: col-resize;
  z-index: 1000;
}

.side-panel-resizer:hover {
  background: var(--vscode-focusBorder);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-main {
    margin-right: 0 !important;
  }
  
  .messages-container {
    padding: 12px;
  }
  
  .messages-list {
    max-width: 100%;
  }
  
  .chat-header {
    padding: 8px 12px;
  }
  
  .chat-title h2 {
    font-size: 16px;
  }
}

/* 主题适配 */
.chat-interface[data-theme="dark"] {
  --message-bg-user: #2d3748;
  --message-bg-assistant: #1a202c;
  --code-bg: #1e2124;
  --border-color: #4a5568;
}

.chat-interface[data-theme="light"] {
  --message-bg-user: #f7fafc;
  --message-bg-assistant: #ffffff;
  --code-bg: #f8f9fa;
  --border-color: #e2e8f0;
}

/* 滚动条样式 */
.messages-container::-webkit-scrollbar {
  width: 8px;
}

.messages-container::-webkit-scrollbar-track {
  background: var(--vscode-scrollbarSlider-background);
}

.messages-container::-webkit-scrollbar-thumb {
  background: var(--vscode-scrollbarSlider-hoverBackground);
  border-radius: 4px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: var(--vscode-scrollbarSlider-activeBackground);
}

/* 动画效果 */
.chat-interface * {
  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
}

/* 焦点样式 */
.control-button:focus,
.side-panel-resizer:focus {
  outline: 2px solid var(--vscode-focusBorder);
  outline-offset: 2px;
}
