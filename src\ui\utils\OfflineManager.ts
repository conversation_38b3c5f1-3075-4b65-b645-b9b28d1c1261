/**
 * 离线模式管理器
 * 
 * 提供离线功能支持，包括网络状态检测、数据同步和优雅降级
 */

export interface OfflineConfig {
  enableOfflineMode: boolean;
  syncInterval: number; // 同步间隔（毫秒）
  maxOfflineActions: number; // 最大离线操作数
  retryAttempts: number; // 重试次数
  retryDelay: number; // 重试延迟（毫秒）
  enableBackgroundSync: boolean; // 后台同步
}

export interface OfflineAction {
  id: string;
  type: string;
  data: any;
  timestamp: number;
  retryCount: number;
  priority: 'low' | 'medium' | 'high';
}

export interface NetworkStatus {
  isOnline: boolean;
  connectionType: string;
  effectiveType: string;
  downlink: number;
  rtt: number;
  saveData: boolean;
}

export interface OfflineStats {
  isOfflineMode: boolean;
  pendingActions: number;
  syncedActions: number;
  failedActions: number;
  lastSyncTime: number;
  networkStatus: NetworkStatus;
}

export class OfflineManager {
  private static instance: OfflineManager;
  private config: OfflineConfig;
  private isOnline: boolean = navigator.onLine;
  private pendingActions: Map<string, OfflineAction> = new Map();
  private syncTimer: NodeJS.Timeout | null = null;
  private listeners: Set<(status: OfflineStats) => void> = new Set();
  private stats: OfflineStats;

  constructor(config: Partial<OfflineConfig> = {}) {
    this.config = {
      enableOfflineMode: true,
      syncInterval: 30000, // 30秒
      maxOfflineActions: 1000,
      retryAttempts: 3,
      retryDelay: 5000, // 5秒
      enableBackgroundSync: true,
      ...config,
    };

    this.stats = {
      isOfflineMode: !this.isOnline,
      pendingActions: 0,
      syncedActions: 0,
      failedActions: 0,
      lastSyncTime: 0,
      networkStatus: this.getNetworkStatus(),
    };

    this.initialize();
  }

  public static getInstance(config?: Partial<OfflineConfig>): OfflineManager {
    if (!OfflineManager.instance) {
      OfflineManager.instance = new OfflineManager(config);
    }
    return OfflineManager.instance;
  }

  /**
   * 初始化离线管理器
   */
  private initialize(): void {
    if (!this.config.enableOfflineMode) return;

    // 监听网络状态变化
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));

    // 监听网络信息变化
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      connection.addEventListener('change', this.handleNetworkChange.bind(this));
    }

    // 加载离线数据
    this.loadOfflineData();

    // 启动同步定时器
    this.startSyncTimer();

    // 注册Service Worker（如果支持）
    this.registerServiceWorker();
  }

  /**
   * 处理网络连接
   */
  private handleOnline(): void {
    this.isOnline = true;
    this.updateStats();
    this.notifyListeners();
    
    // 立即尝试同步
    this.syncPendingActions();
  }

  /**
   * 处理网络断开
   */
  private handleOffline(): void {
    this.isOnline = false;
    this.updateStats();
    this.notifyListeners();
  }

  /**
   * 处理网络信息变化
   */
  private handleNetworkChange(): void {
    this.updateStats();
    this.notifyListeners();
  }

  /**
   * 获取网络状态
   */
  private getNetworkStatus(): NetworkStatus {
    const connection = (navigator as any).connection;
    
    return {
      isOnline: this.isOnline,
      connectionType: connection?.type || 'unknown',
      effectiveType: connection?.effectiveType || 'unknown',
      downlink: connection?.downlink || 0,
      rtt: connection?.rtt || 0,
      saveData: connection?.saveData || false,
    };
  }

  /**
   * 添加离线操作
   */
  public addOfflineAction(
    type: string,
    data: any,
    priority: 'low' | 'medium' | 'high' = 'medium'
  ): string {
    const action: OfflineAction = {
      id: this.generateId(),
      type,
      data,
      timestamp: Date.now(),
      retryCount: 0,
      priority,
    };

    this.pendingActions.set(action.id, action);
    this.saveOfflineData();
    this.updateStats();
    this.notifyListeners();

    // 如果在线，立即尝试执行
    if (this.isOnline) {
      this.executeAction(action);
    }

    return action.id;
  }

  /**
   * 执行操作
   */
  private async executeAction(action: OfflineAction): Promise<boolean> {
    try {
      // 这里应该根据action.type调用相应的API
      const success = await this.callAPI(action);
      
      if (success) {
        this.pendingActions.delete(action.id);
        this.stats.syncedActions++;
        this.saveOfflineData();
        this.updateStats();
        return true;
      } else {
        throw new Error('API call failed');
      }
    } catch (error) {
      console.error(`Failed to execute action ${action.id}:`, error);
      
      action.retryCount++;
      if (action.retryCount >= this.config.retryAttempts) {
        this.pendingActions.delete(action.id);
        this.stats.failedActions++;
      }
      
      this.saveOfflineData();
      this.updateStats();
      return false;
    }
  }

  /**
   * 调用API（模拟）
   */
  private async callAPI(action: OfflineAction): Promise<boolean> {
    // 模拟API调用
    return new Promise((resolve) => {
      setTimeout(() => {
        // 90%成功率
        resolve(Math.random() > 0.1);
      }, 100 + Math.random() * 500);
    });
  }

  /**
   * 同步待处理操作
   */
  private async syncPendingActions(): Promise<void> {
    if (!this.isOnline || this.pendingActions.size === 0) return;

    const actions = Array.from(this.pendingActions.values())
      .sort((a, b) => {
        // 按优先级和时间戳排序
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
        return priorityDiff !== 0 ? priorityDiff : a.timestamp - b.timestamp;
      });

    // 批量执行操作
    const batchSize = 5;
    for (let i = 0; i < actions.length; i += batchSize) {
      const batch = actions.slice(i, i + batchSize);
      await Promise.all(batch.map(action => this.executeAction(action)));
      
      // 避免过度占用网络资源
      if (i + batchSize < actions.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    this.stats.lastSyncTime = Date.now();
    this.updateStats();
    this.notifyListeners();
  }

  /**
   * 启动同步定时器
   */
  private startSyncTimer(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }

    this.syncTimer = setInterval(() => {
      if (this.isOnline) {
        this.syncPendingActions();
      }
    }, this.config.syncInterval);
  }

  /**
   * 保存离线数据
   */
  private saveOfflineData(): void {
    try {
      const data = {
        pendingActions: Array.from(this.pendingActions.entries()),
        stats: this.stats,
      };
      
      localStorage.setItem('ai-agent-offline', JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to save offline data:', error);
    }
  }

  /**
   * 加载离线数据
   */
  private loadOfflineData(): void {
    try {
      const data = localStorage.getItem('ai-agent-offline');
      if (data) {
        const parsed = JSON.parse(data);
        
        // 恢复待处理操作
        if (parsed.pendingActions) {
          this.pendingActions = new Map(parsed.pendingActions);
        }
        
        // 恢复统计信息
        if (parsed.stats) {
          this.stats = { ...this.stats, ...parsed.stats };
        }
      }
    } catch (error) {
      console.warn('Failed to load offline data:', error);
    }
  }

  /**
   * 注册Service Worker
   */
  private async registerServiceWorker(): void {
    if (!('serviceWorker' in navigator)) return;

    try {
      const registration = await navigator.serviceWorker.register('/sw.js');
      console.log('Service Worker registered:', registration);
      
      // 监听Service Worker消息
      navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data.type === 'BACKGROUND_SYNC') {
          this.syncPendingActions();
        }
      });
    } catch (error) {
      console.warn('Service Worker registration failed:', error);
    }
  }

  /**
   * 更新统计信息
   */
  private updateStats(): void {
    this.stats = {
      ...this.stats,
      isOfflineMode: !this.isOnline,
      pendingActions: this.pendingActions.size,
      networkStatus: this.getNetworkStatus(),
    };
  }

  /**
   * 通知监听器
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.stats);
      } catch (error) {
        console.error('Error in offline status listener:', error);
      }
    });
  }

  /**
   * 添加状态监听器
   */
  public addStatusListener(listener: (status: OfflineStats) => void): () => void {
    this.listeners.add(listener);
    
    // 立即调用一次
    listener(this.stats);
    
    // 返回取消监听的函数
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * 获取当前状态
   */
  public getStatus(): OfflineStats {
    return { ...this.stats };
  }

  /**
   * 检查是否在线
   */
  public isOnlineMode(): boolean {
    return this.isOnline;
  }

  /**
   * 检查是否为离线模式
   */
  public isOfflineMode(): boolean {
    return !this.isOnline;
  }

  /**
   * 强制同步
   */
  public async forceSync(): Promise<void> {
    if (this.isOnline) {
      await this.syncPendingActions();
    }
  }

  /**
   * 清除离线数据
   */
  public clearOfflineData(): void {
    this.pendingActions.clear();
    this.stats.pendingActions = 0;
    this.stats.syncedActions = 0;
    this.stats.failedActions = 0;
    this.saveOfflineData();
    this.updateStats();
    this.notifyListeners();
  }

  /**
   * 获取网络质量评估
   */
  public getNetworkQuality(): 'excellent' | 'good' | 'fair' | 'poor' {
    const { rtt, downlink, effectiveType } = this.stats.networkStatus;
    
    if (!this.isOnline) return 'poor';
    
    if (effectiveType === '4g' && rtt < 100 && downlink > 10) {
      return 'excellent';
    } else if (effectiveType === '4g' || (rtt < 200 && downlink > 5)) {
      return 'good';
    } else if (effectiveType === '3g' || (rtt < 500 && downlink > 1)) {
      return 'fair';
    } else {
      return 'poor';
    }
  }

  /**
   * 检查是否应该使用离线优先策略
   */
  public shouldUseOfflineFirst(): boolean {
    const quality = this.getNetworkQuality();
    return quality === 'poor' || this.stats.networkStatus.saveData;
  }

  /**
   * 生成ID
   */
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 销毁离线管理器
   */
  public destroy(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }
    
    window.removeEventListener('online', this.handleOnline.bind(this));
    window.removeEventListener('offline', this.handleOffline.bind(this));
    
    this.saveOfflineData();
    this.listeners.clear();
  }
}

// 全局离线管理器实例
export const offlineManager = OfflineManager.getInstance();
