/* Tool Call Display Styles */
.tool-call-display {
  background: var(--vscode-panel-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 8px;
  margin: 8px 0;
  overflow: hidden;
}

.tool-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--vscode-panel-background);
  border-left: 4px solid;
  border-bottom: 1px solid var(--vscode-panel-border);
}

.tool-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.tool-icon {
  font-size: 20px;
}

.tool-details h3 {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: var(--vscode-foreground);
}

.tool-description {
  margin: 0;
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
}

.tool-actions {
  display: flex;
  gap: 8px;
}

.expand-button,
.execute-button {
  background: var(--vscode-button-background);
  border: 1px solid var(--vscode-button-border);
  color: var(--vscode-button-foreground);
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.expand-button {
  background: transparent;
  padding: 6px;
}

.expand-button:hover {
  background: var(--vscode-toolbar-hoverBackground);
}

.execute-button:hover:not(:disabled) {
  background: var(--vscode-button-hoverBackground);
}

.execute-button.executing {
  opacity: 0.7;
  cursor: not-allowed;
}

.execute-button .spinner {
  width: 12px;
  height: 12px;
  border: 1px solid var(--vscode-progressBar-background);
  border-top: 1px solid var(--vscode-button-foreground);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 6px;
}

.parameter-config {
  padding: 16px;
  border-bottom: 1px solid var(--vscode-panel-border);
  background: var(--vscode-input-background);
}

.parameter-config h4 {
  margin: 0 0 12px 0;
  font-size: 13px;
  color: var(--vscode-foreground);
}

.parameter-item {
  margin-bottom: 12px;
}

.parameter-label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  color: var(--vscode-foreground);
  font-weight: 500;
}

.required {
  color: var(--vscode-errorForeground);
  margin-left: 2px;
}

.parameter-input,
.parameter-select,
.parameter-textarea {
  width: 100%;
  background: var(--vscode-input-background);
  border: 1px solid var(--vscode-input-border);
  color: var(--vscode-input-foreground);
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-family: inherit;
}

.parameter-input:focus,
.parameter-select:focus,
.parameter-textarea:focus {
  outline: none;
  border-color: var(--vscode-focusBorder);
}

.parameter-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.parameter-checkbox input[type="checkbox"] {
  margin: 0;
}

.checkmark {
  width: 16px;
  height: 16px;
  border: 1px solid var(--vscode-input-border);
  border-radius: 3px;
  position: relative;
}

.parameter-checkbox input[type="checkbox"]:checked + .checkmark {
  background: var(--vscode-checkbox-background);
  border-color: var(--vscode-checkbox-border);
}

.parameter-checkbox input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--vscode-checkbox-foreground);
  font-size: 10px;
}

.parameter-array {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.array-item {
  display: flex;
  gap: 6px;
}

.array-item .parameter-input {
  flex: 1;
}

.remove-button,
.add-button {
  background: var(--vscode-button-secondaryBackground);
  border: 1px solid var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.remove-button {
  background: var(--vscode-errorForeground);
  border-color: var(--vscode-errorForeground);
  color: white;
  padding: 6px;
}

.add-button:hover {
  background: var(--vscode-button-secondaryHoverBackground);
}

.parameter-description {
  margin-top: 4px;
  font-size: 11px;
  color: var(--vscode-descriptionForeground);
  line-height: 1.3;
}

.tool-result {
  padding: 16px;
}

.tool-result.success {
  background: var(--vscode-inputValidation-infoBackground);
  border-left: 4px solid var(--vscode-inputValidation-infoBorder);
}

.tool-result.error {
  background: var(--vscode-inputValidation-errorBackground);
  border-left: 4px solid var(--vscode-inputValidation-errorBorder);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.result-status {
  font-size: 12px;
  font-weight: 600;
}

.result-duration {
  font-size: 11px;
  color: var(--vscode-descriptionForeground);
}

.result-error {
  margin-bottom: 8px;
  padding: 8px;
  background: var(--vscode-inputValidation-errorBackground);
  border: 1px solid var(--vscode-inputValidation-errorBorder);
  border-radius: 4px;
  font-size: 12px;
  color: var(--vscode-inputValidation-errorForeground);
}

.result-content {
  margin-bottom: 8px;
}

.text-content {
  font-size: 12px;
  line-height: 1.4;
  color: var(--vscode-foreground);
  white-space: pre-wrap;
}

.result-suggestions {
  margin-top: 8px;
}

.result-suggestions h5 {
  margin: 0 0 6px 0;
  font-size: 12px;
  color: var(--vscode-foreground);
}

.result-suggestions ul {
  margin: 0;
  padding-left: 16px;
  font-size: 11px;
  color: var(--vscode-descriptionForeground);
}

.result-suggestions li {
  margin-bottom: 2px;
}

.execution-progress {
  padding: 16px;
  background: var(--vscode-panel-background);
  border-top: 1px solid var(--vscode-panel-border);
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: var(--vscode-progressBar-background);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: var(--vscode-progressBar-foreground);
  animation: progressPulse 2s ease-in-out infinite;
}

.progress-text {
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
  text-align: center;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes progressPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tool-header {
    padding: 10px 12px;
  }
  
  .tool-info {
    gap: 8px;
  }
  
  .tool-icon {
    font-size: 18px;
  }
  
  .parameter-config {
    padding: 12px;
  }
  
  .tool-result {
    padding: 12px;
  }
}
