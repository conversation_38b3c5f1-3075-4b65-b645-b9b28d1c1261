/**
 * Service Worker for AI Agent
 * 
 * 提供离线缓存、后台同步和推送通知支持
 */

const CACHE_NAME = 'ai-agent-v1';
const STATIC_CACHE = 'ai-agent-static-v1';
const DYNAMIC_CACHE = 'ai-agent-dynamic-v1';

// 需要缓存的静态资源
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/static/css/main.css',
  '/static/js/main.js',
  // 添加其他静态资源
];

// 需要缓存的API端点
const CACHE_API_PATTERNS = [
  /\/api\/conversations/,
  /\/api\/messages/,
  /\/api\/settings/,
];

// 离线时的降级页面
const OFFLINE_PAGE = '/offline.html';

/**
 * Service Worker安装事件
 */
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...');
  
  event.waitUntil(
    Promise.all([
      // 缓存静态资源
      caches.open(STATIC_CACHE).then((cache) => {
        return cache.addAll(STATIC_ASSETS);
      }),
      
      // 创建离线页面
      caches.open(DYNAMIC_CACHE).then((cache) => {
        return cache.put(OFFLINE_PAGE, new Response(`
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <title>AI编程助手 - 离线模式</title>
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
              body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                margin: 0;
                padding: 2rem;
                background: #f5f5f5;
                color: #333;
                text-align: center;
              }
              .container {
                max-width: 600px;
                margin: 0 auto;
                background: white;
                padding: 2rem;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
              }
              .icon {
                font-size: 4rem;
                margin-bottom: 1rem;
              }
              h1 {
                color: #2563eb;
                margin-bottom: 1rem;
              }
              .status {
                background: #fef3c7;
                border: 1px solid #f59e0b;
                border-radius: 4px;
                padding: 1rem;
                margin: 1rem 0;
              }
              .features {
                text-align: left;
                margin: 2rem 0;
              }
              .features li {
                margin: 0.5rem 0;
              }
              .retry-btn {
                background: #2563eb;
                color: white;
                border: none;
                padding: 0.75rem 1.5rem;
                border-radius: 4px;
                cursor: pointer;
                font-size: 1rem;
              }
              .retry-btn:hover {
                background: #1d4ed8;
              }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="icon">🤖</div>
              <h1>AI编程助手</h1>
              <div class="status">
                <strong>⚠️ 当前处于离线模式</strong>
              </div>
              <p>网络连接不可用，但您仍然可以使用以下功能：</p>
              <ul class="features">
                <li>📝 查看已缓存的对话历史</li>
                <li>🔍 搜索本地对话内容</li>
                <li>⚙️ 修改应用设置</li>
                <li>📋 查看代码片段</li>
                <li>🎨 切换主题</li>
              </ul>
              <p>网络恢复后，您的操作将自动同步。</p>
              <button class="retry-btn" onclick="window.location.reload()">
                重试连接
              </button>
            </div>
            <script>
              // 监听网络状态变化
              window.addEventListener('online', () => {
                window.location.reload();
              });
              
              // 定期检查网络状态
              setInterval(() => {
                if (navigator.onLine) {
                  window.location.reload();
                }
              }, 5000);
            </script>
          </body>
          </html>
        `, {
          headers: { 'Content-Type': 'text/html' }
        }));
      })
    ]).then(() => {
      // 强制激活新的Service Worker
      return self.skipWaiting();
    })
  );
});

/**
 * Service Worker激活事件
 */
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...');
  
  event.waitUntil(
    Promise.all([
      // 清理旧缓存
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      
      // 立即控制所有客户端
      self.clients.claim()
    ])
  );
});

/**
 * 网络请求拦截
 */
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // 只处理同源请求
  if (url.origin !== location.origin) {
    return;
  }
  
  // HTML页面请求
  if (request.destination === 'document') {
    event.respondWith(handleDocumentRequest(request));
    return;
  }
  
  // API请求
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(handleAPIRequest(request));
    return;
  }
  
  // 静态资源请求
  event.respondWith(handleStaticRequest(request));
});

/**
 * 处理HTML文档请求
 */
async function handleDocumentRequest(request) {
  try {
    // 尝试网络请求
    const networkResponse = await fetch(request);
    
    // 缓存成功的响应
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    // 网络失败，返回缓存或离线页面
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // 返回离线页面
    return caches.match(OFFLINE_PAGE);
  }
}

/**
 * 处理API请求
 */
async function handleAPIRequest(request) {
  const url = new URL(request.url);
  
  // 检查是否为可缓存的API
  const isCacheable = CACHE_API_PATTERNS.some(pattern => pattern.test(url.pathname));
  
  try {
    // 尝试网络请求
    const networkResponse = await fetch(request);
    
    // 缓存GET请求的成功响应
    if (networkResponse.ok && request.method === 'GET' && isCacheable) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    // 网络失败，对于GET请求尝试返回缓存
    if (request.method === 'GET' && isCacheable) {
      const cachedResponse = await caches.match(request);
      if (cachedResponse) {
        return cachedResponse;
      }
    }
    
    // 对于POST/PUT/DELETE请求，存储到后台同步队列
    if (request.method !== 'GET') {
      await storeOfflineAction(request);
      
      // 返回表示操作已排队的响应
      return new Response(JSON.stringify({
        success: true,
        message: '操作已排队，将在网络恢复后同步',
        offline: true
      }), {
        status: 202,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // 返回错误响应
    return new Response(JSON.stringify({
      error: '网络不可用',
      offline: true
    }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理静态资源请求
 */
async function handleStaticRequest(request) {
  // 缓存优先策略
  const cachedResponse = await caches.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    // 尝试网络请求
    const networkResponse = await fetch(request);
    
    // 缓存成功的响应
    if (networkResponse.ok) {
      const cache = await caches.open(STATIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    // 网络失败，返回通用错误
    return new Response('资源不可用', { status: 503 });
  }
}

/**
 * 存储离线操作
 */
async function storeOfflineAction(request) {
  try {
    const body = await request.text();
    const action = {
      id: Date.now() + '-' + Math.random().toString(36).substr(2, 9),
      url: request.url,
      method: request.method,
      headers: Object.fromEntries(request.headers.entries()),
      body: body,
      timestamp: Date.now()
    };
    
    // 存储到IndexedDB或发送消息给主线程
    self.registration.sync.register('background-sync');
    
    // 通知主线程
    const clients = await self.clients.matchAll();
    clients.forEach(client => {
      client.postMessage({
        type: 'OFFLINE_ACTION_STORED',
        action: action
      });
    });
  } catch (error) {
    console.error('Failed to store offline action:', error);
  }
}

/**
 * 后台同步事件
 */
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(syncOfflineActions());
  }
});

/**
 * 同步离线操作
 */
async function syncOfflineActions() {
  try {
    // 通知主线程执行同步
    const clients = await self.clients.matchAll();
    clients.forEach(client => {
      client.postMessage({
        type: 'BACKGROUND_SYNC'
      });
    });
  } catch (error) {
    console.error('Background sync failed:', error);
  }
}

/**
 * 推送通知事件
 */
self.addEventListener('push', (event) => {
  if (!event.data) return;
  
  const data = event.data.json();
  const options = {
    body: data.body,
    icon: '/icon-192x192.png',
    badge: '/badge-72x72.png',
    vibrate: [100, 50, 100],
    data: data.data,
    actions: data.actions || []
  };
  
  event.waitUntil(
    self.registration.showNotification(data.title, options)
  );
});

/**
 * 通知点击事件
 */
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  event.waitUntil(
    clients.openWindow(event.notification.data.url || '/')
  );
});

/**
 * 消息事件
 */
self.addEventListener('message', (event) => {
  if (event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});

console.log('Service Worker loaded');
