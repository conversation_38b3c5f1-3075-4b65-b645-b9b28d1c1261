/**
 * Code Context Extractor - 代码上下文提取器
 * 
 * 负责提取和分析代码上下文信息
 */

import * as vscode from 'vscode';
import * as path from 'path';
import { EventBus } from './EventBus';

export interface CodeContext {
  type: 'selection' | 'file' | 'workspace' | 'related';
  content: string;
  filePath: string;
  language: string;
  lineRange?: { start: number; end: number };
  metadata?: {
    functions?: string[];
    classes?: string[];
    imports?: string[];
    exports?: string[];
    dependencies?: string[];
  };
}

export interface ContextExtractionOptions {
  includeImports?: boolean;
  includeRelatedFiles?: boolean;
  maxFileSize?: number;
  maxRelatedFiles?: number;
  includeComments?: boolean;
}

export class CodeContextExtractor {
  private eventBus: EventBus;

  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
  }

  /**
   * 获取当前选中的代码上下文
   */
  async getSelectionContext(options: ContextExtractionOptions = {}): Promise<CodeContext | null> {
    const editor = vscode.window.activeTextEditor;
    if (!editor || editor.selection.isEmpty) {
      return null;
    }

    const document = editor.document;
    const selection = editor.selection;
    const selectedText = document.getText(selection);

    const context: CodeContext = {
      type: 'selection',
      content: selectedText,
      filePath: document.uri.fsPath,
      language: document.languageId,
      lineRange: {
        start: selection.start.line + 1,
        end: selection.end.line + 1
      }
    };

    // 分析选中代码的元数据
    context.metadata = await this.analyzeCodeMetadata(selectedText, document.languageId);

    await this.eventBus.emit({
      type: 'context.selection_extracted',
      source: 'CodeContextExtractor',
      filePath: context.filePath,
      lineRange: context.lineRange,
      language: context.language
    });

    return context;
  }

  /**
   * 获取当前文件的上下文
   */
  async getFileContext(options: ContextExtractionOptions = {}): Promise<CodeContext | null> {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      return null;
    }

    const document = editor.document;
    const content = document.getText();

    // 检查文件大小限制
    if (options.maxFileSize && content.length > options.maxFileSize) {
      return this.getPartialFileContext(document, options);
    }

    const context: CodeContext = {
      type: 'file',
      content: content,
      filePath: document.uri.fsPath,
      language: document.languageId
    };

    // 分析文件元数据
    context.metadata = await this.analyzeCodeMetadata(content, document.languageId);

    await this.eventBus.emit({
      type: 'context.file_extracted',
      source: 'CodeContextExtractor',
      filePath: context.filePath,
      language: context.language,
      size: content.length
    });

    return context;
  }

  /**
   * 获取相关文件的上下文
   */
  async getRelatedFilesContext(
    currentFile: string,
    options: ContextExtractionOptions = {}
  ): Promise<CodeContext[]> {
    const relatedFiles = await this.findRelatedFiles(currentFile, options.maxRelatedFiles || 5);
    const contexts: CodeContext[] = [];

    for (const filePath of relatedFiles) {
      try {
        const document = await vscode.workspace.openTextDocument(filePath);
        const content = document.getText();

        // 检查文件大小
        if (options.maxFileSize && content.length > options.maxFileSize) {
          continue;
        }

        const context: CodeContext = {
          type: 'related',
          content: content,
          filePath: filePath,
          language: document.languageId
        };

        context.metadata = await this.analyzeCodeMetadata(content, document.languageId);
        contexts.push(context);
      } catch (error) {
        console.error(`Failed to read related file ${filePath}:`, error);
      }
    }

    await this.eventBus.emit({
      type: 'context.related_files_extracted',
      source: 'CodeContextExtractor',
      currentFile,
      relatedFiles: relatedFiles,
      extractedCount: contexts.length
    });

    return contexts;
  }

  /**
   * 获取工作区上下文摘要
   */
  async getWorkspaceContext(): Promise<CodeContext> {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders || workspaceFolders.length === 0) {
      throw new Error('No workspace folder found');
    }

    const workspaceRoot = workspaceFolders[0].uri.fsPath;
    const projectStructure = await this.analyzeProjectStructure(workspaceRoot);

    const context: CodeContext = {
      type: 'workspace',
      content: projectStructure,
      filePath: workspaceRoot,
      language: 'text',
      metadata: {
        dependencies: await this.extractProjectDependencies(workspaceRoot)
      }
    };

    await this.eventBus.emit({
      type: 'context.workspace_extracted',
      source: 'CodeContextExtractor',
      workspaceRoot
    });

    return context;
  }

  /**
   * 分析代码元数据
   */
  private async analyzeCodeMetadata(content: string, language: string): Promise<any> {
    const metadata: any = {
      functions: [],
      classes: [],
      imports: [],
      exports: []
    };

    try {
      switch (language) {
        case 'typescript':
        case 'javascript':
          return this.analyzeJavaScriptMetadata(content);
        case 'python':
          return this.analyzePythonMetadata(content);
        case 'java':
          return this.analyzeJavaMetadata(content);
        case 'csharp':
          return this.analyzeCSharpMetadata(content);
        default:
          return this.analyzeGenericMetadata(content);
      }
    } catch (error) {
      console.error('Failed to analyze code metadata:', error);
      return metadata;
    }
  }

  /**
   * 分析JavaScript/TypeScript元数据
   */
  private analyzeJavaScriptMetadata(content: string): any {
    const metadata: any = {
      functions: [],
      classes: [],
      imports: [],
      exports: []
    };

    // 提取函数
    const functionRegex = /(?:function\s+(\w+)|(\w+)\s*[:=]\s*(?:async\s+)?function|(?:async\s+)?(\w+)\s*\([^)]*\)\s*(?::\s*[^{]+)?\s*\{)/g;
    let match;
    while ((match = functionRegex.exec(content)) !== null) {
      const functionName = match[1] || match[2] || match[3];
      if (functionName && !metadata.functions.includes(functionName)) {
        metadata.functions.push(functionName);
      }
    }

    // 提取类
    const classRegex = /class\s+(\w+)/g;
    while ((match = classRegex.exec(content)) !== null) {
      metadata.classes.push(match[1]);
    }

    // 提取导入
    const importRegex = /import\s+(?:{[^}]+}|\w+|\*\s+as\s+\w+)\s+from\s+['"]([^'"]+)['"]/g;
    while ((match = importRegex.exec(content)) !== null) {
      metadata.imports.push(match[1]);
    }

    // 提取导出
    const exportRegex = /export\s+(?:default\s+)?(?:class|function|const|let|var)\s+(\w+)/g;
    while ((match = exportRegex.exec(content)) !== null) {
      metadata.exports.push(match[1]);
    }

    return metadata;
  }

  /**
   * 分析Python元数据
   */
  private analyzePythonMetadata(content: string): any {
    const metadata: any = {
      functions: [],
      classes: [],
      imports: []
    };

    // 提取函数
    const functionRegex = /def\s+(\w+)\s*\(/g;
    let match;
    while ((match = functionRegex.exec(content)) !== null) {
      metadata.functions.push(match[1]);
    }

    // 提取类
    const classRegex = /class\s+(\w+)/g;
    while ((match = classRegex.exec(content)) !== null) {
      metadata.classes.push(match[1]);
    }

    // 提取导入
    const importRegex = /(?:from\s+(\S+)\s+import|import\s+(\S+))/g;
    while ((match = importRegex.exec(content)) !== null) {
      metadata.imports.push(match[1] || match[2]);
    }

    return metadata;
  }

  /**
   * 分析通用元数据
   */
  private analyzeGenericMetadata(content: string): any {
    return {
      lineCount: content.split('\n').length,
      characterCount: content.length,
      hasComments: /\/\*[\s\S]*?\*\/|\/\/.*$/m.test(content)
    };
  }

  /**
   * 分析Java元数据
   */
  private analyzeJavaMetadata(content: string): any {
    // 简化的Java分析
    return this.analyzeGenericMetadata(content);
  }

  /**
   * 分析C#元数据
   */
  private analyzeCSharpMetadata(content: string): any {
    // 简化的C#分析
    return this.analyzeGenericMetadata(content);
  }

  /**
   * 获取部分文件上下文（大文件处理）
   */
  private async getPartialFileContext(
    document: vscode.TextDocument,
    options: ContextExtractionOptions
  ): Promise<CodeContext> {
    const editor = vscode.window.activeTextEditor;
    const currentLine = editor?.selection.active.line || 0;
    
    // 获取当前位置周围的代码
    const startLine = Math.max(0, currentLine - 50);
    const endLine = Math.min(document.lineCount - 1, currentLine + 50);
    
    const range = new vscode.Range(startLine, 0, endLine, 0);
    const content = document.getText(range);

    return {
      type: 'file',
      content: content,
      filePath: document.uri.fsPath,
      language: document.languageId,
      lineRange: { start: startLine + 1, end: endLine + 1 }
    };
  }

  /**
   * 查找相关文件
   */
  private async findRelatedFiles(currentFile: string, maxFiles: number): Promise<string[]> {
    const relatedFiles: string[] = [];
    const currentDir = path.dirname(currentFile);
    const currentBaseName = path.basename(currentFile, path.extname(currentFile));

    try {
      // 查找同目录下的相关文件
      const files = await vscode.workspace.findFiles(
        new vscode.RelativePattern(currentDir, '**/*.{ts,js,tsx,jsx,py,java,cs}'),
        null,
        maxFiles * 2
      );

      for (const file of files) {
        const filePath = file.fsPath;
        if (filePath === currentFile) continue;

        const baseName = path.basename(filePath, path.extname(filePath));
        
        // 检查是否为相关文件（同名、测试文件等）
        if (
          baseName.includes(currentBaseName) ||
          currentBaseName.includes(baseName) ||
          baseName.endsWith('.test') ||
          baseName.endsWith('.spec') ||
          baseName.startsWith(currentBaseName)
        ) {
          relatedFiles.push(filePath);
          if (relatedFiles.length >= maxFiles) break;
        }
      }
    } catch (error) {
      console.error('Failed to find related files:', error);
    }

    return relatedFiles;
  }

  /**
   * 分析项目结构
   */
  private async analyzeProjectStructure(workspaceRoot: string): Promise<string> {
    try {
      const files = await vscode.workspace.findFiles('**/*', '**/node_modules/**', 100);
      const structure: { [key: string]: string[] } = {};

      for (const file of files) {
        const relativePath = vscode.workspace.asRelativePath(file);
        const dir = path.dirname(relativePath);
        const fileName = path.basename(relativePath);

        if (!structure[dir]) {
          structure[dir] = [];
        }
        structure[dir].push(fileName);
      }

      // 生成结构描述
      let description = '项目结构:\n';
      for (const [dir, files] of Object.entries(structure)) {
        description += `\n${dir}/\n`;
        files.slice(0, 10).forEach(file => {
          description += `  - ${file}\n`;
        });
        if (files.length > 10) {
          description += `  ... 还有 ${files.length - 10} 个文件\n`;
        }
      }

      return description;
    } catch (error) {
      console.error('Failed to analyze project structure:', error);
      return '无法分析项目结构';
    }
  }

  /**
   * 提取项目依赖
   */
  private async extractProjectDependencies(workspaceRoot: string): Promise<string[]> {
    const dependencies: string[] = [];

    try {
      // 检查package.json
      const packageJsonFiles = await vscode.workspace.findFiles('**/package.json', '**/node_modules/**');
      for (const file of packageJsonFiles) {
        const content = await vscode.workspace.fs.readFile(file);
        const packageJson = JSON.parse(content.toString());
        
        if (packageJson.dependencies) {
          dependencies.push(...Object.keys(packageJson.dependencies));
        }
        if (packageJson.devDependencies) {
          dependencies.push(...Object.keys(packageJson.devDependencies));
        }
      }

      // 检查requirements.txt
      const requirementFiles = await vscode.workspace.findFiles('**/requirements.txt');
      for (const file of requirementFiles) {
        const content = await vscode.workspace.fs.readFile(file);
        const lines = content.toString().split('\n');
        for (const line of lines) {
          const dep = line.trim().split('==')[0].split('>=')[0].split('<=')[0];
          if (dep && !dep.startsWith('#')) {
            dependencies.push(dep);
          }
        }
      }
    } catch (error) {
      console.error('Failed to extract dependencies:', error);
    }

    return Array.from(new Set(dependencies)); // 去重
  }
}
