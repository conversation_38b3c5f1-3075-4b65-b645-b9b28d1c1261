/**
 * 离线状态指示器组件
 * 
 * 显示网络状态、离线操作队列和同步进度
 */

import React, { useState } from 'react';
import { useTheme } from '../theme/ThemeProvider';
import { useOffline, useNetworkStatus, useOfflineActions } from '../hooks/useOffline';
import { Button } from './Button';
import { Transition } from './Transition';
import { Easing } from '../utils/AnimationManager';

export interface OfflineIndicatorProps {
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  compact?: boolean;
  showDetails?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export const OfflineIndicator: React.FC<OfflineIndicatorProps> = ({
  position = 'bottom-right',
  compact = false,
  showDetails = true,
  className,
  style,
}) => {
  const { colors, spacing, typography, borderRadius } = useTheme();
  const offlineState = useOffline();
  const networkStatus = useNetworkStatus();
  const { pendingActions, syncedActions, failedActions, forceSync, clearActions } = useOfflineActions();
  
  const [isExpanded, setIsExpanded] = useState(!compact);
  const [showSyncAnimation, setShowSyncAnimation] = useState(false);

  // 获取状态颜色
  const getStatusColor = () => {
    if (offlineState.isOfflineMode) return colors.error;
    
    switch (offlineState.networkQuality) {
      case 'excellent': return colors.success;
      case 'good': return colors.info;
      case 'fair': return colors.warning;
      case 'poor': return colors.error;
      default: return colors.textSecondary;
    }
  };

  // 获取状态图标
  const getStatusIcon = () => {
    if (offlineState.isOfflineMode) return '📡';
    
    switch (offlineState.networkQuality) {
      case 'excellent': return '📶';
      case 'good': return '📶';
      case 'fair': return '📶';
      case 'poor': return '📶';
      default: return '❓';
    }
  };

  // 获取状态文本
  const getStatusText = () => {
    if (offlineState.isOfflineMode) return '离线模式';
    
    switch (offlineState.networkQuality) {
      case 'excellent': return '网络优秀';
      case 'good': return '网络良好';
      case 'fair': return '网络一般';
      case 'poor': return '网络较差';
      default: return '网络状态未知';
    }
  };

  // 处理同步
  const handleSync = async () => {
    setShowSyncAnimation(true);
    try {
      await forceSync();
    } finally {
      setTimeout(() => setShowSyncAnimation(false), 1000);
    }
  };

  // 位置样式
  const getPositionStyles = (): React.CSSProperties => {
    const baseStyles: React.CSSProperties = {
      position: 'fixed',
      zIndex: 1000,
      maxWidth: compact ? '200px' : '300px',
    };

    switch (position) {
      case 'top-left':
        return { ...baseStyles, top: spacing.md, left: spacing.md };
      case 'top-right':
        return { ...baseStyles, top: spacing.md, right: spacing.md };
      case 'bottom-left':
        return { ...baseStyles, bottom: spacing.md, left: spacing.md };
      case 'bottom-right':
        return { ...baseStyles, bottom: spacing.md, right: spacing.md };
      default:
        return { ...baseStyles, bottom: spacing.md, right: spacing.md };
    }
  };

  const containerStyles: React.CSSProperties = {
    ...getPositionStyles(),
    backgroundColor: colors.surface,
    border: `1px solid ${colors.border}`,
    borderRadius: borderRadius.md,
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    backdropFilter: 'blur(8px)',
    overflow: 'hidden',
    ...style,
  };

  const headerStyles: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: spacing.sm,
    backgroundColor: getStatusColor() + '10',
    borderBottom: isExpanded ? `1px solid ${colors.border}` : 'none',
    cursor: compact ? 'pointer' : 'default',
  };

  const statusStyles: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    gap: spacing.xs,
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: getStatusColor(),
  };

  const detailsStyles: React.CSSProperties = {
    padding: spacing.sm,
    fontSize: typography.fontSize.xs,
    color: colors.textSecondary,
  };

  const metricStyles: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs / 2,
  };

  const actionButtonStyles: React.CSSProperties = {
    padding: `${spacing.xs} ${spacing.sm}`,
    fontSize: typography.fontSize.xs,
    minHeight: 'auto',
  };

  return (
    <Transition
      show={true}
      config={{
        type: 'slide-up',
        duration: 300,
        easing: Easing.easeOutCubic,
      }}
    >
      <div className={className} style={containerStyles}>
        {/* 状态头部 */}
        <div 
          style={headerStyles} 
          onClick={compact ? () => setIsExpanded(!isExpanded) : undefined}
        >
          <div style={statusStyles}>
            <span style={{ fontSize: '16px' }}>{getStatusIcon()}</span>
            <span>{getStatusText()}</span>
            {showSyncAnimation && (
              <div style={{
                width: '12px',
                height: '12px',
                border: `2px solid ${colors.border}`,
                borderTop: `2px solid ${getStatusColor()}`,
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
              }} />
            )}
          </div>
          
          {compact && (
            <span style={{ 
              color: colors.textSecondary, 
              fontSize: '12px',
              transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
              transition: 'transform 0.2s ease',
            }}>
              ▼
            </span>
          )}
        </div>

        {/* 详细信息 */}
        <Transition
          show={isExpanded && showDetails}
          config={{
            type: 'slide-down',
            duration: 200,
            easing: Easing.easeOutCubic,
          }}
        >
          <div style={detailsStyles}>
            {/* 网络信息 */}
            <div style={{ marginBottom: spacing.sm }}>
              <div style={metricStyles}>
                <span>连接类型:</span>
                <span style={{ fontWeight: 'bold' }}>
                  {networkStatus.connectionType}
                </span>
              </div>
              
              <div style={metricStyles}>
                <span>网络速度:</span>
                <span style={{ fontWeight: 'bold' }}>
                  {networkStatus.effectiveType}
                </span>
              </div>
              
              {networkStatus.downlink > 0 && (
                <div style={metricStyles}>
                  <span>下行带宽:</span>
                  <span style={{ fontWeight: 'bold' }}>
                    {networkStatus.downlink}Mbps
                  </span>
                </div>
              )}
              
              {networkStatus.rtt > 0 && (
                <div style={metricStyles}>
                  <span>延迟:</span>
                  <span style={{ fontWeight: 'bold' }}>
                    {networkStatus.rtt}ms
                  </span>
                </div>
              )}
            </div>

            {/* 离线操作统计 */}
            {(pendingActions > 0 || syncedActions > 0 || failedActions > 0) && (
              <div style={{ marginBottom: spacing.sm }}>
                <div style={{ 
                  fontSize: typography.fontSize.xs, 
                  fontWeight: 'bold',
                  marginBottom: spacing.xs,
                  color: colors.text,
                }}>
                  操作队列
                </div>
                
                {pendingActions > 0 && (
                  <div style={metricStyles}>
                    <span>待同步:</span>
                    <span style={{ 
                      fontWeight: 'bold',
                      color: colors.warning,
                    }}>
                      {pendingActions}
                    </span>
                  </div>
                )}
                
                {syncedActions > 0 && (
                  <div style={metricStyles}>
                    <span>已同步:</span>
                    <span style={{ 
                      fontWeight: 'bold',
                      color: colors.success,
                    }}>
                      {syncedActions}
                    </span>
                  </div>
                )}
                
                {failedActions > 0 && (
                  <div style={metricStyles}>
                    <span>失败:</span>
                    <span style={{ 
                      fontWeight: 'bold',
                      color: colors.error,
                    }}>
                      {failedActions}
                    </span>
                  </div>
                )}
              </div>
            )}

            {/* 操作按钮 */}
            <div style={{ 
              display: 'flex', 
              gap: spacing.xs, 
              flexWrap: 'wrap',
            }}>
              {pendingActions > 0 && !offlineState.isOfflineMode && (
                <Button
                  onClick={handleSync}
                  variant="primary"
                  size="sm"
                  style={actionButtonStyles}
                  disabled={showSyncAnimation}
                >
                  {showSyncAnimation ? '同步中...' : '立即同步'}
                </Button>
              )}
              
              {(pendingActions > 0 || syncedActions > 0 || failedActions > 0) && (
                <Button
                  onClick={clearActions}
                  variant="outline"
                  size="sm"
                  style={actionButtonStyles}
                >
                  清除队列
                </Button>
              )}
            </div>

            {/* 最后同步时间 */}
            {offlineState.lastSyncTime > 0 && (
              <div style={{ 
                marginTop: spacing.sm,
                fontSize: typography.fontSize.xs,
                color: colors.textSecondary,
                textAlign: 'center',
              }}>
                最后同步: {new Date(offlineState.lastSyncTime).toLocaleTimeString()}
              </div>
            )}
          </div>
        </Transition>

        <style>
          {`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          `}
        </style>
      </div>
    </Transition>
  );
};

// 简化的网络状态指示器
export interface NetworkStatusBadgeProps {
  showText?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  style?: React.CSSProperties;
}

export const NetworkStatusBadge: React.FC<NetworkStatusBadgeProps> = ({
  showText = false,
  size = 'md',
  className,
  style,
}) => {
  const { colors, spacing, typography } = useTheme();
  const offlineState = useOffline();

  const sizeStyles = {
    sm: { padding: spacing.xs, fontSize: typography.fontSize.xs },
    md: { padding: spacing.sm, fontSize: typography.fontSize.sm },
    lg: { padding: spacing.md, fontSize: typography.fontSize.md },
  };

  const badgeStyles: React.CSSProperties = {
    display: 'inline-flex',
    alignItems: 'center',
    gap: spacing.xs,
    backgroundColor: offlineState.isOfflineMode ? colors.error : colors.success,
    color: 'white',
    borderRadius: '12px',
    fontWeight: 'bold',
    ...sizeStyles[size],
    ...style,
  };

  return (
    <div className={className} style={badgeStyles}>
      <span>{offlineState.isOfflineMode ? '📡' : '📶'}</span>
      {showText && (
        <span>
          {offlineState.isOfflineMode ? '离线' : '在线'}
        </span>
      )}
    </div>
  );
};
