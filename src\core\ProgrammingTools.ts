/**
 * Programming Tools - 编程工具集成器
 * 
 * 提供代码重构、测试生成、代码解释、终端命令执行等编程工具
 */

import * as vscode from 'vscode';
import { EventBus } from './EventBus';
import { CodeContextExtractor, CodeContext } from './CodeContextExtractor';
import { WorkspaceAwareness } from './WorkspaceAwareness';

export interface ToolResult {
  success: boolean;
  content: string;
  metadata?: any;
  error?: string;
}

export interface RefactorOptions {
  type: 'extract-function' | 'extract-variable' | 'rename' | 'optimize' | 'format';
  target?: string;
  newName?: string;
}

export interface TestGenerationOptions {
  framework?: string;
  coverage?: 'basic' | 'comprehensive';
  includeEdgeCases?: boolean;
}

export class ProgrammingTools {
  private eventBus: EventBus;
  private contextExtractor: CodeContextExtractor;
  private workspaceAwareness: WorkspaceAwareness;

  constructor(
    eventBus: EventBus,
    contextExtractor: CodeContextExtractor,
    workspaceAwareness: WorkspaceAwareness
  ) {
    this.eventBus = eventBus;
    this.contextExtractor = contextExtractor;
    this.workspaceAwareness = workspaceAwareness;
  }

  /**
   * 执行工具调用
   */
  async executeTool(toolName: string, parameters: any): Promise<ToolResult> {
    switch (toolName) {
      case 'refactor':
        return this.refactorCode(parameters);
      case 'generateTests':
        return this.generateTests(parameters);
      case 'explainCode':
        return this.explainCode();
      case 'executeTerminal':
        return this.executeTerminalCommand(parameters.command);
      case 'formatCode':
        return this.formatCode();
      case 'getOptimizations':
        return this.getOptimizationSuggestions();
      default:
        return {
          success: false,
          content: '',
          error: `Unknown tool: ${toolName}`
        };
    }
  }

  /**
   * 代码重构工具
   */
  async refactorCode(options: RefactorOptions): Promise<ToolResult> {
    try {
      await this.eventBus.emit({
        type: 'tools.refactor_started',
        source: 'ProgrammingTools',
        refactorType: options.type
      });

      const context = await this.contextExtractor.getSelectionContext();
      if (!context) {
        return {
          success: false,
          content: '',
          error: '请先选择要重构的代码'
        };
      }

      const projectInfo = this.workspaceAwareness.getProjectInfo();
      const refactoredCode = await this.generateRefactoredCode(context, options, projectInfo);

      await this.eventBus.emit({
        type: 'tools.refactor_completed',
        source: 'ProgrammingTools',
        refactorType: options.type,
        success: true
      });

      return {
        success: true,
        content: refactoredCode,
        metadata: {
          originalLines: context.lineRange,
          language: context.language,
          refactorType: options.type
        }
      };

    } catch (error) {
      await this.eventBus.emit({
        type: 'tools.refactor_failed',
        source: 'ProgrammingTools',
        error: (error as Error).message
      });

      return {
        success: false,
        content: '',
        error: (error as Error).message
      };
    }
  }

  /**
   * 生成测试代码
   */
  async generateTests(options: TestGenerationOptions = {}): Promise<ToolResult> {
    try {
      await this.eventBus.emit({
        type: 'tools.test_generation_started',
        source: 'ProgrammingTools'
      });

      const context = await this.contextExtractor.getFileContext();
      if (!context) {
        return {
          success: false,
          content: '',
          error: '无法获取当前文件上下文'
        };
      }

      const projectInfo = this.workspaceAwareness.getProjectInfo();
      const testFramework = options.framework || this.detectTestFramework(projectInfo);
      
      const testCode = await this.generateTestCode(context, testFramework, options);

      await this.eventBus.emit({
        type: 'tools.test_generation_completed',
        source: 'ProgrammingTools',
        framework: testFramework,
        success: true
      });

      return {
        success: true,
        content: testCode,
        metadata: {
          framework: testFramework,
          language: context.language,
          coverage: options.coverage || 'basic'
        }
      };

    } catch (error) {
      await this.eventBus.emit({
        type: 'tools.test_generation_failed',
        source: 'ProgrammingTools',
        error: (error as Error).message
      });

      return {
        success: false,
        content: '',
        error: (error as Error).message
      };
    }
  }

  /**
   * 代码解释工具
   */
  async explainCode(): Promise<ToolResult> {
    try {
      await this.eventBus.emit({
        type: 'tools.explanation_started',
        source: 'ProgrammingTools'
      });

      const context = await this.contextExtractor.getSelectionContext() || 
                     await this.contextExtractor.getFileContext();
      
      if (!context) {
        return {
          success: false,
          content: '',
          error: '无法获取代码上下文'
        };
      }

      const projectInfo = this.workspaceAwareness.getProjectInfo();
      const explanation = await this.generateCodeExplanation(context, projectInfo);

      await this.eventBus.emit({
        type: 'tools.explanation_completed',
        source: 'ProgrammingTools',
        success: true
      });

      return {
        success: true,
        content: explanation,
        metadata: {
          language: context.language,
          type: context.type,
          lineCount: context.content.split('\n').length
        }
      };

    } catch (error) {
      await this.eventBus.emit({
        type: 'tools.explanation_failed',
        source: 'ProgrammingTools',
        error: (error as Error).message
      });

      return {
        success: false,
        content: '',
        error: (error as Error).message
      };
    }
  }

  /**
   * 执行终端命令
   */
  async executeTerminalCommand(command: string): Promise<ToolResult> {
    try {
      await this.eventBus.emit({
        type: 'tools.terminal_execution_started',
        source: 'ProgrammingTools',
        command
      });

      // 安全检查
      if (!this.isCommandSafe(command)) {
        return {
          success: false,
          content: '',
          error: '出于安全考虑，不允许执行此命令'
        };
      }

      const terminal = vscode.window.createTerminal('AI Agent');
      terminal.show();
      terminal.sendText(command);

      await this.eventBus.emit({
        type: 'tools.terminal_execution_completed',
        source: 'ProgrammingTools',
        command,
        success: true
      });

      return {
        success: true,
        content: `命令已在终端中执行: ${command}`,
        metadata: {
          command,
          terminalName: 'AI Agent'
        }
      };

    } catch (error) {
      await this.eventBus.emit({
        type: 'tools.terminal_execution_failed',
        source: 'ProgrammingTools',
        command,
        error: (error as Error).message
      });

      return {
        success: false,
        content: '',
        error: (error as Error).message
      };
    }
  }

  /**
   * 代码格式化
   */
  async formatCode(): Promise<ToolResult> {
    try {
      const editor = vscode.window.activeTextEditor;
      if (!editor) {
        return {
          success: false,
          content: '',
          error: '没有活动的编辑器'
        };
      }

      await vscode.commands.executeCommand('editor.action.formatDocument');

      return {
        success: true,
        content: '代码已格式化',
        metadata: {
          language: editor.document.languageId,
          fileName: editor.document.fileName
        }
      };

    } catch (error) {
      return {
        success: false,
        content: '',
        error: (error as Error).message
      };
    }
  }

  /**
   * 代码优化建议
   */
  async getOptimizationSuggestions(): Promise<ToolResult> {
    try {
      const context = await this.contextExtractor.getSelectionContext() || 
                     await this.contextExtractor.getFileContext();
      
      if (!context) {
        return {
          success: false,
          content: '',
          error: '无法获取代码上下文'
        };
      }

      const projectInfo = this.workspaceAwareness.getProjectInfo();
      const suggestions = await this.generateOptimizationSuggestions(context, projectInfo);

      return {
        success: true,
        content: suggestions,
        metadata: {
          language: context.language,
          analysisType: 'optimization'
        }
      };

    } catch (error) {
      return {
        success: false,
        content: '',
        error: (error as Error).message
      };
    }
  }

  /**
   * 生成重构后的代码
   */
  private async generateRefactoredCode(
    context: CodeContext,
    options: RefactorOptions,
    projectInfo: any
  ): Promise<string> {
    // 这里应该调用LLM来生成重构后的代码
    // 为了演示，返回一个模板
    const prompt = this.buildRefactorPrompt(context, options, projectInfo);
    
    // TODO: 集成ChatService来调用LLM
    return `// 重构后的代码 (${options.type})
// 原始代码:
${context.content}

// 重构建议:
// 1. 提取函数以提高可读性
// 2. 使用更具描述性的变量名
// 3. 添加适当的错误处理
// 4. 优化性能

// 注意: 这是一个示例输出，实际实现需要集成LLM服务`;
  }

  /**
   * 生成测试代码
   */
  private async generateTestCode(
    context: CodeContext,
    framework: string,
    options: TestGenerationOptions
  ): Promise<string> {
    const prompt = this.buildTestPrompt(context, framework, options);
    
    // TODO: 集成ChatService来调用LLM
    return `// 生成的测试代码 (${framework})
// 测试文件: ${context.filePath.replace(/\.(ts|js)$/, '.test.$1')}

import { describe, it, expect } from '${framework}';
// 导入被测试的模块

describe('${context.filePath}', () => {
  it('should work correctly', () => {
    // 测试用例
    expect(true).toBe(true);
  });
  
  // 更多测试用例...
});

// 注意: 这是一个示例输出，实际实现需要集成LLM服务`;
  }

  /**
   * 生成代码解释
   */
  private async generateCodeExplanation(
    context: CodeContext,
    projectInfo: any
  ): Promise<string> {
    const prompt = this.buildExplanationPrompt(context, projectInfo);
    
    // TODO: 集成ChatService来调用LLM
    return `# 代码解释

## 概述
这段${context.language}代码位于 \`${context.filePath}\`

## 功能分析
${context.metadata?.functions?.length ? 
  `### 函数列表:\n${context.metadata.functions.map(f => `- ${f}`).join('\n')}` : ''}

${context.metadata?.classes?.length ? 
  `### 类列表:\n${context.metadata.classes.map(c => `- ${c}`).join('\n')}` : ''}

## 代码逻辑
这段代码的主要功能是...

## 改进建议
1. 考虑添加错误处理
2. 优化性能
3. 改进代码可读性

注意: 这是一个示例输出，实际实现需要集成LLM服务`;
  }

  /**
   * 生成优化建议
   */
  private async generateOptimizationSuggestions(
    context: CodeContext,
    projectInfo: any
  ): Promise<string> {
    // TODO: 集成ChatService来调用LLM
    return `# 代码优化建议

## 性能优化
1. 考虑使用更高效的算法
2. 减少不必要的计算
3. 优化内存使用

## 代码质量
1. 提高代码可读性
2. 增强错误处理
3. 添加适当的注释

## 最佳实践
1. 遵循${context.language}编码规范
2. 使用现代语言特性
3. 考虑安全性问题

注意: 这是一个示例输出，实际实现需要集成LLM服务`;
  }

  /**
   * 检测测试框架
   */
  private detectTestFramework(projectInfo: any): string {
    if (!projectInfo) return 'jest';
    
    const testFrameworks = projectInfo.testFrameworks || [];
    if (testFrameworks.includes('Jest')) return 'jest';
    if (testFrameworks.includes('Vitest')) return 'vitest';
    if (testFrameworks.includes('Mocha')) return 'mocha';
    if (testFrameworks.includes('pytest')) return 'pytest';
    
    // 根据语言推断默认框架
    const languages = projectInfo.languages || [];
    if (languages.includes('Python')) return 'pytest';
    if (languages.includes('Java')) return 'junit';
    
    return 'jest'; // 默认
  }

  /**
   * 构建重构提示
   */
  private buildRefactorPrompt(
    context: CodeContext,
    options: RefactorOptions,
    projectInfo: any
  ): string {
    return `请对以下${context.language}代码进行${options.type}重构:

代码:
\`\`\`${context.language}
${context.content}
\`\`\`

项目信息: ${projectInfo ? JSON.stringify(projectInfo, null, 2) : '无'}
重构类型: ${options.type}
${options.newName ? `新名称: ${options.newName}` : ''}

请提供重构后的代码和解释。`;
  }

  /**
   * 构建测试提示
   */
  private buildTestPrompt(
    context: CodeContext,
    framework: string,
    options: TestGenerationOptions
  ): string {
    return `请为以下${context.language}代码生成${framework}测试:

代码:
\`\`\`${context.language}
${context.content}
\`\`\`

测试要求:
- 框架: ${framework}
- 覆盖率: ${options.coverage || 'basic'}
- 包含边界情况: ${options.includeEdgeCases ? '是' : '否'}

请生成完整的测试文件。`;
  }

  /**
   * 构建解释提示
   */
  private buildExplanationPrompt(context: CodeContext, projectInfo: any): string {
    return `请解释以下${context.language}代码:

代码:
\`\`\`${context.language}
${context.content}
\`\`\`

文件路径: ${context.filePath}
项目信息: ${projectInfo ? JSON.stringify(projectInfo, null, 2) : '无'}

请提供详细的代码解释，包括功能、逻辑和改进建议。`;
  }

  /**
   * 检查命令安全性
   */
  private isCommandSafe(command: string): boolean {
    const dangerousCommands = [
      'rm -rf',
      'del /f',
      'format',
      'shutdown',
      'reboot',
      'sudo rm',
      'dd if=',
      '> /dev/null',
      'chmod 777',
      'curl | sh',
      'wget | sh'
    ];

    const lowerCommand = command.toLowerCase();
    return !dangerousCommands.some(dangerous => lowerCommand.includes(dangerous));
  }
}
