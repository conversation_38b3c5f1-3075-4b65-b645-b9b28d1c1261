/**
 * VS Code Adapter - VS Code适配器主类
 * 
 * 提供VS Code API的统一封装和抽象
 */

import * as vscode from 'vscode';
import { EventBus } from '@/core/EventBus';
import { CommandRegistry } from './CommandRegistry';
import { WebviewManager } from './WebviewManager';
import { EventTranslator } from './EventTranslator';
import { 
  IVSCodeAdapterExtended, 
  WebviewConfig, 
  IWebview, 
  CommandHandler,
  DocumentChangeHandler,
  SelectionChangeHandler,
  WorkspaceChangeHandler,
  StatusBarConfig,
  IStatusBarItem,
  QuickPickConfig,
  InputBoxConfig,
  ProgressConfig,
  DiagnosticInfo
} from './interfaces';

class StatusBarItemWrapper implements IStatusBarItem {
  public readonly item: vscode.StatusBarItem;

  constructor(item: vscode.StatusBarItem) {
    this.item = item;
  }

  update(config: Partial<StatusBarConfig>): void {
    if (config.text !== undefined) this.item.text = config.text;
    if (config.tooltip !== undefined) this.item.tooltip = config.tooltip;
    if (config.command !== undefined) this.item.command = config.command;
    if (config.color !== undefined) this.item.color = config.color;
    if (config.backgroundColor !== undefined) this.item.backgroundColor = config.backgroundColor;
  }

  show(): void {
    this.item.show();
  }

  hide(): void {
    this.item.hide();
  }

  dispose(): void {
    this.item.dispose();
  }
}

export class VSCodeAdapter implements IVSCodeAdapterExtended {
  private commandRegistry: CommandRegistry;
  private webviewManager: WebviewManager;
  private eventTranslator: EventTranslator;
  private eventBus: EventBus;
  private extensionContext: vscode.ExtensionContext;
  private disposables: vscode.Disposable[] = [];

  constructor(eventBus: EventBus, extensionContext: vscode.ExtensionContext) {
    this.eventBus = eventBus;
    this.extensionContext = extensionContext;
    
    this.commandRegistry = new CommandRegistry(eventBus);
    this.webviewManager = new WebviewManager(eventBus, extensionContext);
    this.eventTranslator = new EventTranslator(eventBus);
  }

  // 命令管理
  registerCommand(id: string, handler: CommandHandler): vscode.Disposable {
    return this.commandRegistry.registerCommand(id, handler);
  }

  executeCommand<T = any>(command: string, ...args: any[]): Thenable<T> {
    return this.commandRegistry.executeCommand<T>(command, ...args);
  }

  // UI管理
  createWebview(config: WebviewConfig): IWebview {
    return this.webviewManager.createWebview(config);
  }

  showInformationMessage(message: string, ...items: string[]): Thenable<string | undefined> {
    return vscode.window.showInformationMessage(message, ...items);
  }

  showWarningMessage(message: string, ...items: string[]): Thenable<string | undefined> {
    return vscode.window.showWarningMessage(message, ...items);
  }

  showErrorMessage(message: string, ...items: string[]): Thenable<string | undefined> {
    return vscode.window.showErrorMessage(message, ...items);
  }

  showStatusMessage(message: string, hideAfterTimeout?: number): vscode.Disposable {
    return vscode.window.setStatusBarMessage(message, hideAfterTimeout);
  }

  // 编辑器管理
  getActiveTextEditor(): vscode.TextEditor | undefined {
    return vscode.window.activeTextEditor;
  }

  showTextDocument(document: vscode.TextDocument, options?: vscode.TextDocumentShowOptions): Thenable<vscode.TextEditor> {
    return vscode.window.showTextDocument(document, options);
  }

  // 工作区管理
  getWorkspaceFolders(): readonly vscode.WorkspaceFolder[] | undefined {
    return vscode.workspace.workspaceFolders;
  }

  getConfiguration(section?: string, scope?: vscode.ConfigurationScope): vscode.WorkspaceConfiguration {
    return vscode.workspace.getConfiguration(section, scope);
  }

  // 文件系统
  readFile(uri: vscode.Uri): Thenable<Uint8Array> {
    return vscode.workspace.fs.readFile(uri);
  }

  writeFile(uri: vscode.Uri, content: Uint8Array): Thenable<void> {
    return vscode.workspace.fs.writeFile(uri, content);
  }

  // 事件监听
  onDidChangeActiveTextEditor(handler: (editor: vscode.TextEditor | undefined) => void): vscode.Disposable {
    return vscode.window.onDidChangeActiveTextEditor(handler);
  }

  onDidChangeTextDocument(handler: DocumentChangeHandler): vscode.Disposable {
    return this.eventTranslator.onDidChangeTextDocument(handler);
  }

  onDidChangeTextEditorSelection(handler: SelectionChangeHandler): vscode.Disposable {
    return this.eventTranslator.onDidChangeTextEditorSelection(handler);
  }

  onDidChangeWorkspaceFolders(handler: WorkspaceChangeHandler): vscode.Disposable {
    return this.eventTranslator.onDidChangeWorkspaceFolders(handler);
  }

  // 状态栏管理
  createStatusBarItem(config: StatusBarConfig): IStatusBarItem {
    const item = vscode.window.createStatusBarItem(
      config.alignment || vscode.StatusBarAlignment.Right,
      config.priority
    );
    
    const wrapper = new StatusBarItemWrapper(item);
    wrapper.update(config);
    
    return wrapper;
  }

  // 快速选择和输入
  showQuickPick(config: QuickPickConfig): Thenable<vscode.QuickPickItem | undefined> {
    return vscode.window.showQuickPick(config.items, config.options);
  }

  showInputBox(config: InputBoxConfig): Thenable<string | undefined> {
    return vscode.window.showInputBox(config.options);
  }

  // 进度显示
  withProgress<R>(
    config: ProgressConfig,
    task: (progress: vscode.Progress<{ message?: string; increment?: number }>, token: vscode.CancellationToken) => Thenable<R>
  ): Thenable<R> {
    return vscode.window.withProgress(
      {
        location: config.location,
        title: config.title,
        cancellable: config.cancellable,
      },
      task
    );
  }

  // 诊断管理
  setDiagnostics(collection: vscode.DiagnosticCollection, diagnostics: DiagnosticInfo[]): void {
    collection.clear();
    for (const diagnostic of diagnostics) {
      collection.set(diagnostic.uri, diagnostic.diagnostics);
    }
  }

  // 树视图
  createTreeView<T>(viewId: string, options: vscode.TreeViewOptions<T>): vscode.TreeView<T> {
    return vscode.window.createTreeView(viewId, options);
  }

  // 终端管理
  createTerminal(options?: vscode.TerminalOptions): vscode.Terminal {
    return vscode.window.createTerminal(options);
  }

  // 输出通道
  createOutputChannel(name: string): vscode.OutputChannel {
    return vscode.window.createOutputChannel(name);
  }

  // 获取适配器统计信息
  getStats(): {
    commands: any;
    webviews: any;
    events: any;
  } {
    return {
      commands: this.commandRegistry.getStats(),
      webviews: this.webviewManager.getStats(),
      events: this.eventTranslator.getStats(),
    };
  }

  // 获取扩展上下文
  getExtensionContext(): vscode.ExtensionContext {
    return this.extensionContext;
  }

  // 获取命令注册器
  getCommandRegistry(): CommandRegistry {
    return this.commandRegistry;
  }

  // 获取Webview管理器
  getWebviewManager(): WebviewManager {
    return this.webviewManager;
  }

  // 获取事件转换器
  getEventTranslator(): EventTranslator {
    return this.eventTranslator;
  }

  // 生命周期管理
  dispose(): void {
    this.disposables.forEach(disposable => disposable.dispose());
    this.disposables = [];
    
    this.commandRegistry.dispose();
    this.webviewManager.dispose();
    this.eventTranslator.dispose();
  }
}
