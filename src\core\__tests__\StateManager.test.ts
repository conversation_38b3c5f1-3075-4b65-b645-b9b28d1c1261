/**
 * StateManager Tests
 */

import { StateManager } from '../StateManager';
import { AppState } from '@/types';

describe('StateManager', () => {
  let stateManager: StateManager;

  beforeEach(() => {
    stateManager = new StateManager();
  });

  describe('initialization', () => {
    it('should create with default state', () => {
      const state = stateManager.getState();
      
      expect(state.system.isInitialized).toBe(false);
      expect(state.system.isLoading).toBe(false);
      expect(state.system.error).toBeNull();
      expect(state.config.provider).toBe('openai');
      expect(state.conversation.messages).toEqual([]);
    });

    it('should create with partial initial state', () => {
      const manager = new StateManager();
      const state = manager.getState();

      expect(state.config.provider).toBe('openai');
      expect(state.system.isInitialized).toBe(false);
    });
  });

  describe('state updates', () => {
    it('should update state with partial updates', () => {
      stateManager.updateSystemState({ isLoading: true });

      const state = stateManager.getState();
      expect(state.system.isLoading).toBe(true);
      expect(state.system.isInitialized).toBe(false); // 其他值保持不变
    });

    it('should update state with function updater', () => {
      stateManager.updateSystemState({ isLoading: true });

      const state = stateManager.getState();
      expect(state.system.isLoading).toBe(true);
    });

    it('should update specific state sections', () => {
      stateManager.updateSystemState({ isInitialized: true });
      stateManager.updateConfigState({ model: 'gpt-4' });

      const state = stateManager.getState();
      expect(state.system.isInitialized).toBe(true);
      expect(state.config.model).toBe('gpt-4');
    });
  });

  describe('state subscriptions', () => {
    it('should notify listeners on state changes', () => {
      const listener = jest.fn();
      const unsubscribe = stateManager.subscribe(listener);

      stateManager.updateSystemState({ isLoading: true });

      expect(listener).toHaveBeenCalledTimes(1);
      expect(listener).toHaveBeenCalledWith(expect.objectContaining({
        system: expect.objectContaining({ isLoading: true }),
      }));

      unsubscribe();
    });

    it('should support selector-based subscriptions', () => {
      const selector = (state: AppState) => state.system.isLoading;
      const listener = jest.fn();
      
      const unsubscribe = stateManager.subscribeWithSelector(selector, listener);

      stateManager.updateSystemState({ isLoading: true });

      expect(listener).toHaveBeenCalledWith(true);

      unsubscribe();
    });

    it('should unsubscribe correctly', () => {
      const listener = jest.fn();
      const unsubscribe = stateManager.subscribe(listener);

      unsubscribe();
      stateManager.updateSystemState({ isLoading: true });

      expect(listener).not.toHaveBeenCalled();
    });
  });

  describe('state selectors', () => {
    it('should get state by selector', () => {
      const selector = (state: AppState) => state.config.model;
      
      stateManager.updateConfigState({ model: 'gpt-4' });
      const model = stateManager.getStateBySelector(selector);

      expect(model).toBe('gpt-4');
    });
  });

  describe('state persistence', () => {
    it('should create and restore from snapshot', () => {
      stateManager.updateSystemState({ isInitialized: true });
      stateManager.updateConfigState({ model: 'gpt-4' });

      const snapshot = stateManager.getSnapshot();
      expect(typeof snapshot).toBe('string');

      const newManager = new StateManager();
      newManager.restoreFromSnapshot(snapshot);

      const restoredState = newManager.getState();
      expect(restoredState.system.isInitialized).toBe(true);
      expect(restoredState.config.model).toBe('gpt-4');
    });

    it('should handle invalid snapshot gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      stateManager.restoreFromSnapshot('invalid json');
      
      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });
  });

  describe('state reset', () => {
    it('should reset to default state', () => {
      stateManager.updateSystemState({ isInitialized: true });
      stateManager.reset();

      const state = stateManager.getState();
      expect(state.system.isInitialized).toBe(false);
    });

    it('should reset with new initial state', () => {
      stateManager.updateConfigState({ model: 'gpt-4' });
      stateManager.reset();

      const state = stateManager.getState();
      expect(state.config.model).toBe('gpt-4o'); // 回到默认值
    });
  });

  describe('error handling', () => {
    it('should handle listener errors gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      const errorListener = jest.fn(() => {
        throw new Error('Listener error');
      });
      const normalListener = jest.fn();

      stateManager.subscribe(errorListener);
      stateManager.subscribe(normalListener);

      stateManager.updateSystemState({ isLoading: true });

      expect(consoleSpy).toHaveBeenCalled();
      expect(normalListener).toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });
  });
});
