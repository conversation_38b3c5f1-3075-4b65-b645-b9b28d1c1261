/**
 * RAG System - RAG系统集成器
 * 
 * 企业级RAG检索系统的核心集成组件，统一管理索引、搜索和知识库
 */

import * as vscode from 'vscode';
import { EventBus } from './EventBus';
import { RAGIndexManager } from './RAGIndexManager';
import { SemanticSearchEngine, SearchQuery, SearchResult } from './SemanticSearchEngine';
import { KnowledgeBaseManager, KnowledgeQuery, KnowledgeItem } from './KnowledgeBaseManager';
import { EnhancedCodeContext } from './EnhancedCodeContextExtractor';
import { SemanticContextIntegrator, SemanticSearchQuery, SemanticSearchResult } from '../rag/SemanticContextIntegrator';

export interface RAGQuery {
  text: string;
  context?: EnhancedCodeContext;
  includeCode?: boolean;
  includeKnowledge?: boolean;
  searchType?: 'semantic' | 'keyword' | 'hybrid';
  filters?: {
    languages?: string[];
    frameworks?: string[];
    fileTypes?: string[];
    difficulty?: string[];
  };
  limit?: number;
}

export interface RAGResult {
  codeResults: SearchResult[];
  knowledgeResults: KnowledgeItem[];
  combinedScore: number;
  explanation: string;
  suggestions: string[];
}

export interface RAGStats {
  indexStats: any;
  searchStats: any;
  knowledgeStats: any;
  totalQueries: number;
  averageResponseTime: number;
  lastQueryTime: Date;
}

export class RAGSystem {
  private eventBus: EventBus;
  private indexManager: RAGIndexManager;
  private searchEngine: SemanticSearchEngine;
  private knowledgeManager: KnowledgeBaseManager;
  private semanticIntegrator: SemanticContextIntegrator;
  private isInitialized = false;
  private queryStats: {
    totalQueries: number;
    averageResponseTime: number;
    lastQueryTime: Date;
  };

  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
    this.indexManager = new RAGIndexManager(eventBus);
    this.searchEngine = new SemanticSearchEngine(eventBus, this.indexManager);
    this.knowledgeManager = new KnowledgeBaseManager(eventBus);
    this.semanticIntegrator = new SemanticContextIntegrator(eventBus);

    this.queryStats = {
      totalQueries: 0,
      averageResponseTime: 0,
      lastQueryTime: new Date()
    };
  }

  /**
   * 初始化RAG系统
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    const startTime = Date.now();

    await this.eventBus.emit({
      type: 'rag.system_initializing',
      source: 'RAGSystem'
    });

    try {
      // 并行初始化各个组件
      await Promise.all([
        this.indexManager.initialize(),
        this.knowledgeManager.initialize()
      ]);

      // 构建初始索引
      await this.buildInitialIndex();

      this.isInitialized = true;
      const duration = Date.now() - startTime;

      await this.eventBus.emit({
        type: 'rag.system_initialized',
        source: 'RAGSystem',
        duration,
        stats: this.getStats()
      });

      console.log(`RAG System initialized successfully in ${duration}ms`);
    } catch (error) {
      await this.eventBus.emit({
        type: 'rag.system_init_failed',
        source: 'RAGSystem',
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * 执行RAG查询
   */
  async query(ragQuery: RAGQuery): Promise<RAGResult> {
    if (!this.isInitialized) {
      throw new Error('RAG System not initialized');
    }

    const startTime = Date.now();

    try {
      await this.eventBus.emit({
        type: 'rag.query_started',
        source: 'RAGSystem',
        query: ragQuery.text
      });

      // 并行执行代码搜索和知识库搜索
      const [codeResults, knowledgeResults] = await Promise.all([
        this.searchCode(ragQuery),
        this.searchKnowledge(ragQuery)
      ]);

      // 合并和排序结果
      const result = this.combineResults(codeResults, knowledgeResults, ragQuery);

      // 更新统计信息
      this.updateQueryStats(startTime);

      await this.eventBus.emit({
        type: 'rag.query_completed',
        source: 'RAGSystem',
        query: ragQuery.text,
        codeResultCount: result.codeResults.length,
        knowledgeResultCount: result.knowledgeResults.length,
        duration: Date.now() - startTime
      });

      return result;

    } catch (error) {
      await this.eventBus.emit({
        type: 'rag.query_failed',
        source: 'RAGSystem',
        query: ragQuery.text,
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * 获取智能建议
   */
  async getSuggestions(context: EnhancedCodeContext): Promise<string[]> {
    const suggestions: string[] = [];

    try {
      // 基于当前上下文生成建议查询
      const suggestedQueries = this.generateSuggestedQueries(context);
      
      for (const query of suggestedQueries.slice(0, 3)) {
        const result = await this.query({
          text: query,
          context,
          includeCode: true,
          includeKnowledge: true,
          limit: 2
        });

        if (result.codeResults.length > 0 || result.knowledgeResults.length > 0) {
          suggestions.push(query);
        }
      }

      return suggestions;
    } catch (error) {
      console.error('Failed to get suggestions:', error);
      return [];
    }
  }

  /**
   * 更新索引
   */
  async updateIndex(filePaths: string[]): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    await this.indexManager.updateIndex(filePaths);
  }

  /**
   * 重建索引
   */
  async rebuildIndex(): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('RAG System not initialized');
    }

    await this.eventBus.emit({
      type: 'rag.rebuild_index_started',
      source: 'RAGSystem'
    });

    try {
      await this.indexManager.buildFullIndex();
      
      await this.eventBus.emit({
        type: 'rag.rebuild_index_completed',
        source: 'RAGSystem'
      });
    } catch (error) {
      await this.eventBus.emit({
        type: 'rag.rebuild_index_failed',
        source: 'RAGSystem',
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * 同步知识库
   */
  async syncKnowledgeBase(): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('RAG System not initialized');
    }

    await this.knowledgeManager.syncKnowledgeSources();
  }

  /**
   * 获取系统统计信息
   */
  getStats(): RAGStats {
    return {
      indexStats: this.indexManager.getIndexStats(),
      searchStats: this.searchEngine.getSearchStats(),
      knowledgeStats: this.knowledgeManager.getStats(),
      totalQueries: this.queryStats.totalQueries,
      averageResponseTime: this.queryStats.averageResponseTime,
      lastQueryTime: this.queryStats.lastQueryTime
    };
  }

  /**
   * 搜索代码
   */
  private async searchCode(ragQuery: RAGQuery): Promise<SearchResult[]> {
    if (!ragQuery.includeCode) {
      return [];
    }

    const searchQuery: SearchQuery = {
      text: ragQuery.text,
      type: ragQuery.searchType || 'hybrid',
      context: ragQuery.context,
      limit: ragQuery.limit || 10,
      filters: {
        languages: ragQuery.filters?.languages,
        fileTypes: ragQuery.filters?.fileTypes
      }
    };

    return await this.searchEngine.search(searchQuery);
  }

  /**
   * 搜索知识库
   */
  private async searchKnowledge(ragQuery: RAGQuery): Promise<KnowledgeItem[]> {
    if (!ragQuery.includeKnowledge) {
      return [];
    }

    const knowledgeQuery: KnowledgeQuery = {
      query: ragQuery.text,
      language: ragQuery.context?.language,
      framework: this.extractFramework(ragQuery.context),
      difficulty: ragQuery.filters?.difficulty as any,
      limit: ragQuery.limit || 10
    };

    return await this.knowledgeManager.searchKnowledge(knowledgeQuery);
  }

  /**
   * 合并搜索结果
   */
  private combineResults(
    codeResults: SearchResult[],
    knowledgeResults: KnowledgeItem[],
    ragQuery: RAGQuery
  ): RAGResult {
    // 计算综合评分
    const codeScore = codeResults.reduce((sum, result) => sum + result.score, 0);
    const knowledgeScore = knowledgeResults.reduce((sum, item) => sum + (item.metadata.relevanceScore || 0), 0);
    const combinedScore = (codeScore + knowledgeScore) / (codeResults.length + knowledgeResults.length || 1);

    // 生成解释
    const explanation = this.generateExplanation(codeResults, knowledgeResults, ragQuery);

    // 生成建议
    const suggestions = this.generateQuerySuggestions(codeResults, knowledgeResults, ragQuery);

    return {
      codeResults: codeResults.slice(0, ragQuery.limit || 10),
      knowledgeResults: knowledgeResults.slice(0, ragQuery.limit || 10),
      combinedScore,
      explanation,
      suggestions
    };
  }

  /**
   * 生成解释
   */
  private generateExplanation(
    codeResults: SearchResult[],
    knowledgeResults: KnowledgeItem[],
    ragQuery: RAGQuery
  ): string {
    const parts: string[] = [];

    if (codeResults.length > 0) {
      parts.push(`找到 ${codeResults.length} 个相关代码片段`);
    }

    if (knowledgeResults.length > 0) {
      parts.push(`找到 ${knowledgeResults.length} 个相关知识条目`);
    }

    if (parts.length === 0) {
      return '未找到相关结果，请尝试调整搜索关键词';
    }

    let explanation = parts.join('，');

    // 添加搜索类型说明
    if (ragQuery.searchType === 'semantic') {
      explanation += '（基于语义相似度搜索）';
    } else if (ragQuery.searchType === 'keyword') {
      explanation += '（基于关键词匹配搜索）';
    } else {
      explanation += '（基于混合搜索策略）';
    }

    return explanation;
  }

  /**
   * 生成查询建议
   */
  private generateQuerySuggestions(
    codeResults: SearchResult[],
    knowledgeResults: KnowledgeItem[],
    ragQuery: RAGQuery
  ): string[] {
    const suggestions: string[] = [];

    // 基于代码结果生成建议
    if (codeResults.length > 0) {
      const topResult = codeResults[0];
      if (topResult.item.metadata.name) {
        suggestions.push(`${topResult.item.metadata.name} 用法示例`);
        suggestions.push(`${topResult.item.metadata.name} 最佳实践`);
      }
    }

    // 基于知识库结果生成建议
    if (knowledgeResults.length > 0) {
      const topKnowledge = knowledgeResults[0];
      for (const tag of topKnowledge.tags.slice(0, 2)) {
        suggestions.push(`${tag} 教程`);
      }
    }

    // 基于上下文生成建议
    if (ragQuery.context) {
      if (ragQuery.context.language) {
        suggestions.push(`${ragQuery.context.language} 常见问题`);
      }
      
      if (ragQuery.context.metadata?.functions) {
        for (const func of ragQuery.context.metadata.functions.slice(0, 1)) {
          suggestions.push(`${func} 函数优化`);
        }
      }
    }

    return [...new Set(suggestions)].slice(0, 5);
  }

  /**
   * 生成建议查询
   */
  private generateSuggestedQueries(context: EnhancedCodeContext): string[] {
    const queries: string[] = [];

    // 基于错误上下文
    if (context.errorContext) {
      queries.push(context.errorContext.errorType);
      queries.push(`${context.language} ${context.errorContext.errorType}`);
    }

    // 基于函数名
    if (context.metadata?.functions) {
      for (const func of context.metadata.functions.slice(0, 2)) {
        queries.push(`${func} 示例`);
        queries.push(`${func} 最佳实践`);
      }
    }

    // 基于类名
    if (context.metadata?.classes) {
      for (const cls of context.metadata.classes.slice(0, 2)) {
        queries.push(`${cls} 用法`);
        queries.push(`${cls} 设计模式`);
      }
    }

    // 基于语言和框架
    if (context.language) {
      queries.push(`${context.language} 最佳实践`);
      queries.push(`${context.language} 常见错误`);
    }

    return [...new Set(queries)];
  }

  /**
   * 提取框架信息
   */
  private extractFramework(context?: EnhancedCodeContext): string | undefined {
    if (!context || !context.metadata?.imports) {
      return undefined;
    }

    const imports = context.metadata.imports;
    
    // 检测常见框架
    if (imports.some(imp => imp.includes('react'))) {
      return 'react';
    }
    if (imports.some(imp => imp.includes('vue'))) {
      return 'vue';
    }
    if (imports.some(imp => imp.includes('angular'))) {
      return 'angular';
    }
    if (imports.some(imp => imp.includes('express'))) {
      return 'express';
    }

    return undefined;
  }

  /**
   * 构建初始索引
   */
  private async buildInitialIndex(): Promise<void> {
    const indexStats = this.indexManager.getIndexStats();
    
    // 如果索引为空或过期，构建完整索引
    if (indexStats.totalItems === 0) {
      console.log('Building initial index...');
      await this.indexManager.buildFullIndex();
    } else {
      console.log(`Using existing index with ${indexStats.totalItems} items`);
    }
  }

  /**
   * 更新查询统计
   */
  private updateQueryStats(startTime: number): void {
    const duration = Date.now() - startTime;
    
    this.queryStats.totalQueries++;
    this.queryStats.lastQueryTime = new Date();
    
    // 更新平均响应时间
    this.queryStats.averageResponseTime = 
      (this.queryStats.averageResponseTime * (this.queryStats.totalQueries - 1) + duration) / 
      this.queryStats.totalQueries;
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    this.searchEngine.clearCache();
  }

  /**
   * 清理资源
   */
  dispose(): void {
    this.indexManager.dispose();
    this.searchEngine.dispose();
    this.knowledgeManager.dispose();
    this.isInitialized = false;
  }
}
