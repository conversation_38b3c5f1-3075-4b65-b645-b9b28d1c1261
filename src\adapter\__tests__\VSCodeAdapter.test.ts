/**
 * VSCodeAdapter Tests
 */

import { VSCodeAdapter } from '../VSCodeAdapter';
import { EventBus } from '@/core/EventBus';

describe('VSCodeAdapter', () => {
  let adapter: VSCodeAdapter;
  let eventBus: EventBus;
  let mockExtensionContext: any;

  beforeEach(() => {
    eventBus = new EventBus();
    mockExtensionContext = {
      extensionPath: '/test/extension/path',
      subscriptions: [],
    };
    
    adapter = new VSCodeAdapter(eventBus, mockExtensionContext);
  });

  afterEach(() => {
    adapter.dispose();
    eventBus.destroy();
  });

  describe('initialization', () => {
    it('should initialize successfully', () => {
      expect(adapter).toBeDefined();
      expect(adapter.getExtensionContext()).toBe(mockExtensionContext);
    });

    it('should provide access to sub-components', () => {
      expect(adapter.getCommandRegistry()).toBeDefined();
      expect(adapter.getWebviewManager()).toBeDefined();
      expect(adapter.getEventTranslator()).toBeDefined();
    });
  });

  describe('statistics', () => {
    it('should provide adapter statistics', () => {
      const stats = adapter.getStats();
      
      expect(stats).toHaveProperty('commands');
      expect(stats).toHaveProperty('webviews');
      expect(stats).toHaveProperty('events');
    });
  });

  describe('disposal', () => {
    it('should dispose cleanly', () => {
      expect(() => {
        adapter.dispose();
      }).not.toThrow();
    });
  });
});
