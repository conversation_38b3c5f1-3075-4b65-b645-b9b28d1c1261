/**
 * Chat Interface - 主聊天界面组件
 * 
 * 专业级聊天界面，支持Markdown渲染、代码高亮、流式输出
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Message, ToolCall, ToolResult } from '../../types';
import { MessageBubble } from './MessageBubble';
import { InputArea } from './InputArea';
import { ToolCallPanel } from './ToolCallPanel';
import { StatusBar } from './StatusBar';
import { SidePanel } from './SidePanel';
import './ChatInterface.css';

export interface ChatInterfaceProps {
  messages: Message[];
  isLoading: boolean;
  onSendMessage: (message: string, attachments?: any[]) => void;
  onToolCall: (toolCall: ToolCall) => Promise<ToolResult>;
  onClearChat: () => void;
  systemStats?: {
    tokenUsage: number;
    maxTokens: number;
    responseTime: number;
    ragStats: any;
  };
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({
  messages,
  isLoading,
  onSendMessage,
  onToolCall,
  onClearChat,
  systemStats
}) => {
  const [sidePanelOpen, setSidePanelOpen] = useState(true);
  const [sidePanelWidth, setSidePanelWidth] = useState(300);
  const [activeToolCall, setActiveToolCall] = useState<ToolCall | null>(null);
  const [streamingMessage, setStreamingMessage] = useState<string>('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // 自动滚动到底部
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, streamingMessage, scrollToBottom]);

  // 处理键盘快捷键
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'k':
            event.preventDefault();
            onClearChat();
            break;
          case 'b':
            event.preventDefault();
            setSidePanelOpen(!sidePanelOpen);
            break;
          case '/':
            event.preventDefault();
            // 聚焦到输入框
            const inputElement = document.querySelector('.input-area textarea') as HTMLTextAreaElement;
            inputElement?.focus();
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [onClearChat, sidePanelOpen]);

  // 处理侧边栏拖拽调整大小
  const handleSidePanelResize = useCallback((event: React.MouseEvent) => {
    const startX = event.clientX;
    const startWidth = sidePanelWidth;

    const handleMouseMove = (e: MouseEvent) => {
      const newWidth = Math.max(200, Math.min(600, startWidth + (startX - e.clientX)));
      setSidePanelWidth(newWidth);
    };

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [sidePanelWidth]);

  // 处理工具调用
  const handleToolCall = async (toolCall: ToolCall) => {
    setActiveToolCall(toolCall);
    try {
      const result = await onToolCall(toolCall);
      setActiveToolCall(null);
      return result;
    } catch (error) {
      setActiveToolCall(null);
      throw error;
    }
  };

  // 处理流式消息更新
  const handleStreamingUpdate = useCallback((chunk: string) => {
    setStreamingMessage(prev => prev + chunk);
  }, []);

  // 处理流式消息完成
  const handleStreamingComplete = useCallback(() => {
    setStreamingMessage('');
  }, []);

  return (
    <div className="chat-interface">
      {/* 主聊天区域 */}
      <div 
        className="chat-main"
        style={{ 
          marginRight: sidePanelOpen ? `${sidePanelWidth}px` : '0',
          transition: 'margin-right 0.3s ease'
        }}
      >
        {/* 聊天头部 */}
        <div className="chat-header">
          <div className="chat-title">
            <h2>AI Agent</h2>
            <span className="chat-subtitle">专业编程助手</span>
          </div>
          <div className="chat-controls">
            <button
              className="control-button"
              onClick={() => setSidePanelOpen(!sidePanelOpen)}
              title="切换侧边栏 (Ctrl+B)"
            >
              <i className={`icon ${sidePanelOpen ? 'icon-panel-close' : 'icon-panel-open'}`} />
            </button>
            <button
              className="control-button"
              onClick={onClearChat}
              title="清空对话 (Ctrl+K)"
            >
              <i className="icon icon-trash" />
            </button>
          </div>
        </div>

        {/* 消息列表 */}
        <div 
          className="messages-container"
          ref={chatContainerRef}
        >
          <div className="messages-list">
            {messages.map((message, index) => (
              <MessageBubble
                key={index}
                message={message}
                onToolCall={handleToolCall}
                isStreaming={false}
              />
            ))}
            
            {/* 流式消息显示 */}
            {streamingMessage && (
              <MessageBubble
                message={{
                  role: 'assistant',
                  content: streamingMessage,
                  timestamp: new Date()
                }}
                onToolCall={handleToolCall}
                isStreaming={true}
              />
            )}
            
            {/* 加载指示器 */}
            {isLoading && !streamingMessage && (
              <div className="loading-indicator">
                <div className="loading-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
                <span className="loading-text">AI正在思考...</span>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* 输入区域 */}
        <InputArea
          onSendMessage={onSendMessage}
          disabled={isLoading}
          placeholder="输入您的问题... (Ctrl+Enter发送)"
        />
      </div>

      {/* 侧边栏 */}
      {sidePanelOpen && (
        <>
          <div 
            className="side-panel-resizer"
            onMouseDown={handleSidePanelResize}
          />
          <SidePanel
            width={sidePanelWidth}
            systemStats={systemStats}
            activeToolCall={activeToolCall}
          />
        </>
      )}

      {/* 工具调用面板 */}
      {activeToolCall && (
        <ToolCallPanel
          toolCall={activeToolCall}
          onComplete={handleStreamingComplete}
          onUpdate={handleStreamingUpdate}
        />
      )}

      {/* 状态栏 */}
      <StatusBar
        systemStats={systemStats}
        isLoading={isLoading}
        messageCount={messages.length}
      />
    </div>
  );
};
