/**
 * 主题选择器组件
 * 
 * 提供用户友好的主题切换界面，支持VS Code主题同步
 */

import React, { useState } from 'react';
import { useTheme, useThemeToggle } from '../theme/ThemeProvider';
import { Button } from './Button';
import { Transition } from './Transition';
import { Easing } from '../utils/AnimationManager';

export interface ThemeSelectorProps {
  compact?: boolean;
  showLabels?: boolean;
  showSyncOption?: boolean;
  position?: 'horizontal' | 'vertical';
  className?: string;
  style?: React.CSSProperties;
}

export const ThemeSelector: React.FC<ThemeSelectorProps> = ({
  compact = false,
  showLabels = true,
  showSyncOption = true,
  position = 'horizontal',
  className,
  style,
}) => {
  const { 
    theme, 
    themeConfig, 
    setTheme, 
    syncWithVSCode, 
    setSyncWithVSCode, 
    isTransitioning 
  } = useTheme();
  
  const [isExpanded, setIsExpanded] = useState(!compact);

  const themes = [
    { 
      key: 'light' as const, 
      name: '浅色主题', 
      icon: '☀️',
      description: '适合白天使用的明亮主题',
    },
    { 
      key: 'dark' as const, 
      name: '深色主题', 
      icon: '🌙',
      description: '适合夜间使用的暗色主题',
    },
    { 
      key: 'high-contrast' as const, 
      name: '高对比度', 
      icon: '🔆',
      description: '提高可读性的高对比度主题',
    },
  ];

  const containerStyles: React.CSSProperties = {
    display: 'flex',
    flexDirection: position === 'vertical' ? 'column' : 'row',
    gap: themeConfig.spacing.sm,
    alignItems: position === 'vertical' ? 'stretch' : 'center',
    padding: compact ? themeConfig.spacing.xs : themeConfig.spacing.sm,
    backgroundColor: themeConfig.colors.surface,
    borderRadius: themeConfig.borderRadius.md,
    border: `1px solid ${themeConfig.colors.border}`,
    ...style,
  };

  const headerStyles: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: isExpanded ? themeConfig.spacing.sm : 0,
    cursor: compact ? 'pointer' : 'default',
  };

  const titleStyles: React.CSSProperties = {
    fontSize: themeConfig.typography.fontSize.sm,
    fontWeight: themeConfig.typography.fontWeight.medium,
    color: themeConfig.colors.text,
    margin: 0,
  };

  const themeButtonStyles = (isActive: boolean): React.CSSProperties => ({
    display: 'flex',
    alignItems: 'center',
    gap: themeConfig.spacing.xs,
    padding: `${themeConfig.spacing.xs} ${themeConfig.spacing.sm}`,
    backgroundColor: isActive ? themeConfig.colors.primary : 'transparent',
    color: isActive ? '#ffffff' : themeConfig.colors.text,
    border: `1px solid ${isActive ? themeConfig.colors.primary : themeConfig.colors.border}`,
    borderRadius: themeConfig.borderRadius.sm,
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    fontSize: themeConfig.typography.fontSize.sm,
    fontWeight: isActive ? themeConfig.typography.fontWeight.medium : themeConfig.typography.fontWeight.normal,
    opacity: isTransitioning ? 0.7 : 1,
    minWidth: compact ? 'auto' : '120px',
    justifyContent: compact ? 'center' : 'flex-start',
  });

  const syncOptionStyles: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    gap: themeConfig.spacing.xs,
    padding: themeConfig.spacing.xs,
    fontSize: themeConfig.typography.fontSize.xs,
    color: themeConfig.colors.textSecondary,
    borderTop: position === 'vertical' ? `1px solid ${themeConfig.colors.border}` : 'none',
    marginTop: position === 'vertical' ? themeConfig.spacing.sm : 0,
    paddingTop: position === 'vertical' ? themeConfig.spacing.sm : 0,
  };

  const handleThemeChange = (newTheme: typeof theme) => {
    if (isTransitioning) return;
    setTheme(newTheme);
  };

  const handleSyncToggle = () => {
    setSyncWithVSCode(!syncWithVSCode);
  };

  const renderThemeButton = (themeOption: typeof themes[0]) => {
    const isActive = theme === themeOption.key;
    
    return (
      <Transition
        key={themeOption.key}
        show={true}
        config={{
          type: 'scale-fade',
          duration: 200,
          easing: Easing.easeOutCubic,
        }}
      >
        <button
          style={themeButtonStyles(isActive)}
          onClick={() => handleThemeChange(themeOption.key)}
          disabled={isTransitioning || syncWithVSCode}
          title={syncWithVSCode ? 'VS Code主题同步已启用' : themeOption.description}
          aria-label={`切换到${themeOption.name}`}
        >
          <span style={{ fontSize: '16px' }}>{themeOption.icon}</span>
          {(showLabels && !compact) && (
            <span>{themeOption.name}</span>
          )}
          {isActive && (
            <span style={{ 
              marginLeft: 'auto', 
              fontSize: '12px',
              opacity: 0.8,
            }}>
              ✓
            </span>
          )}
        </button>
      </Transition>
    );
  };

  return (
    <div className={className} style={containerStyles}>
      {compact && (
        <div style={headerStyles} onClick={() => setIsExpanded(!isExpanded)}>
          <h4 style={titleStyles}>主题</h4>
          <span style={{ 
            color: themeConfig.colors.textSecondary, 
            fontSize: '12px',
            transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
            transition: 'transform 0.2s ease',
          }}>
            ▼
          </span>
        </div>
      )}
      
      <Transition
        show={isExpanded}
        config={{
          type: 'slide-down',
          duration: 300,
          easing: Easing.easeOutCubic,
        }}
      >
        <div style={{
          display: 'flex',
          flexDirection: position === 'vertical' ? 'column' : 'row',
          gap: themeConfig.spacing.xs,
        }}>
          {themes.map(renderThemeButton)}
        </div>
        
        {showSyncOption && (
          <div style={syncOptionStyles}>
            <label style={{
              display: 'flex',
              alignItems: 'center',
              gap: themeConfig.spacing.xs,
              cursor: 'pointer',
              fontSize: themeConfig.typography.fontSize.xs,
            }}>
              <input
                type="checkbox"
                checked={syncWithVSCode}
                onChange={handleSyncToggle}
                style={{
                  accentColor: themeConfig.colors.primary,
                }}
              />
              <span>与VS Code主题同步</span>
            </label>
            {syncWithVSCode && (
              <span style={{
                marginLeft: 'auto',
                fontSize: '10px',
                color: themeConfig.colors.success,
                display: 'flex',
                alignItems: 'center',
                gap: '2px',
              }}>
                <span style={{ 
                  width: '6px', 
                  height: '6px', 
                  backgroundColor: themeConfig.colors.success,
                  borderRadius: '50%',
                  animation: 'pulse 2s infinite',
                }} />
                已同步
              </span>
            )}
          </div>
        )}
      </Transition>
      
      <style>
        {`
          @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
          }
        `}
      </style>
    </div>
  );
};

// 快速主题切换按钮
export interface QuickThemeToggleProps {
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  showLabel?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export const QuickThemeToggle: React.FC<QuickThemeToggleProps> = ({
  size = 'md',
  showIcon = true,
  showLabel = false,
  className,
  style,
}) => {
  const { theme, toggleTheme, isTransitioning } = useTheme();
  
  const getThemeIcon = () => {
    switch (theme) {
      case 'light': return '☀️';
      case 'dark': return '🌙';
      case 'high-contrast': return '🔆';
      default: return '🎨';
    }
  };

  const getThemeLabel = () => {
    switch (theme) {
      case 'light': return '浅色';
      case 'dark': return '深色';
      case 'high-contrast': return '高对比度';
      default: return '主题';
    }
  };

  return (
    <Button
      onClick={toggleTheme}
      variant="ghost"
      size={size}
      disabled={isTransitioning}
      className={className}
      style={style}
      aria-label="切换主题"
      title="切换主题"
    >
      {showIcon && (
        <span style={{ fontSize: size === 'sm' ? '14px' : size === 'lg' ? '20px' : '16px' }}>
          {getThemeIcon()}
        </span>
      )}
      {showLabel && <span>{getThemeLabel()}</span>}
    </Button>
  );
};

// 主题预览组件
export interface ThemePreviewProps {
  themeName: string;
  themeConfig: any;
  isActive?: boolean;
  onClick?: () => void;
  className?: string;
  style?: React.CSSProperties;
}

export const ThemePreview: React.FC<ThemePreviewProps> = ({
  themeName,
  themeConfig,
  isActive = false,
  onClick,
  className,
  style,
}) => {
  const previewStyles: React.CSSProperties = {
    width: '60px',
    height: '40px',
    borderRadius: '6px',
    border: `2px solid ${isActive ? themeConfig.colors.primary : themeConfig.colors.border}`,
    cursor: 'pointer',
    position: 'relative',
    overflow: 'hidden',
    transition: 'all 0.2s ease',
    ...style,
  };

  const backgroundStyles: React.CSSProperties = {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: themeConfig.colors.background,
  };

  const surfaceStyles: React.CSSProperties = {
    position: 'absolute',
    top: '8px',
    left: '8px',
    right: '8px',
    height: '12px',
    backgroundColor: themeConfig.colors.surface,
    borderRadius: '2px',
  };

  const accentStyles: React.CSSProperties = {
    position: 'absolute',
    bottom: '8px',
    left: '8px',
    width: '20px',
    height: '4px',
    backgroundColor: themeConfig.colors.primary,
    borderRadius: '2px',
  };

  return (
    <div
      className={className}
      style={previewStyles}
      onClick={onClick}
      title={themeName}
    >
      <div style={backgroundStyles} />
      <div style={surfaceStyles} />
      <div style={accentStyles} />
    </div>
  );
};
