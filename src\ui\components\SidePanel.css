/* Side Panel Styles */
.side-panel {
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  background: var(--vscode-sideBar-background);
  border-left: 1px solid var(--vscode-sideBar-border);
  display: flex;
  flex-direction: column;
  z-index: 100;
}

.panel-tabs {
  display: flex;
  background: var(--vscode-tab-inactiveBackground);
  border-bottom: 1px solid var(--vscode-tab-border);
}

.tab-button {
  flex: 1;
  background: none;
  border: none;
  color: var(--vscode-tab-inactiveForeground);
  padding: 12px 8px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  transition: all 0.2s ease;
}

.tab-button.active {
  background: var(--vscode-tab-activeBackground);
  color: var(--vscode-tab-activeForeground);
  border-bottom: 2px solid var(--vscode-tab-activeBorder);
}

.tab-button:hover:not(.active) {
  background: var(--vscode-tab-hoverBackground);
  color: var(--vscode-tab-hoverForeground);
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.panel-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--vscode-foreground);
}

.tool-count,
.status-indicator {
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
}

.status-indicator.online {
  color: var(--vscode-testing-iconPassed);
}

.status-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  background: currentColor;
  border-radius: 50%;
  margin-right: 4px;
}

.tools-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
  margin-bottom: 16px;
}

.tool-card {
  padding: 12px;
  background: var(--vscode-panel-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tool-card:hover {
  background: var(--vscode-list-hoverBackground);
  border-color: var(--vscode-focusBorder);
}

.tool-card.active {
  background: var(--vscode-list-activeSelectionBackground);
  border-color: var(--vscode-list-activeSelectionBackground);
  color: var(--vscode-list-activeSelectionForeground);
}

.tool-icon {
  font-size: 20px;
  margin-bottom: 8px;
}

.tool-title {
  margin: 0 0 4px 0;
  font-size: 13px;
  font-weight: 600;
}

.tool-description {
  margin: 0 0 8px 0;
  font-size: 11px;
  color: var(--vscode-descriptionForeground);
  line-height: 1.3;
}

.tool-shortcut {
  font-size: 10px;
  color: var(--vscode-descriptionForeground);
  background: var(--vscode-keybindingLabel-background);
  padding: 2px 4px;
  border-radius: 3px;
  border: 1px solid var(--vscode-keybindingLabel-border);
}

.tool-tips {
  margin-top: 16px;
}

.tool-tips h4 {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: var(--vscode-foreground);
}

.tool-tips ul {
  margin: 0;
  padding-left: 16px;
  font-size: 11px;
  color: var(--vscode-descriptionForeground);
}

.tool-tips li {
  margin-bottom: 4px;
}

.context-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.context-section h4 {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: var(--vscode-foreground);
}

.context-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
  margin-bottom: 4px;
}

.context-list {
  margin-bottom: 8px;
}

.list-label {
  font-size: 11px;
  color: var(--vscode-descriptionForeground);
  margin-bottom: 4px;
  display: block;
}

.context-tag {
  display: inline-block;
  background: var(--vscode-badge-background);
  color: var(--vscode-badge-foreground);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  margin: 2px 4px 2px 0;
}

.search-input {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.search-field {
  flex: 1;
  background: var(--vscode-input-background);
  border: 1px solid var(--vscode-input-border);
  color: var(--vscode-input-foreground);
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.search-submit {
  background: var(--vscode-button-background);
  border: 1px solid var(--vscode-button-border);
  color: var(--vscode-button-foreground);
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
}

.search-results {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.search-result {
  padding: 8px;
  background: var(--vscode-panel-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  font-size: 11px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.result-type {
  background: var(--vscode-badge-background);
  color: var(--vscode-badge-foreground);
  padding: 1px 4px;
  border-radius: 8px;
  font-size: 9px;
}

.result-score {
  color: var(--vscode-descriptionForeground);
  font-size: 9px;
}

.result-title {
  margin: 0 0 4px 0;
  font-size: 12px;
  font-weight: 600;
}

.result-snippet {
  margin: 0 0 4px 0;
  color: var(--vscode-descriptionForeground);
  line-height: 1.3;
}

.result-meta {
  display: flex;
  gap: 8px;
  font-size: 9px;
  color: var(--vscode-descriptionForeground);
}

.empty-state {
  text-align: center;
  padding: 32px 16px;
  color: var(--vscode-descriptionForeground);
}

.empty-state .icon {
  font-size: 32px;
  margin-bottom: 8px;
  opacity: 0.5;
}

.search-suggestions {
  margin-top: 16px;
}

.search-suggestions h5 {
  margin: 0 0 8px 0;
  font-size: 11px;
}

.suggestion-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.suggestion-tag {
  background: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.suggestion-tag:hover {
  background: var(--vscode-button-secondaryHoverBackground);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

.stat-card {
  padding: 12px;
  background: var(--vscode-panel-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 6px;
}

.stat-icon {
  font-size: 16px;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 10px;
  color: var(--vscode-descriptionForeground);
  margin-bottom: 4px;
  display: block;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: var(--vscode-foreground);
}

.stat-progress {
  height: 4px;
  background: var(--vscode-progressBar-background);
  border-radius: 2px;
  margin-top: 4px;
  overflow: hidden;
}

.stat-progress-fill {
  height: 100%;
  background: var(--vscode-progressBar-foreground);
  transition: width 0.3s ease;
}

.system-info {
  margin-top: 16px;
}

.system-info h4 {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: var(--vscode-foreground);
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
}

.info-label {
  color: var(--vscode-descriptionForeground);
}

.info-value {
  color: var(--vscode-foreground);
  font-weight: 500;
}

.refresh-button,
.search-button {
  background: none;
  border: none;
  color: var(--vscode-icon-foreground);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.refresh-button:hover,
.search-button:hover {
  background: var(--vscode-toolbar-hoverBackground);
}
