/**
 * LLM Integration Interfaces
 * 
 * 定义LLM集成模块的接口和类型
 */

import { Message, Tool, Tool<PERSON>all, ToolResult, TokenUsage, LLMProvider } from '@/types';

// LLM请求配置
export interface LLMRequestConfig {
  model: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stop?: string[];
  stream?: boolean;
  tools?: Tool[];
  toolChoice?: 'auto' | 'none' | { type: 'function'; function: { name: string } };
}

// LLM响应
export interface LLMResponse {
  id: string;
  content: string;
  role: 'assistant';
  toolCalls?: ToolCall[];
  usage?: TokenUsage;
  model: string;
  finishReason?: 'stop' | 'length' | 'tool_calls' | 'content_filter';
  metadata?: Record<string, any>;
}

// 流式响应块
export interface LLMStreamChunk {
  id: string;
  delta: {
    content?: string;
    toolCalls?: Partial<ToolCall>[];
  };
  usage?: TokenUsage;
  finishReason?: string;
}

// LLM提供者接口
export interface ILLMProvider {
  readonly name: LLMProvider;
  readonly models: string[];
  readonly supportsStreaming: boolean;
  readonly supportsTools: boolean;
  readonly supportsVision: boolean;

  // 基础聊天
  chat(messages: Message[], config: LLMRequestConfig): Promise<LLMResponse>;
  
  // 流式聊天
  chatStream(messages: Message[], config: LLMRequestConfig): AsyncIterable<LLMStreamChunk>;
  
  // 工具调用
  callTool(toolCall: ToolCall, context?: any): Promise<ToolResult>;
  
  // 配置和验证
  configure(config: Record<string, any>): void;
  validateConfig(): Promise<boolean>;
  
  // 模型信息
  getModelInfo(model: string): ModelInfo | undefined;
  
  // 健康检查
  healthCheck(): Promise<HealthStatus>;
}

// 模型信息
export interface ModelInfo {
  name: string;
  displayName: string;
  description: string;
  maxTokens: number;
  inputCost: number; // per 1K tokens
  outputCost: number; // per 1K tokens
  supportsTools: boolean;
  supportsVision: boolean;
  contextWindow: number;
}

// 健康状态
export interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  latency?: number;
  error?: string;
  timestamp: number;
}

// LLM管理器接口
export interface ILLMManager {
  // 提供者管理
  registerProvider(provider: ILLMProvider): void;
  unregisterProvider(name: LLMProvider): void;
  getProvider(name: LLMProvider): ILLMProvider | undefined;
  listProviders(): LLMProvider[];
  
  // 聊天功能
  chat(
    provider: LLMProvider,
    messages: Message[],
    config: LLMRequestConfig
  ): Promise<LLMResponse>;
  
  chatStream(
    provider: LLMProvider,
    messages: Message[],
    config: LLMRequestConfig
  ): AsyncIterable<LLMStreamChunk>;
  
  // 工具调用
  executeToolCall(toolCall: ToolCall, context?: any): Promise<ToolResult>;
  
  // 模型选择
  selectBestModel(requirements: ModelRequirements): {
    provider: LLMProvider;
    model: string;
  } | null;
  
  // 统计和监控
  getUsageStats(): UsageStats;
  getHealthStatus(): Promise<Record<LLMProvider, HealthStatus>>;
}

// 模型需求
export interface ModelRequirements {
  maxTokens?: number;
  supportsTools?: boolean;
  supportsVision?: boolean;
  costSensitive?: boolean;
  speedSensitive?: boolean;
  qualityLevel?: 'basic' | 'standard' | 'premium';
}

// 使用统计
export interface UsageStats {
  totalRequests: number;
  totalTokens: number;
  totalCost: number;
  requestsByProvider: Record<LLMProvider, number>;
  tokensByProvider: Record<LLMProvider, number>;
  costByProvider: Record<LLMProvider, number>;
  averageLatency: number;
  errorRate: number;
}

// 提供者配置
export interface ProviderConfig {
  apiKey: string;
  baseUrl?: string;
  organization?: string;
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
  customHeaders?: Record<string, string>;
}

// OpenAI配置
export interface OpenAIConfig extends ProviderConfig {
  organization?: string;
  project?: string;
}

// Claude配置
export interface ClaudeConfig extends ProviderConfig {
  version?: string;
}

// Gemini配置
export interface GeminiConfig extends ProviderConfig {
  projectId?: string;
  location?: string;
}

// DeepSeek配置
export interface DeepSeekConfig extends ProviderConfig {
  // DeepSeek特定配置
}

// 错误类型
export class LLMError extends Error {
  constructor(
    message: string,
    public provider: LLMProvider,
    public code: string,
    public statusCode?: number,
    public details?: any
  ) {
    super(message);
    this.name = 'LLMError';
  }
}

// 速率限制错误
export class RateLimitError extends LLMError {
  constructor(
    provider: LLMProvider,
    public retryAfter?: number,
    details?: any
  ) {
    super(`Rate limit exceeded for ${provider}`, provider, 'RATE_LIMIT_EXCEEDED', 429, details);
    this.name = 'RateLimitError';
  }
}

// 配额错误
export class QuotaExceededError extends LLMError {
  constructor(provider: LLMProvider, details?: any) {
    super(`Quota exceeded for ${provider}`, provider, 'QUOTA_EXCEEDED', 429, details);
    this.name = 'QuotaExceededError';
  }
}

// 模型不可用错误
export class ModelUnavailableError extends LLMError {
  constructor(provider: LLMProvider, model: string, details?: any) {
    super(`Model ${model} is not available for ${provider}`, provider, 'MODEL_UNAVAILABLE', 404, details);
    this.name = 'ModelUnavailableError';
  }
}

// 工具调用错误
export class ToolCallError extends Error {
  constructor(
    message: string,
    public toolName: string,
    public toolCallId: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ToolCallError';
  }
}
