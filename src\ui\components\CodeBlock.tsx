/**
 * CodeBlock Component - 代码块组件
 * 
 * 显示代码块，支持语法高亮、复制和应用功能
 */

import React, { useState } from 'react';
import { CodeBlockProps } from '../types';
import { useThemeStyles } from '../theme/ThemeProvider';
import { Button, IconButton } from './Button';

export const CodeBlock: React.FC<CodeBlockProps> = ({
  code,
  language,
  filename,
  showLineNumbers = true,
  onCopy,
  onApply,
}) => {
  const { colors, spacing, typography, borderRadius } = useThemeStyles();
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      onCopy?.(code);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy code:', error);
    }
  };

  const handleApply = () => {
    onApply?.(code, filename);
  };

  const getLanguageColor = (lang: string) => {
    const languageColors: Record<string, string> = {
      javascript: '#f7df1e',
      typescript: '#3178c6',
      python: '#3776ab',
      java: '#ed8b00',
      cpp: '#00599c',
      csharp: '#239120',
      php: '#777bb4',
      ruby: '#cc342d',
      go: '#00add8',
      rust: '#000000',
      html: '#e34f26',
      css: '#1572b6',
      json: '#000000',
      xml: '#0060ac',
      yaml: '#cb171e',
      markdown: '#083fa1',
      shell: '#89e051',
      sql: '#336791',
      dockerfile: '#384d54',
    };

    return languageColors[lang.toLowerCase()] || colors.textSecondary;
  };

  const formatCode = (code: string) => {
    const lines = code.split('\n');
    return lines.map((line, index) => ({
      number: index + 1,
      content: line,
    }));
  };

  const headerStyles: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: `${spacing.sm} ${spacing.md}`,
    backgroundColor: colors.surface,
    borderBottom: `1px solid ${colors.border}`,
    borderTopLeftRadius: borderRadius.md,
    borderTopRightRadius: borderRadius.md,
  };

  const languageTagStyles: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    gap: spacing.xs,
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  };

  const languageDotStyles: React.CSSProperties = {
    width: '8px',
    height: '8px',
    borderRadius: '50%',
    backgroundColor: getLanguageColor(language),
  };

  const actionsStyles: React.CSSProperties = {
    display: 'flex',
    gap: spacing.xs,
  };

  const codeContainerStyles: React.CSSProperties = {
    backgroundColor: colors.background,
    border: `1px solid ${colors.border}`,
    borderTop: 'none',
    borderBottomLeftRadius: borderRadius.md,
    borderBottomRightRadius: borderRadius.md,
    overflow: 'hidden',
  };

  const codeStyles: React.CSSProperties = {
    fontFamily: 'Consolas, Monaco, "Courier New", monospace',
    fontSize: typography.fontSize.sm,
    lineHeight: '1.5',
    margin: 0,
    padding: spacing.md,
    overflow: 'auto',
    maxHeight: '400px',
    whiteSpace: 'pre',
    color: colors.text,
  };

  const lineNumberStyles: React.CSSProperties = {
    display: 'table-cell',
    textAlign: 'right',
    paddingRight: spacing.md,
    color: colors.textSecondary,
    userSelect: 'none',
    borderRight: `1px solid ${colors.border}`,
    minWidth: '40px',
    verticalAlign: 'top',
  };

  const lineContentStyles: React.CSSProperties = {
    display: 'table-cell',
    paddingLeft: spacing.md,
    width: '100%',
    verticalAlign: 'top',
  };

  const renderLineNumbers = () => {
    if (!showLineNumbers) return null;

    const lines = formatCode(code);
    return (
      <div style={lineNumberStyles}>
        {lines.map((line) => (
          <div key={line.number}>{line.number}</div>
        ))}
      </div>
    );
  };

  const renderCodeContent = () => {
    const lines = formatCode(code);
    
    if (showLineNumbers) {
      return (
        <div style={{ display: 'table', width: '100%' }}>
          <div style={{ display: 'table-row' }}>
            {renderLineNumbers()}
            <div style={lineContentStyles}>
              {lines.map((line) => (
                <div key={line.number}>{line.content || '\u00A0'}</div>
              ))}
            </div>
          </div>
        </div>
      );
    }

    return (
      <div>
        {lines.map((line) => (
          <div key={line.number}>{line.content || '\u00A0'}</div>
        ))}
      </div>
    );
  };

  const containerStyles: React.CSSProperties = {
    margin: `${spacing.md} 0`,
    borderRadius: borderRadius.md,
    overflow: 'hidden',
    border: `1px solid ${colors.border}`,
  };

  return (
    <div style={containerStyles}>
      <div style={headerStyles}>
        <div style={languageTagStyles}>
          <div style={languageDotStyles} />
          <span>{language}</span>
          {filename && (
            <>
              <span style={{ color: colors.textSecondary }}>•</span>
              <span style={{ color: colors.textSecondary }}>{filename}</span>
            </>
          )}
        </div>
        
        <div style={actionsStyles}>
          <IconButton
            icon={copied ? "✓" : "📋"}
            size="sm"
            variant="ghost"
            aria-label={copied ? "Copied" : "Copy code"}
            onClick={handleCopy}
            style={{
              color: copied ? colors.success : colors.textSecondary,
            }}
          />
          
          {onApply && (
            <IconButton
              icon="⚡"
              size="sm"
              variant="ghost"
              aria-label="Apply code"
              onClick={handleApply}
              style={{ color: colors.textSecondary }}
            />
          )}
        </div>
      </div>
      
      <div style={codeContainerStyles}>
        <pre style={codeStyles}>
          {renderCodeContent()}
        </pre>
      </div>
    </div>
  );
};

// 内联代码组件
interface InlineCodeProps {
  children: React.ReactNode;
}

export const InlineCode: React.FC<InlineCodeProps> = ({ children }) => {
  const { colors, spacing, typography, borderRadius } = useThemeStyles();

  const styles: React.CSSProperties = {
    fontFamily: 'Consolas, Monaco, "Courier New", monospace',
    fontSize: '0.9em',
    backgroundColor: colors.surface,
    color: colors.text,
    padding: `2px ${spacing.xs}`,
    borderRadius: borderRadius.sm,
    border: `1px solid ${colors.border}`,
  };

  return <code style={styles}>{children}</code>;
};

// 代码差异组件
interface CodeDiffProps {
  oldCode: string;
  newCode: string;
  language: string;
  filename?: string;
}

export const CodeDiff: React.FC<CodeDiffProps> = ({
  oldCode,
  newCode,
  language,
  filename,
}) => {
  const { colors, spacing, borderRadius } = useThemeStyles();

  const getDiffLines = () => {
    const oldLines = oldCode.split('\n');
    const newLines = newCode.split('\n');
    const maxLines = Math.max(oldLines.length, newLines.length);
    
    const diffLines = [];
    for (let i = 0; i < maxLines; i++) {
      const oldLine = oldLines[i] || '';
      const newLine = newLines[i] || '';
      
      if (oldLine === newLine) {
        diffLines.push({ type: 'unchanged', content: oldLine, lineNumber: i + 1 });
      } else if (oldLine && !newLine) {
        diffLines.push({ type: 'removed', content: oldLine, lineNumber: i + 1 });
      } else if (!oldLine && newLine) {
        diffLines.push({ type: 'added', content: newLine, lineNumber: i + 1 });
      } else {
        diffLines.push({ type: 'removed', content: oldLine, lineNumber: i + 1 });
        diffLines.push({ type: 'added', content: newLine, lineNumber: i + 1 });
      }
    }
    
    return diffLines;
  };

  const getLineStyles = (type: string): React.CSSProperties => {
    const baseStyles: React.CSSProperties = {
      padding: `2px ${spacing.sm}`,
      fontFamily: 'Consolas, Monaco, "Courier New", monospace',
      fontSize: '14px',
      lineHeight: '1.5',
      whiteSpace: 'pre',
    };

    switch (type) {
      case 'added':
        return {
          ...baseStyles,
          backgroundColor: `${colors.success}20`,
          borderLeft: `3px solid ${colors.success}`,
        };
      case 'removed':
        return {
          ...baseStyles,
          backgroundColor: `${colors.error}20`,
          borderLeft: `3px solid ${colors.error}`,
        };
      default:
        return {
          ...baseStyles,
          backgroundColor: 'transparent',
        };
    }
  };

  const containerStyles: React.CSSProperties = {
    border: `1px solid ${colors.border}`,
    borderRadius: borderRadius.md,
    overflow: 'hidden',
    margin: `${spacing.md} 0`,
  };

  const headerStyles: React.CSSProperties = {
    padding: `${spacing.sm} ${spacing.md}`,
    backgroundColor: colors.surface,
    borderBottom: `1px solid ${colors.border}`,
    fontWeight: 'bold',
  };

  return (
    <div style={containerStyles}>
      <div style={headerStyles}>
        Diff: {filename || `${language} code`}
      </div>
      <div>
        {getDiffLines().map((line, index) => (
          <div key={index} style={getLineStyles(line.type)}>
            <span style={{ color: colors.textSecondary, marginRight: spacing.md }}>
              {line.type === 'added' ? '+' : line.type === 'removed' ? '-' : ' '}
            </span>
            {line.content}
          </div>
        ))}
      </div>
    </div>
  );
};
