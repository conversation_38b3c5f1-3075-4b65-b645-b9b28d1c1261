/**
 * WebView Entry Point - WebView入口文件
 * 
 * React应用的入口点，在VS Code WebView中运行
 */

import React, { useState, useEffect, useCallback } from 'react';
import { createRoot } from 'react-dom/client';
import { ChatInterface } from './components/ChatInterface';
import { Message, ToolCall, ToolResult } from '../types';
import './styles/global.css';

// VS Code API类型声明
declare const vscode: {
  postMessage: (message: any) => void;
  getState: () => any;
  setState: (state: any) => void;
};

interface AppState {
  messages: Message[];
  isLoading: boolean;
  systemStats?: {
    tokenUsage: number;
    maxTokens: number;
    responseTime: number;
    ragStats: any;
  };
  connectionStatus: 'connected' | 'connecting' | 'disconnected';
}

const App: React.FC = () => {
  const [state, setState] = useState<AppState>({
    messages: [],
    isLoading: false,
    connectionStatus: 'connecting'
  });

  // 发送消息到扩展
  const sendMessage = useCallback((message: any) => {
    vscode.postMessage(message);
  }, []);

  // 处理来自扩展的消息
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      const message = event.data;
      
      switch (message.type) {
        case 'initialize':
          setState(prev => ({
            ...prev,
            systemStats: message.stats,
            messages: message.conversationHistory || [],
            connectionStatus: 'connected'
          }));
          break;
        
        case 'messageSent':
          setState(prev => ({
            ...prev,
            messages: [...prev.messages, message.message],
            isLoading: true
          }));
          break;
        
        case 'messageReceived':
          setState(prev => ({
            ...prev,
            messages: [...prev.messages, message.message],
            isLoading: false
          }));
          break;
        
        case 'streamingChunk':
          // 处理流式输出
          setState(prev => {
            const newMessages = [...prev.messages];
            const lastMessage = newMessages[newMessages.length - 1];
            
            if (lastMessage && lastMessage.role === 'assistant') {
              lastMessage.content += message.chunk;
            } else {
              newMessages.push({
                role: 'assistant',
                content: message.chunk,
                timestamp: new Date()
              });
            }
            
            return {
              ...prev,
              messages: newMessages,
              isLoading: true
            };
          });
          break;
        
        case 'streamingComplete':
          setState(prev => ({
            ...prev,
            isLoading: false
          }));
          break;
        
        case 'systemStats':
          setState(prev => ({
            ...prev,
            systemStats: message.stats
          }));
          break;
        
        case 'chatCleared':
          setState(prev => ({
            ...prev,
            messages: []
          }));
          break;
        
        case 'toolExecutionStarted':
          // 可以添加工具执行状态显示
          break;
        
        case 'toolExecutionCompleted':
          // 处理工具执行完成
          break;
        
        case 'error':
          console.error('Extension error:', message.error);
          setState(prev => ({
            ...prev,
            isLoading: false
          }));
          // 可以添加错误提示
          break;
        
        default:
          console.warn('Unknown message type:', message.type);
      }
    };

    window.addEventListener('message', handleMessage);
    
    // 通知扩展WebView已就绪
    sendMessage({ type: 'ready' });
    
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [sendMessage]);

  // 发送消息处理
  const handleSendMessage = useCallback((message: string, attachments?: any[]) => {
    sendMessage({
      type: 'sendMessage',
      message,
      attachments
    });
  }, [sendMessage]);

  // 工具调用处理
  const handleToolCall = useCallback(async (toolCall: ToolCall): Promise<ToolResult> => {
    return new Promise((resolve, reject) => {
      const messageId = Date.now().toString();
      
      // 发送工具调用请求
      sendMessage({
        type: 'executeToolCall',
        toolCall,
        messageId
      });
      
      // 监听响应
      const handleResponse = (event: MessageEvent) => {
        const message = event.data;
        
        if (message.type === 'toolCallResult' && message.messageId === messageId) {
          window.removeEventListener('message', handleResponse);
          resolve(message.result);
        } else if (message.type === 'toolCallError' && message.messageId === messageId) {
          window.removeEventListener('message', handleResponse);
          reject(new Error(message.error));
        }
      };
      
      window.addEventListener('message', handleResponse);
      
      // 超时处理
      setTimeout(() => {
        window.removeEventListener('message', handleResponse);
        reject(new Error('Tool call timeout'));
      }, 30000);
    });
  }, [sendMessage]);

  // 清空聊天处理
  const handleClearChat = useCallback(() => {
    sendMessage({ type: 'clearChat' });
  }, [sendMessage]);

  // 定期更新系统统计
  useEffect(() => {
    const interval = setInterval(() => {
      sendMessage({ type: 'getSystemStats' });
    }, 5000); // 每5秒更新一次

    return () => clearInterval(interval);
  }, [sendMessage]);

  // 键盘快捷键处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'r':
            event.preventDefault();
            // 触发RAG查询
            break;
          case 't':
            event.preventDefault();
            // 触发测试生成
            break;
          case 'e':
            event.preventDefault();
            // 触发代码解释
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // 主题检测
  useEffect(() => {
    const updateTheme = () => {
      const body = document.body;
      const isDark = body.classList.contains('vscode-dark') || 
                    body.classList.contains('vscode-high-contrast');
      
      document.documentElement.setAttribute('data-theme', isDark ? 'dark' : 'light');
    };

    // 初始主题设置
    updateTheme();
    
    // 监听主题变化
    const observer = new MutationObserver(updateTheme);
    observer.observe(document.body, {
      attributes: true,
      attributeFilter: ['class']
    });

    return () => observer.disconnect();
  }, []);

  return (
    <ChatInterface
      messages={state.messages}
      isLoading={state.isLoading}
      onSendMessage={handleSendMessage}
      onToolCall={handleToolCall}
      onClearChat={handleClearChat}
      systemStats={state.systemStats}
    />
  );
};

// 渲染应用
const container = document.getElementById('root');
if (container) {
  const root = createRoot(container);
  root.render(<App />);
} else {
  console.error('Root container not found');
}
