/**
 * Semantic Search Engine - 语义搜索引擎
 * 
 * 基于向量相似度的智能代码搜索引擎，支持语义匹配和混合检索策略
 */

import * as vscode from 'vscode';
import { EventBus } from './EventBus';
import { RAGIndexManager, IndexedItem } from './RAGIndexManager';
import { EnhancedCodeContext } from './EnhancedCodeContextExtractor';

export interface SearchQuery {
  text: string;
  type?: 'semantic' | 'keyword' | 'hybrid';
  filters?: SearchFilters;
  context?: EnhancedCodeContext;
  limit?: number;
}

export interface SearchFilters {
  fileTypes?: string[];
  languages?: string[];
  itemTypes?: IndexedItem['type'][];
  dateRange?: {
    start: Date;
    end: Date;
  };
  complexity?: {
    min: number;
    max: number;
  };
  tags?: string[];
}

export interface SearchResult {
  item: IndexedItem;
  score: number;
  relevanceType: 'semantic' | 'keyword' | 'context' | 'structural';
  explanation: string;
  highlights?: TextHighlight[];
}

export interface TextHighlight {
  start: number;
  end: number;
  type: 'exact' | 'semantic' | 'fuzzy';
}

export interface SearchStats {
  totalQueries: number;
  averageResponseTime: number;
  cacheHitRate: number;
  lastQueryTime: Date;
}

export class SemanticSearchEngine {
  private eventBus: EventBus;
  private indexManager: RAGIndexManager;
  private queryCache: Map<string, SearchResult[]> = new Map();
  private embeddingCache: Map<string, number[]> = new Map();
  private searchStats: SearchStats;
  
  // 配置参数
  private readonly CACHE_SIZE = 1000;
  private readonly CACHE_TTL = 30 * 60 * 1000; // 30分钟
  private readonly SIMILARITY_THRESHOLD = 0.7;
  private readonly MAX_RESULTS = 50;

  constructor(eventBus: EventBus, indexManager: RAGIndexManager) {
    this.eventBus = eventBus;
    this.indexManager = indexManager;
    this.searchStats = {
      totalQueries: 0,
      averageResponseTime: 0,
      cacheHitRate: 0,
      lastQueryTime: new Date()
    };
  }

  /**
   * 执行搜索
   */
  async search(query: SearchQuery): Promise<SearchResult[]> {
    const startTime = Date.now();
    
    try {
      await this.eventBus.emit({
        type: 'search.query_started',
        source: 'SemanticSearchEngine',
        query: query.text,
        queryType: query.type
      });

      // 检查缓存
      const cacheKey = this.generateCacheKey(query);
      const cachedResults = this.queryCache.get(cacheKey);
      
      if (cachedResults) {
        this.updateSearchStats(startTime, true);
        return cachedResults;
      }

      // 执行搜索
      let results: SearchResult[] = [];
      
      switch (query.type || 'hybrid') {
        case 'semantic':
          results = await this.performSemanticSearch(query);
          break;
        case 'keyword':
          results = await this.performKeywordSearch(query);
          break;
        case 'hybrid':
          results = await this.performHybridSearch(query);
          break;
      }

      // 应用过滤器
      if (query.filters) {
        results = this.applyFilters(results, query.filters);
      }

      // 限制结果数量
      results = results.slice(0, query.limit || this.MAX_RESULTS);

      // 缓存结果
      this.cacheResults(cacheKey, results);

      // 更新统计
      this.updateSearchStats(startTime, false);

      await this.eventBus.emit({
        type: 'search.query_completed',
        source: 'SemanticSearchEngine',
        query: query.text,
        resultCount: results.length,
        duration: Date.now() - startTime
      });

      return results;

    } catch (error) {
      await this.eventBus.emit({
        type: 'search.query_failed',
        source: 'SemanticSearchEngine',
        query: query.text,
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * 语义搜索
   */
  private async performSemanticSearch(query: SearchQuery): Promise<SearchResult[]> {
    const results: SearchResult[] = [];
    
    // 获取查询的向量表示
    const queryEmbedding = await this.getEmbedding(query.text);
    if (!queryEmbedding) {
      return results;
    }

    // 搜索所有索引项
    const allItems = this.getAllIndexedItems();
    
    for (const item of allItems) {
      // 获取项目的向量表示
      const itemEmbedding = await this.getItemEmbedding(item);
      if (!itemEmbedding) {
        continue;
      }

      // 计算相似度
      const similarity = this.calculateCosineSimilarity(queryEmbedding, itemEmbedding);
      
      if (similarity >= this.SIMILARITY_THRESHOLD) {
        results.push({
          item,
          score: similarity,
          relevanceType: 'semantic',
          explanation: `语义相似度: ${(similarity * 100).toFixed(1)}%`,
          highlights: await this.generateSemanticHighlights(query.text, item.content)
        });
      }
    }

    // 按相似度排序
    results.sort((a, b) => b.score - a.score);
    
    return results;
  }

  /**
   * 关键词搜索
   */
  private async performKeywordSearch(query: SearchQuery): Promise<SearchResult[]> {
    const results: SearchResult[] = [];
    const queryTerms = this.tokenizeQuery(query.text);
    
    // 使用索引管理器的搜索功能
    const indexResults = this.indexManager.searchIndex(query.text, {
      limit: this.MAX_RESULTS * 2 // 获取更多结果用于重新排序
    });

    for (const item of indexResults) {
      const score = this.calculateKeywordScore(queryTerms, item);
      
      if (score > 0) {
        results.push({
          item,
          score,
          relevanceType: 'keyword',
          explanation: `关键词匹配评分: ${score.toFixed(2)}`,
          highlights: this.generateKeywordHighlights(queryTerms, item.content)
        });
      }
    }

    // 按评分排序
    results.sort((a, b) => b.score - a.score);
    
    return results;
  }

  /**
   * 混合搜索
   */
  private async performHybridSearch(query: SearchQuery): Promise<SearchResult[]> {
    // 并行执行语义搜索和关键词搜索
    const [semanticResults, keywordResults] = await Promise.all([
      this.performSemanticSearch(query),
      this.performKeywordSearch(query)
    ]);

    // 合并和重新排序结果
    const combinedResults = this.combineSearchResults(semanticResults, keywordResults, query);
    
    // 添加上下文相关性
    if (query.context) {
      this.enhanceWithContextRelevance(combinedResults, query.context);
    }

    return combinedResults;
  }

  /**
   * 获取文本的向量表示
   */
  private async getEmbedding(text: string): Promise<number[] | null> {
    // 检查缓存
    const cacheKey = this.hashText(text);
    const cached = this.embeddingCache.get(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      // 这里应该调用实际的embedding API
      // 为了演示，我们使用简化的向量表示
      const embedding = this.generateSimpleEmbedding(text);
      
      // 缓存结果
      this.embeddingCache.set(cacheKey, embedding);
      
      return embedding;
    } catch (error) {
      console.error('Failed to get embedding:', error);
      return null;
    }
  }

  /**
   * 获取索引项的向量表示
   */
  private async getItemEmbedding(item: IndexedItem): Promise<number[] | null> {
    // 如果已有embedding，直接返回
    if (item.embedding) {
      return item.embedding;
    }

    // 生成embedding
    const text = this.prepareTextForEmbedding(item);
    return await this.getEmbedding(text);
  }

  /**
   * 计算余弦相似度
   */
  private calculateCosineSimilarity(vec1: number[], vec2: number[]): number {
    if (vec1.length !== vec2.length) {
      return 0;
    }

    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;

    for (let i = 0; i < vec1.length; i++) {
      dotProduct += vec1[i] * vec2[i];
      norm1 += vec1[i] * vec1[i];
      norm2 += vec2[i] * vec2[i];
    }

    if (norm1 === 0 || norm2 === 0) {
      return 0;
    }

    return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
  }

  /**
   * 生成简化的向量表示（用于演示）
   */
  private generateSimpleEmbedding(text: string): number[] {
    const words = text.toLowerCase().split(/\s+/);
    const embedding = new Array(100).fill(0);
    
    // 基于词频的简单向量化
    for (const word of words) {
      const hash = this.simpleHash(word);
      const index = Math.abs(hash) % embedding.length;
      embedding[index] += 1;
    }
    
    // 归一化
    const norm = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    if (norm > 0) {
      for (let i = 0; i < embedding.length; i++) {
        embedding[i] /= norm;
      }
    }
    
    return embedding;
  }

  /**
   * 准备文本用于embedding
   */
  private prepareTextForEmbedding(item: IndexedItem): string {
    let text = '';
    
    // 添加名称（权重更高）
    if (item.metadata.name) {
      text += item.metadata.name + ' ';
    }
    
    // 添加签名
    if (item.metadata.signature) {
      text += item.metadata.signature + ' ';
    }
    
    // 添加内容（截断）
    const content = item.content.length > 500 ? 
      item.content.substring(0, 500) : item.content;
    text += content;
    
    return text;
  }

  /**
   * 分词查询
   */
  private tokenizeQuery(query: string): string[] {
    return query.toLowerCase()
      .split(/\s+/)
      .filter(term => term.length > 2)
      .map(term => term.replace(/[^\w]/g, ''));
  }

  /**
   * 计算关键词评分
   */
  private calculateKeywordScore(queryTerms: string[], item: IndexedItem): number {
    let score = 0;
    const content = item.content.toLowerCase();
    const name = item.metadata.name?.toLowerCase() || '';
    
    for (const term of queryTerms) {
      // 名称匹配权重更高
      if (name.includes(term)) {
        score += 10;
      }
      
      // 内容匹配
      const contentMatches = (content.match(new RegExp(term, 'g')) || []).length;
      score += contentMatches * 2;
      
      // 标签匹配
      if (item.metadata.tags?.some(tag => tag.toLowerCase().includes(term))) {
        score += 5;
      }
    }
    
    return score;
  }

  /**
   * 生成关键词高亮
   */
  private generateKeywordHighlights(queryTerms: string[], content: string): TextHighlight[] {
    const highlights: TextHighlight[] = [];
    const contentLower = content.toLowerCase();
    
    for (const term of queryTerms) {
      let index = 0;
      while ((index = contentLower.indexOf(term, index)) !== -1) {
        highlights.push({
          start: index,
          end: index + term.length,
          type: 'exact'
        });
        index += term.length;
      }
    }
    
    return highlights;
  }

  /**
   * 生成语义高亮
   */
  private async generateSemanticHighlights(query: string, content: string): Promise<TextHighlight[]> {
    // 简化实现：基于关键词的模糊匹配
    const queryTerms = this.tokenizeQuery(query);
    const highlights: TextHighlight[] = [];
    
    // 这里可以实现更复杂的语义高亮逻辑
    return this.generateKeywordHighlights(queryTerms, content);
  }

  /**
   * 合并搜索结果
   */
  private combineSearchResults(
    semanticResults: SearchResult[],
    keywordResults: SearchResult[],
    query: SearchQuery
  ): SearchResult[] {
    const resultMap = new Map<string, SearchResult>();
    
    // 添加语义搜索结果
    for (const result of semanticResults) {
      const key = result.item.id;
      resultMap.set(key, {
        ...result,
        score: result.score * 0.6, // 语义搜索权重
        relevanceType: 'semantic'
      });
    }
    
    // 合并关键词搜索结果
    for (const result of keywordResults) {
      const key = result.item.id;
      const existing = resultMap.get(key);
      
      if (existing) {
        // 合并评分
        existing.score = existing.score + (result.score * 0.4); // 关键词搜索权重
        existing.relevanceType = 'semantic'; // 混合类型
        existing.explanation = `混合搜索: 语义+关键词匹配`;
      } else {
        resultMap.set(key, {
          ...result,
          score: result.score * 0.4,
          relevanceType: 'keyword'
        });
      }
    }
    
    // 转换为数组并排序
    const results = Array.from(resultMap.values());
    results.sort((a, b) => b.score - a.score);
    
    return results;
  }

  /**
   * 增强上下文相关性
   */
  private enhanceWithContextRelevance(results: SearchResult[], context: EnhancedCodeContext): void {
    for (const result of results) {
      let contextBonus = 0;
      
      // 同一文件的项目获得加分
      if (result.item.filePath === context.filePath) {
        contextBonus += 0.2;
      }
      
      // 相同语言的项目获得加分
      if (result.item.metadata.language === context.language) {
        contextBonus += 0.1;
      }
      
      // 依赖关系加分
      if (context.dependencies.some(dep => 
        dep.target === result.item.filePath || dep.source === result.item.filePath
      )) {
        contextBonus += 0.15;
      }
      
      result.score += contextBonus;
      
      if (contextBonus > 0) {
        result.explanation += ` (上下文加分: +${(contextBonus * 100).toFixed(1)}%)`;
      }
    }
    
    // 重新排序
    results.sort((a, b) => b.score - a.score);
  }

  /**
   * 应用过滤器
   */
  private applyFilters(results: SearchResult[], filters: SearchFilters): SearchResult[] {
    return results.filter(result => {
      const item = result.item;
      
      // 文件类型过滤
      if (filters.fileTypes && filters.fileTypes.length > 0) {
        const ext = item.filePath.split('.').pop()?.toLowerCase();
        if (!ext || !filters.fileTypes.includes(ext)) {
          return false;
        }
      }
      
      // 语言过滤
      if (filters.languages && filters.languages.length > 0) {
        if (!filters.languages.includes(item.metadata.language)) {
          return false;
        }
      }
      
      // 项目类型过滤
      if (filters.itemTypes && filters.itemTypes.length > 0) {
        if (!filters.itemTypes.includes(item.type)) {
          return false;
        }
      }
      
      // 复杂度过滤
      if (filters.complexity && item.metadata.complexity !== undefined) {
        const complexity = item.metadata.complexity;
        if (complexity < filters.complexity.min || complexity > filters.complexity.max) {
          return false;
        }
      }
      
      // 标签过滤
      if (filters.tags && filters.tags.length > 0) {
        const itemTags = item.metadata.tags || [];
        if (!filters.tags.some(tag => itemTags.includes(tag))) {
          return false;
        }
      }
      
      return true;
    });
  }

  /**
   * 获取所有索引项
   */
  private getAllIndexedItems(): IndexedItem[] {
    const stats = this.indexManager.getIndexStats();
    const items: IndexedItem[] = [];
    
    // 这里需要从索引管理器获取所有项目
    // 简化实现：使用搜索功能获取
    const searchResults = this.indexManager.searchIndex('', { limit: 10000 });
    
    return searchResults;
  }

  /**
   * 工具方法
   */
  private generateCacheKey(query: SearchQuery): string {
    const key = JSON.stringify({
      text: query.text,
      type: query.type,
      filters: query.filters,
      limit: query.limit
    });
    return this.hashText(key);
  }

  private hashText(text: string): string {
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return hash.toString();
  }

  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return hash;
  }

  private cacheResults(key: string, results: SearchResult[]): void {
    // 限制缓存大小
    if (this.queryCache.size >= this.CACHE_SIZE) {
      const firstKey = this.queryCache.keys().next().value;
      if (firstKey) {
        this.queryCache.delete(firstKey);
      }
    }
    
    this.queryCache.set(key, results);
  }

  private updateSearchStats(startTime: number, cacheHit: boolean): void {
    const duration = Date.now() - startTime;
    
    this.searchStats.totalQueries++;
    this.searchStats.lastQueryTime = new Date();
    
    // 更新平均响应时间
    this.searchStats.averageResponseTime = 
      (this.searchStats.averageResponseTime * (this.searchStats.totalQueries - 1) + duration) / 
      this.searchStats.totalQueries;
    
    // 更新缓存命中率
    const cacheHits = cacheHit ? 1 : 0;
    this.searchStats.cacheHitRate = 
      (this.searchStats.cacheHitRate * (this.searchStats.totalQueries - 1) + cacheHits) / 
      this.searchStats.totalQueries;
  }

  /**
   * 获取搜索统计
   */
  getSearchStats(): SearchStats {
    return { ...this.searchStats };
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.queryCache.clear();
    this.embeddingCache.clear();
  }

  /**
   * 清理资源
   */
  dispose(): void {
    this.clearCache();
  }
}
