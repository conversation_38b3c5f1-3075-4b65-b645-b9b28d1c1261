/**
 * 可访问性管理器
 * 
 * 提供键盘导航、屏幕阅读器支持和ARIA标签管理
 * 遵循WCAG 2.1 AA级标准
 */

export interface AccessibilityConfig {
  enableKeyboardNavigation?: boolean;
  enableScreenReader?: boolean;
  enableHighContrast?: boolean;
  enableReducedMotion?: boolean;
  focusRingStyle?: 'default' | 'enhanced' | 'custom';
  announceChanges?: boolean;
}

export interface FocusableElement {
  element: HTMLElement;
  tabIndex: number;
  role?: string;
  ariaLabel?: string;
  ariaDescribedBy?: string;
}

export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  metaKey?: boolean;
  action: () => void;
  description: string;
  scope?: 'global' | 'local';
}

export class AccessibilityManager {
  private static instance: AccessibilityManager;
  private config: Required<AccessibilityConfig>;
  private focusableElements: FocusableElement[] = [];
  private currentFocusIndex = -1;
  private keyboardShortcuts: Map<string, KeyboardShortcut> = new Map();
  private announcer: HTMLElement | null = null;
  private focusRing: HTMLElement | null = null;
  private isInitialized = false;

  constructor(config: AccessibilityConfig = {}) {
    this.config = {
      enableKeyboardNavigation: true,
      enableScreenReader: true,
      enableHighContrast: false,
      enableReducedMotion: false,
      focusRingStyle: 'enhanced',
      announceChanges: true,
      ...config,
    };
  }

  public static getInstance(config?: AccessibilityConfig): AccessibilityManager {
    if (!AccessibilityManager.instance) {
      AccessibilityManager.instance = new AccessibilityManager(config);
    }
    return AccessibilityManager.instance;
  }

  /**
   * 初始化可访问性管理器
   */
  public initialize(): void {
    if (this.isInitialized) return;

    this.setupScreenReaderAnnouncer();
    this.setupFocusRing();
    this.setupKeyboardNavigation();
    this.setupUserPreferences();
    this.setupGlobalShortcuts();
    
    this.isInitialized = true;
    this.announce('可访问性功能已启用');
  }

  /**
   * 设置屏幕阅读器播报器
   */
  private setupScreenReaderAnnouncer(): void {
    if (!this.config.enableScreenReader) return;

    this.announcer = document.createElement('div');
    this.announcer.setAttribute('aria-live', 'polite');
    this.announcer.setAttribute('aria-atomic', 'true');
    this.announcer.setAttribute('aria-relevant', 'text');
    this.announcer.style.cssText = `
      position: absolute;
      left: -10000px;
      width: 1px;
      height: 1px;
      overflow: hidden;
    `;
    document.body.appendChild(this.announcer);
  }

  /**
   * 设置焦点环
   */
  private setupFocusRing(): void {
    if (!this.config.enableKeyboardNavigation) return;

    this.focusRing = document.createElement('div');
    this.focusRing.className = 'accessibility-focus-ring';
    
    const styles = this.getFocusRingStyles();
    this.focusRing.style.cssText = styles;
    
    document.body.appendChild(this.focusRing);
  }

  /**
   * 获取焦点环样式
   */
  private getFocusRingStyles(): string {
    const baseStyles = `
      position: absolute;
      pointer-events: none;
      z-index: 9999;
      border-radius: 4px;
      transition: all 0.15s ease;
      opacity: 0;
    `;

    switch (this.config.focusRingStyle) {
      case 'enhanced':
        return baseStyles + `
          border: 2px solid #007acc;
          box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.3);
        `;
      case 'custom':
        return baseStyles + `
          border: 2px solid var(--color-primary, #007acc);
          box-shadow: 0 0 0 2px var(--color-primary-alpha, rgba(0, 122, 204, 0.3));
        `;
      default:
        return baseStyles + `
          outline: 2px solid #007acc;
          outline-offset: 2px;
        `;
    }
  }

  /**
   * 设置键盘导航
   */
  private setupKeyboardNavigation(): void {
    if (!this.config.enableKeyboardNavigation) return;

    document.addEventListener('keydown', this.handleKeyDown.bind(this));
    document.addEventListener('focusin', this.handleFocusIn.bind(this));
    document.addEventListener('focusout', this.handleFocusOut.bind(this));
  }

  /**
   * 设置用户偏好
   */
  private setupUserPreferences(): void {
    // 检测用户偏好减少动画
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      this.config.enableReducedMotion = true;
      document.documentElement.setAttribute('data-reduced-motion', 'true');
    }

    // 检测用户偏好高对比度
    if (window.matchMedia('(prefers-contrast: high)').matches) {
      this.config.enableHighContrast = true;
      document.documentElement.setAttribute('data-high-contrast', 'true');
    }

    // 监听偏好变化
    window.matchMedia('(prefers-reduced-motion: reduce)').addEventListener('change', (e) => {
      this.config.enableReducedMotion = e.matches;
      document.documentElement.setAttribute('data-reduced-motion', e.matches.toString());
    });

    window.matchMedia('(prefers-contrast: high)').addEventListener('change', (e) => {
      this.config.enableHighContrast = e.matches;
      document.documentElement.setAttribute('data-high-contrast', e.matches.toString());
    });
  }

  /**
   * 设置全局快捷键
   */
  private setupGlobalShortcuts(): void {
    // 跳转到主要内容
    this.registerShortcut({
      key: 'KeyS',
      altKey: true,
      action: () => this.skipToMainContent(),
      description: '跳转到主要内容',
      scope: 'global',
    });

    // 显示快捷键帮助
    this.registerShortcut({
      key: 'Slash',
      ctrlKey: true,
      action: () => this.showShortcutHelp(),
      description: '显示快捷键帮助',
      scope: 'global',
    });

    // 切换高对比度模式
    this.registerShortcut({
      key: 'KeyH',
      ctrlKey: true,
      altKey: true,
      action: () => this.toggleHighContrast(),
      description: '切换高对比度模式',
      scope: 'global',
    });
  }

  /**
   * 处理键盘按下事件
   */
  private handleKeyDown(event: KeyboardEvent): void {
    const shortcutKey = this.getShortcutKey(event);
    const shortcut = this.keyboardShortcuts.get(shortcutKey);
    
    if (shortcut) {
      event.preventDefault();
      shortcut.action();
      this.announce(`执行快捷键: ${shortcut.description}`);
      return;
    }

    // Tab导航
    if (event.key === 'Tab') {
      this.handleTabNavigation(event);
    }

    // 方向键导航
    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
      this.handleArrowNavigation(event);
    }

    // Enter和Space激活
    if (event.key === 'Enter' || event.key === ' ') {
      this.handleActivation(event);
    }

    // Escape键
    if (event.key === 'Escape') {
      this.handleEscape(event);
    }
  }

  /**
   * 处理焦点进入事件
   */
  private handleFocusIn(event: FocusEvent): void {
    const target = event.target as HTMLElement;
    if (!target) return;

    this.updateFocusRing(target);
    this.announceElement(target);
  }

  /**
   * 处理焦点离开事件
   */
  private handleFocusOut(): void {
    this.hideFocusRing();
  }

  /**
   * 更新焦点环位置
   */
  private updateFocusRing(element: HTMLElement): void {
    if (!this.focusRing) return;

    const rect = element.getBoundingClientRect();
    const scrollX = window.pageXOffset;
    const scrollY = window.pageYOffset;

    this.focusRing.style.left = `${rect.left + scrollX - 2}px`;
    this.focusRing.style.top = `${rect.top + scrollY - 2}px`;
    this.focusRing.style.width = `${rect.width + 4}px`;
    this.focusRing.style.height = `${rect.height + 4}px`;
    this.focusRing.style.opacity = '1';
  }

  /**
   * 隐藏焦点环
   */
  private hideFocusRing(): void {
    if (this.focusRing) {
      this.focusRing.style.opacity = '0';
    }
  }

  /**
   * 播报元素信息
   */
  private announceElement(element: HTMLElement): void {
    if (!this.config.announceChanges) return;

    const label = this.getElementLabel(element);
    const role = element.getAttribute('role') || element.tagName.toLowerCase();
    const state = this.getElementState(element);
    
    const announcement = `${label} ${role} ${state}`.trim();
    if (announcement) {
      this.announce(announcement);
    }
  }

  /**
   * 获取元素标签
   */
  private getElementLabel(element: HTMLElement): string {
    return element.getAttribute('aria-label') ||
           element.getAttribute('aria-labelledby') ||
           element.getAttribute('title') ||
           element.textContent?.trim() ||
           '';
  }

  /**
   * 获取元素状态
   */
  private getElementState(element: HTMLElement): string {
    const states = [];
    
    if (element.getAttribute('aria-expanded') === 'true') states.push('已展开');
    if (element.getAttribute('aria-expanded') === 'false') states.push('已折叠');
    if (element.getAttribute('aria-checked') === 'true') states.push('已选中');
    if (element.getAttribute('aria-checked') === 'false') states.push('未选中');
    if (element.getAttribute('aria-disabled') === 'true') states.push('已禁用');
    if (element.getAttribute('aria-selected') === 'true') states.push('已选择');
    
    return states.join(' ');
  }

  /**
   * 处理Tab导航
   */
  private handleTabNavigation(event: KeyboardEvent): void {
    // 让浏览器处理默认的Tab导航
    // 这里可以添加自定义逻辑
  }

  /**
   * 处理方向键导航
   */
  private handleArrowNavigation(event: KeyboardEvent): void {
    const target = event.target as HTMLElement;
    const role = target.getAttribute('role');
    
    // 根据角色处理方向键导航
    if (role === 'menu' || role === 'menubar') {
      this.handleMenuNavigation(event);
    } else if (role === 'tablist') {
      this.handleTabListNavigation(event);
    } else if (role === 'listbox') {
      this.handleListBoxNavigation(event);
    }
  }

  /**
   * 处理激活事件
   */
  private handleActivation(event: KeyboardEvent): void {
    const target = event.target as HTMLElement;
    
    if (target.tagName === 'BUTTON' || target.getAttribute('role') === 'button') {
      event.preventDefault();
      target.click();
    }
  }

  /**
   * 处理Escape键
   */
  private handleEscape(event: KeyboardEvent): void {
    // 关闭模态框、下拉菜单等
    const modals = document.querySelectorAll('[role="dialog"], [role="alertdialog"]');
    modals.forEach(modal => {
      if (modal instanceof HTMLElement && modal.style.display !== 'none') {
        const closeButton = modal.querySelector('[aria-label*="关闭"], [aria-label*="close"]');
        if (closeButton instanceof HTMLElement) {
          closeButton.click();
        }
      }
    });
  }

  /**
   * 处理菜单导航
   */
  private handleMenuNavigation(event: KeyboardEvent): void {
    // 实现菜单的方向键导航
    event.preventDefault();
    // 具体实现根据菜单结构而定
  }

  /**
   * 处理标签列表导航
   */
  private handleTabListNavigation(event: KeyboardEvent): void {
    // 实现标签列表的方向键导航
    event.preventDefault();
    // 具体实现根据标签结构而定
  }

  /**
   * 处理列表框导航
   */
  private handleListBoxNavigation(event: KeyboardEvent): void {
    // 实现列表框的方向键导航
    event.preventDefault();
    // 具体实现根据列表结构而定
  }

  /**
   * 获取快捷键字符串
   */
  private getShortcutKey(event: KeyboardEvent): string {
    const parts = [];
    if (event.ctrlKey) parts.push('Ctrl');
    if (event.altKey) parts.push('Alt');
    if (event.shiftKey) parts.push('Shift');
    if (event.metaKey) parts.push('Meta');
    parts.push(event.code);
    return parts.join('+');
  }

  /**
   * 注册快捷键
   */
  public registerShortcut(shortcut: KeyboardShortcut): void {
    const key = this.buildShortcutKey(shortcut);
    this.keyboardShortcuts.set(key, shortcut);
  }

  /**
   * 构建快捷键字符串
   */
  private buildShortcutKey(shortcut: KeyboardShortcut): string {
    const parts = [];
    if (shortcut.ctrlKey) parts.push('Ctrl');
    if (shortcut.altKey) parts.push('Alt');
    if (shortcut.shiftKey) parts.push('Shift');
    if (shortcut.metaKey) parts.push('Meta');
    parts.push(shortcut.key);
    return parts.join('+');
  }

  /**
   * 播报消息
   */
  public announce(message: string, priority: 'polite' | 'assertive' = 'polite'): void {
    if (!this.announcer || !this.config.announceChanges) return;

    this.announcer.setAttribute('aria-live', priority);
    this.announcer.textContent = message;
    
    // 清除消息以便下次播报
    setTimeout(() => {
      if (this.announcer) {
        this.announcer.textContent = '';
      }
    }, 1000);
  }

  /**
   * 跳转到主要内容
   */
  private skipToMainContent(): void {
    const main = document.querySelector('main, [role="main"], #main');
    if (main instanceof HTMLElement) {
      main.focus();
      main.scrollIntoView({ behavior: 'smooth' });
    }
  }

  /**
   * 显示快捷键帮助
   */
  private showShortcutHelp(): void {
    // 实现快捷键帮助对话框
    this.announce('快捷键帮助已打开');
  }

  /**
   * 切换高对比度模式
   */
  private toggleHighContrast(): void {
    this.config.enableHighContrast = !this.config.enableHighContrast;
    document.documentElement.setAttribute('data-high-contrast', this.config.enableHighContrast.toString());
    this.announce(`高对比度模式已${this.config.enableHighContrast ? '启用' : '禁用'}`);
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    if (this.announcer) {
      document.body.removeChild(this.announcer);
    }
    
    if (this.focusRing) {
      document.body.removeChild(this.focusRing);
    }
    
    document.removeEventListener('keydown', this.handleKeyDown.bind(this));
    document.removeEventListener('focusin', this.handleFocusIn.bind(this));
    document.removeEventListener('focusout', this.handleFocusOut.bind(this));
    
    this.keyboardShortcuts.clear();
    this.isInitialized = false;
  }
}

// 全局可访问性管理器实例
export const accessibilityManager = AccessibilityManager.getInstance();
