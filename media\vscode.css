/* VS Code theme variables */
:root {
	--vscode-font-family: var(--vscode-editor-font-family);
	--vscode-font-weight: var(--vscode-editor-font-weight);
	--vscode-font-size: var(--vscode-editor-font-size);
	--vscode-line-height: var(--vscode-editor-line-height);
}

body {
	font-family: var(--vscode-font-family);
	font-weight: var(--vscode-font-weight);
	font-size: var(--vscode-font-size);
	line-height: var(--vscode-line-height);
	color: var(--vscode-foreground);
	background-color: var(--vscode-editor-background);
}

* {
	box-sizing: border-box;
}

body {
	padding: 0;
	margin: 0;
	overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
	color: var(--vscode-foreground);
}

a {
	color: var(--vscode-textLink-foreground);
}

a:hover {
	color: var(--vscode-textLink-activeForeground);
}

button {
	background-color: var(--vscode-button-background);
	color: var(--vscode-button-foreground);
	border: none;
	padding: 6px 14px;
	border-radius: 2px;
	cursor: pointer;
	font-family: var(--vscode-font-family);
	font-size: var(--vscode-font-size);
}

button:hover {
	background-color: var(--vscode-button-hoverBackground);
}

button:focus {
	outline: 1px solid var(--vscode-focusBorder);
	outline-offset: 2px;
}

input[type="text"], input[type="password"], textarea {
	background-color: var(--vscode-input-background);
	color: var(--vscode-input-foreground);
	border: 1px solid var(--vscode-input-border);
	padding: 6px 8px;
	border-radius: 2px;
	font-family: var(--vscode-font-family);
	font-size: var(--vscode-font-size);
}

input[type="text"]:focus, input[type="password"]:focus, textarea:focus {
	outline: 1px solid var(--vscode-focusBorder);
	outline-offset: -1px;
	border-color: var(--vscode-focusBorder);
}

input[type="text"]::placeholder, input[type="password"]::placeholder, textarea::placeholder {
	color: var(--vscode-input-placeholderForeground);
}

.codicon {
	font-family: codicon;
	cursor: default;
	user-select: none;
}

.error {
	color: var(--vscode-errorForeground);
}

.warning {
	color: var(--vscode-warningForeground);
}

.info {
	color: var(--vscode-infoForeground);
}
