/**
 * Input Area - 输入区域组件
 * 
 * 支持多行输入、文件拖拽、快捷键、自动补全
 */

import React, { useState, useRef, useEffect, useCallback } from 'react';
import './InputArea.css';

export interface InputAreaProps {
  onSendMessage: (message: string, attachments?: any[]) => void;
  disabled?: boolean;
  placeholder?: string;
  maxLength?: number;
  showAttachments?: boolean;
}

export const InputArea: React.FC<InputAreaProps> = ({
  onSendMessage,
  disabled = false,
  placeholder = '输入您的问题...',
  maxLength = 4000,
  showAttachments = true
}) => {
  const [message, setMessage] = useState('');
  const [attachments, setAttachments] = useState<any[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedSuggestion, setSelectedSuggestion] = useState(-1);
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 预设的建议问题
  const predefinedSuggestions = [
    '解释这段代码的功能',
    '重构这个函数',
    '为这个类生成单元测试',
    '优化这段代码的性能',
    '检查代码中的潜在问题',
    '添加错误处理',
    '生成代码文档',
    '转换为TypeScript',
    '实现设计模式',
    '添加日志记录'
  ];

  // 自动调整文本框高度
  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      const newHeight = Math.min(textarea.scrollHeight, 200); // 最大高度200px
      textarea.style.height = `${newHeight}px`;
    }
  }, []);

  useEffect(() => {
    adjustTextareaHeight();
  }, [message, adjustTextareaHeight]);

  // 处理键盘事件
  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (showSuggestions && suggestions.length > 0) {
      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault();
          setSelectedSuggestion(prev => 
            prev < suggestions.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          event.preventDefault();
          setSelectedSuggestion(prev => 
            prev > 0 ? prev - 1 : suggestions.length - 1
          );
          break;
        case 'Tab':
        case 'Enter':
          if (selectedSuggestion >= 0) {
            event.preventDefault();
            selectSuggestion(suggestions[selectedSuggestion]);
            return;
          }
          break;
        case 'Escape':
          event.preventDefault();
          setShowSuggestions(false);
          setSelectedSuggestion(-1);
          break;
      }
    }

    if (event.key === 'Enter') {
      if (event.ctrlKey || event.metaKey) {
        event.preventDefault();
        handleSend();
      } else if (!event.shiftKey && !showSuggestions) {
        event.preventDefault();
        handleSend();
      }
    }

    if (event.key === 'Tab' && !showSuggestions) {
      event.preventDefault();
      // 插入制表符
      const start = event.currentTarget.selectionStart;
      const end = event.currentTarget.selectionEnd;
      const newMessage = message.substring(0, start) + '  ' + message.substring(end);
      setMessage(newMessage);
      
      // 恢复光标位置
      setTimeout(() => {
        if (textareaRef.current) {
          textareaRef.current.selectionStart = textareaRef.current.selectionEnd = start + 2;
        }
      }, 0);
    }
  };

  // 处理输入变化
  const handleInputChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newMessage = event.target.value;
    if (newMessage.length <= maxLength) {
      setMessage(newMessage);
      
      // 显示建议
      if (newMessage.trim().length > 0) {
        const filteredSuggestions = predefinedSuggestions.filter(suggestion =>
          suggestion.toLowerCase().includes(newMessage.toLowerCase())
        );
        setSuggestions(filteredSuggestions.slice(0, 5));
        setShowSuggestions(filteredSuggestions.length > 0);
        setSelectedSuggestion(-1);
      } else {
        setShowSuggestions(false);
      }
    }
  };

  // 选择建议
  const selectSuggestion = (suggestion: string) => {
    setMessage(suggestion);
    setShowSuggestions(false);
    setSelectedSuggestion(-1);
    textareaRef.current?.focus();
  };

  // 发送消息
  const handleSend = () => {
    const trimmedMessage = message.trim();
    if (trimmedMessage && !disabled) {
      onSendMessage(trimmedMessage, attachments);
      setMessage('');
      setAttachments([]);
      setShowSuggestions(false);
      textareaRef.current?.focus();
    }
  };

  // 处理文件拖拽
  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);
    
    const files = Array.from(event.dataTransfer.files);
    handleFiles(files);
  };

  // 处理文件选择
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    handleFiles(files);
  };

  // 处理文件
  const handleFiles = (files: File[]) => {
    const validFiles = files.filter(file => {
      // 只允许文本文件和图片
      return file.type.startsWith('text/') || 
             file.type.startsWith('image/') ||
             file.name.endsWith('.md') ||
             file.name.endsWith('.json') ||
             file.name.endsWith('.js') ||
             file.name.endsWith('.ts') ||
             file.name.endsWith('.py') ||
             file.name.endsWith('.java');
    });

    const newAttachments = validFiles.map(file => ({
      id: Date.now() + Math.random(),
      name: file.name,
      size: file.size,
      type: file.type,
      file
    }));

    setAttachments(prev => [...prev, ...newAttachments]);
  };

  // 移除附件
  const removeAttachment = (id: number) => {
    setAttachments(prev => prev.filter(att => att.id !== id));
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`input-area ${isDragging ? 'dragging' : ''}`}>
      {/* 建议列表 */}
      {showSuggestions && suggestions.length > 0 && (
        <div className="suggestions-list">
          {suggestions.map((suggestion, index) => (
            <div
              key={index}
              className={`suggestion-item ${index === selectedSuggestion ? 'selected' : ''}`}
              onClick={() => selectSuggestion(suggestion)}
            >
              <i className="icon icon-lightbulb" />
              {suggestion}
            </div>
          ))}
        </div>
      )}

      {/* 附件列表 */}
      {attachments.length > 0 && (
        <div className="attachments-list">
          {attachments.map(attachment => (
            <div key={attachment.id} className="attachment-item">
              <div className="attachment-info">
                <i className="icon icon-file" />
                <span className="attachment-name">{attachment.name}</span>
                <span className="attachment-size">({formatFileSize(attachment.size)})</span>
              </div>
              <button
                className="remove-attachment"
                onClick={() => removeAttachment(attachment.id)}
                title="移除附件"
              >
                <i className="icon icon-x" />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* 输入区域 */}
      <div 
        className="input-container"
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <textarea
          ref={textareaRef}
          value={message}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled}
          className="message-input"
          rows={1}
        />
        
        <div className="input-actions">
          {/* 附件按钮 */}
          {showAttachments && (
            <>
              <input
                ref={fileInputRef}
                type="file"
                multiple
                onChange={handleFileSelect}
                style={{ display: 'none' }}
                accept=".txt,.md,.json,.js,.ts,.py,.java,image/*"
              />
              <button
                className="action-button"
                onClick={() => fileInputRef.current?.click()}
                title="添加附件"
                disabled={disabled}
              >
                <i className="icon icon-paperclip" />
              </button>
            </>
          )}

          {/* 发送按钮 */}
          <button
            className={`send-button ${message.trim() ? 'active' : ''}`}
            onClick={handleSend}
            disabled={disabled || !message.trim()}
            title="发送消息 (Ctrl+Enter)"
          >
            {disabled ? (
              <div className="spinner" />
            ) : (
              <i className="icon icon-send" />
            )}
          </button>
        </div>
      </div>

      {/* 字符计数 */}
      <div className="input-footer">
        <div className="character-count">
          <span className={message.length > maxLength * 0.9 ? 'warning' : ''}>
            {message.length}/{maxLength}
          </span>
        </div>
        
        <div className="input-hints">
          <span className="hint">Enter 发送 • Shift+Enter 换行 • Ctrl+Enter 强制发送</span>
        </div>
      </div>

      {/* 拖拽提示 */}
      {isDragging && (
        <div className="drag-overlay">
          <div className="drag-message">
            <i className="icon icon-upload" />
            <span>拖拽文件到这里添加附件</span>
          </div>
        </div>
      )}
    </div>
  );
};
