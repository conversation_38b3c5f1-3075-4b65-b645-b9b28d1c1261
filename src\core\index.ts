/**
 * Core Module Exports
 *
 * 导出核心模块的所有公共接口
 */

export { CoreEngine, type CoreEngineConfig } from './CoreEngine';
export { StateManager } from './StateManager';
export { EventBus } from './EventBus';
export { ServiceOrchestrator } from './ServiceOrchestrator';
export { ErrorHandler, ErrorSeverity, ErrorCategory } from './ErrorHandler';

// 创建核心引擎实例的工厂函数
export function createCoreEngine(config?: any) {
  return new CoreEngine(config);
}
