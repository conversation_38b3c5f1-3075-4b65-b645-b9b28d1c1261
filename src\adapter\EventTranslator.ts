/**
 * Event Translator - 事件转换器
 * 
 * 负责将VS Code事件转换为内部事件格式
 */

import * as vscode from 'vscode';
import { EventBus } from '@/core/EventBus';
import { 
  DocumentChangeEvent, 
  SelectionChangeEvent, 
  WorkspaceChangeEvent,
  DocumentChangeHandler,
  SelectionChangeHandler,
  WorkspaceChangeHandler
} from './interfaces';

export class EventTranslator {
  private eventBus: EventBus;
  private disposables: vscode.Disposable[] = [];

  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
    this.setupEventListeners();
  }

  /**
   * 设置VS Code事件监听器
   */
  private setupEventListeners(): void {
    // 监听活动编辑器变化
    this.disposables.push(
      vscode.window.onDidChangeActiveTextEditor((editor) => {
        this.eventBus.emit({
          type: 'editor.active_changed',
          source: 'EventTranslator',
          editor: editor ? {
            document: {
              uri: editor.document.uri.toString(),
              fileName: editor.document.fileName,
              languageId: editor.document.languageId,
              lineCount: editor.document.lineCount,
            },
            selection: {
              start: editor.selection.start,
              end: editor.selection.end,
              isEmpty: editor.selection.isEmpty,
            },
          } : null,
        });
      })
    );

    // 监听文档内容变化
    this.disposables.push(
      vscode.workspace.onDidChangeTextDocument((event) => {
        this.eventBus.emit({
          type: 'document.content_changed',
          source: 'EventTranslator',
          document: {
            uri: event.document.uri.toString(),
            fileName: event.document.fileName,
            languageId: event.document.languageId,
            version: event.document.version,
          },
          changes: event.contentChanges.map(change => ({
            range: change.range,
            rangeOffset: change.rangeOffset,
            rangeLength: change.rangeLength,
            text: change.text,
          })),
          reason: event.reason,
        });
      })
    );

    // 监听文档保存
    this.disposables.push(
      vscode.workspace.onDidSaveTextDocument((document) => {
        this.eventBus.emit({
          type: 'document.saved',
          source: 'EventTranslator',
          document: {
            uri: document.uri.toString(),
            fileName: document.fileName,
            languageId: document.languageId,
            version: document.version,
          },
        });
      })
    );

    // 监听文档打开
    this.disposables.push(
      vscode.workspace.onDidOpenTextDocument((document) => {
        this.eventBus.emit({
          type: 'document.opened',
          source: 'EventTranslator',
          document: {
            uri: document.uri.toString(),
            fileName: document.fileName,
            languageId: document.languageId,
            lineCount: document.lineCount,
          },
        });
      })
    );

    // 监听文档关闭
    this.disposables.push(
      vscode.workspace.onDidCloseTextDocument((document) => {
        this.eventBus.emit({
          type: 'document.closed',
          source: 'EventTranslator',
          document: {
            uri: document.uri.toString(),
            fileName: document.fileName,
            languageId: document.languageId,
          },
        });
      })
    );

    // 监听选择变化
    this.disposables.push(
      vscode.window.onDidChangeTextEditorSelection((event) => {
        this.eventBus.emit({
          type: 'editor.selection_changed',
          source: 'EventTranslator',
          editor: {
            document: {
              uri: event.textEditor.document.uri.toString(),
              fileName: event.textEditor.document.fileName,
              languageId: event.textEditor.document.languageId,
            },
            selections: event.selections.map(selection => ({
              start: selection.start,
              end: selection.end,
              isEmpty: selection.isEmpty,
              text: event.textEditor.document.getText(selection),
            })),
          },
          kind: event.kind,
        });
      })
    );

    // 监听工作区文件夹变化
    this.disposables.push(
      vscode.workspace.onDidChangeWorkspaceFolders((event) => {
        this.eventBus.emit({
          type: 'workspace.folders_changed',
          source: 'EventTranslator',
          added: event.added.map(folder => ({
            uri: folder.uri.toString(),
            name: folder.name,
            index: folder.index,
          })),
          removed: event.removed.map(folder => ({
            uri: folder.uri.toString(),
            name: folder.name,
            index: folder.index,
          })),
        });
      })
    );

    // 监听配置变化
    this.disposables.push(
      vscode.workspace.onDidChangeConfiguration((event) => {
        this.eventBus.emit({
          type: 'configuration.changed',
          source: 'EventTranslator',
          affectsConfiguration: (section: string) => event.affectsConfiguration(section),
        });
      })
    );

    // 监听文件系统变化
    this.disposables.push(
      vscode.workspace.onDidCreateFiles((event) => {
        this.eventBus.emit({
          type: 'files.created',
          source: 'EventTranslator',
          files: event.files.map(file => ({
            uri: file.toString(),
          })),
        });
      })
    );

    this.disposables.push(
      vscode.workspace.onDidDeleteFiles((event) => {
        this.eventBus.emit({
          type: 'files.deleted',
          source: 'EventTranslator',
          files: event.files.map(file => ({
            uri: file.toString(),
          })),
        });
      })
    );

    this.disposables.push(
      vscode.workspace.onDidRenameFiles((event) => {
        this.eventBus.emit({
          type: 'files.renamed',
          source: 'EventTranslator',
          files: event.files.map(file => ({
            oldUri: file.oldUri.toString(),
            newUri: file.newUri.toString(),
          })),
        });
      })
    );

    // 监听终端变化
    this.disposables.push(
      vscode.window.onDidOpenTerminal((terminal) => {
        this.eventBus.emit({
          type: 'terminal.opened',
          source: 'EventTranslator',
          terminal: {
            name: terminal.name,
            processId: terminal.processId,
          },
        });
      })
    );

    this.disposables.push(
      vscode.window.onDidCloseTerminal((terminal) => {
        this.eventBus.emit({
          type: 'terminal.closed',
          source: 'EventTranslator',
          terminal: {
            name: terminal.name,
            processId: terminal.processId,
            exitStatus: terminal.exitStatus,
          },
        });
      })
    );
  }

  /**
   * 注册文档变化处理器
   */
  onDidChangeTextDocument(handler: DocumentChangeHandler): vscode.Disposable {
    return vscode.workspace.onDidChangeTextDocument((event) => {
      const translatedEvent: DocumentChangeEvent = {
        document: event.document,
        changes: event.contentChanges,
      };
      handler(translatedEvent);
    });
  }

  /**
   * 注册选择变化处理器
   */
  onDidChangeTextEditorSelection(handler: SelectionChangeHandler): vscode.Disposable {
    return vscode.window.onDidChangeTextEditorSelection((event) => {
      const translatedEvent: SelectionChangeEvent = {
        textEditor: event.textEditor,
        selections: event.selections,
      };
      handler(translatedEvent);
    });
  }

  /**
   * 注册工作区变化处理器
   */
  onDidChangeWorkspaceFolders(handler: WorkspaceChangeHandler): vscode.Disposable {
    return vscode.workspace.onDidChangeWorkspaceFolders((event) => {
      const translatedEvent: WorkspaceChangeEvent = {
        added: event.added,
        removed: event.removed,
      };
      handler(translatedEvent);
    });
  }

  /**
   * 获取事件统计信息
   */
  getStats(): {
    activeListeners: number;
    eventTypes: string[];
  } {
    return {
      activeListeners: this.disposables.length,
      eventTypes: [
        'editor.active_changed',
        'document.content_changed',
        'document.saved',
        'document.opened',
        'document.closed',
        'editor.selection_changed',
        'workspace.folders_changed',
        'configuration.changed',
        'files.created',
        'files.deleted',
        'files.renamed',
        'terminal.opened',
        'terminal.closed',
      ],
    };
  }

  /**
   * 销毁事件监听器
   */
  dispose(): void {
    this.disposables.forEach(disposable => disposable.dispose());
    this.disposables = [];
  }
}
