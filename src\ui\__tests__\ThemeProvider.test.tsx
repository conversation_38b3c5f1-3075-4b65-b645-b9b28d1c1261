/**
 * ThemeProvider Tests
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider, useTheme, useThemeToggle } from '../theme/ThemeProvider';
import { lightTheme, darkTheme } from '../theme/themes';

// 测试组件
const TestComponent: React.FC = () => {
  const { theme, themeConfig } = useTheme();
  return (
    <div data-testid="theme-info">
      <span data-testid="theme-name">{theme}</span>
      <span data-testid="primary-color">{themeConfig.colors.primary}</span>
    </div>
  );
};

const ThemeToggleComponent: React.FC = () => {
  const { theme, toggleTheme, setTheme } = useThemeToggle();
  return (
    <div>
      <span data-testid="current-theme">{theme}</span>
      <button data-testid="toggle-theme" onClick={toggleTheme}>
        Toggle Theme
      </button>
      <button data-testid="set-light" onClick={() => setTheme('light')}>
        Set Light
      </button>
      <button data-testid="set-dark" onClick={() => setTheme('dark')}>
        Set Dark
      </button>
    </div>
  );
};

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

describe('ThemeProvider', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset document styles
    document.documentElement.style.cssText = '';
    document.body.className = '';
  });

  it('should provide default theme', () => {
    render(
      <ThemeProvider defaultTheme="dark">
        <TestComponent />
      </ThemeProvider>
    );

    expect(screen.getByTestId('theme-name')).toHaveTextContent('dark');
    expect(screen.getByTestId('primary-color')).toHaveTextContent(darkTheme.colors.primary);
  });

  it('should provide light theme', () => {
    render(
      <ThemeProvider defaultTheme="light">
        <TestComponent />
      </ThemeProvider>
    );

    expect(screen.getByTestId('theme-name')).toHaveTextContent('light');
    expect(screen.getByTestId('primary-color')).toHaveTextContent(lightTheme.colors.primary);
  });

  it('should load theme from localStorage', () => {
    mockLocalStorage.getItem.mockReturnValue('light');

    render(
      <ThemeProvider defaultTheme="dark" storageKey="test-theme">
        <TestComponent />
      </ThemeProvider>
    );

    expect(mockLocalStorage.getItem).toHaveBeenCalledWith('test-theme');
    expect(screen.getByTestId('theme-name')).toHaveTextContent('light');
  });

  it('should save theme to localStorage', () => {
    render(
      <ThemeProvider defaultTheme="dark" storageKey="test-theme">
        <ThemeToggleComponent />
      </ThemeProvider>
    );

    const setLightButton = screen.getByTestId('set-light');
    fireEvent.click(setLightButton);

    expect(mockLocalStorage.setItem).toHaveBeenCalledWith('test-theme', 'light');
    expect(screen.getByTestId('current-theme')).toHaveTextContent('light');
  });

  it('should handle localStorage errors gracefully', () => {
    mockLocalStorage.getItem.mockImplementation(() => {
      throw new Error('localStorage error');
    });

    const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

    render(
      <ThemeProvider defaultTheme="dark">
        <TestComponent />
      </ThemeProvider>
    );

    expect(consoleSpy).toHaveBeenCalledWith(
      'Failed to load theme from localStorage:',
      expect.any(Error)
    );

    consoleSpy.mockRestore();
  });

  it('should apply CSS variables to document', () => {
    render(
      <ThemeProvider defaultTheme="dark">
        <TestComponent />
      </ThemeProvider>
    );

    const root = document.documentElement;
    expect(root.style.getPropertyValue('--color-primary')).toBe(darkTheme.colors.primary);
    expect(root.style.getPropertyValue('--color-background')).toBe(darkTheme.colors.background);
  });

  it('should add theme class to body', () => {
    render(
      <ThemeProvider defaultTheme="dark">
        <TestComponent />
      </ThemeProvider>
    );

    expect(document.body.classList.contains('theme-dark')).toBe(true);
  });

  it('should throw error when useTheme is used outside provider', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

    expect(() => {
      render(<TestComponent />);
    }).toThrow('useTheme must be used within a ThemeProvider');

    consoleSpy.mockRestore();
  });
});

describe('useThemeToggle', () => {
  it('should toggle between themes', () => {
    render(
      <ThemeProvider defaultTheme="dark">
        <ThemeToggleComponent />
      </ThemeProvider>
    );

    expect(screen.getByTestId('current-theme')).toHaveTextContent('dark');

    const toggleButton = screen.getByTestId('toggle-theme');
    fireEvent.click(toggleButton);

    expect(screen.getByTestId('current-theme')).toHaveTextContent('light');

    fireEvent.click(toggleButton);
    expect(screen.getByTestId('current-theme')).toHaveTextContent('high-contrast');

    fireEvent.click(toggleButton);
    expect(screen.getByTestId('current-theme')).toHaveTextContent('dark');
  });

  it('should set specific theme', () => {
    render(
      <ThemeProvider defaultTheme="dark">
        <ThemeToggleComponent />
      </ThemeProvider>
    );

    const setLightButton = screen.getByTestId('set-light');
    fireEvent.click(setLightButton);

    expect(screen.getByTestId('current-theme')).toHaveTextContent('light');

    const setDarkButton = screen.getByTestId('set-dark');
    fireEvent.click(setDarkButton);

    expect(screen.getByTestId('current-theme')).toHaveTextContent('dark');
  });
});

describe('Theme utilities', () => {
  it('should detect system theme preference', () => {
    // Mock matchMedia
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(query => ({
        matches: query === '(prefers-color-scheme: dark)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    });

    const { detectSystemTheme } = require('../theme/themes');
    expect(detectSystemTheme()).toBe('dark');
  });

  it('should validate theme configuration', () => {
    const { validateTheme } = require('../theme/themes');
    
    const validTheme = {
      colors: {
        primary: '#000',
        secondary: '#111',
        background: '#222',
        surface: '#333',
        text: '#444',
        textSecondary: '#555',
        border: '#666',
        accent: '#777',
      },
    };

    const invalidTheme = {
      colors: {
        primary: '#000',
        // Missing required colors
      },
    };

    expect(validateTheme(validTheme)).toBe(true);
    expect(validateTheme(invalidTheme)).toBe(false);
    expect(validateTheme({})).toBe(false);
  });

  it('should generate CSS variables', () => {
    const { generateCSSVariables } = require('../theme/themes');
    
    const css = generateCSSVariables(darkTheme);
    
    expect(css).toContain('--color-primary');
    expect(css).toContain('--spacing-md');
    expect(css).toContain('--font-size-md');
    expect(css).toContain(':root {');
  });

  it('should manipulate colors', () => {
    const { hexToRgb, rgbToHex, lighten, darken } = require('../theme/themes');
    
    // Test color conversion
    expect(hexToRgb('#ff0000')).toEqual({ r: 255, g: 0, b: 0 });
    expect(rgbToHex(255, 0, 0)).toBe('#ff0000');
    
    // Test color manipulation
    expect(lighten('#000000', 0.5)).toBe('#808080');
    expect(darken('#ffffff', 0.5)).toBe('#808080');
  });
});
