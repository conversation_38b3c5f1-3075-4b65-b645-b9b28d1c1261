/**
 * Tools Module Exports
 * 
 * 导出工具集成模块的所有公共接口
 */

export { ToolManager } from './ToolManager';
export { FileSystemTool } from './implementations/FileSystemTool';
export { CodeAnalysisTool } from './implementations/CodeAnalysisTool';
export { GitTool } from './implementations/GitTool';

export type {
  ITool,
  IToolManager,
  ToolDefinition,
  ToolCategory,
  ToolExecutionContext,
  ToolExecutionResult,
  ValidationResult,
  ToolUsageStats,
  ToolHealthStatus,
  ParameterDefinition,
  ToolExample,
  ToolPermission,
  FileSystemOperation,
  FileInfo,
  CodeAnalysisOptions,
  CodeMetrics,
  CodeIssue,
  GitOperation,
  GitStatus,
  TerminalCommand,
  TerminalResult,
  SearchOptions,
  SearchResult,
  RefactorOperation,
  RefactorResult,
  FileChange,
  RefactorConflict,
  TestOptions,
  TestResult,
  TestCoverage,
  TestFailure,
} from './interfaces';

export {
  ToolError,
  ToolValidationError,
  ToolExecutionError,
  ToolPermissionError,
} from './interfaces';

// 工厂函数
export function createToolManager(eventBus: any) {
  return new ToolManager(eventBus);
}

export function createFileSystemTool() {
  return new FileSystemTool();
}

export function createCodeAnalysisTool() {
  return new CodeAnalysisTool();
}

export function createGitTool() {
  return new GitTool();
}

// 工具注册助手
export function registerDefaultTools(toolManager: ToolManager) {
  toolManager.registerTool(new FileSystemTool());
  toolManager.registerTool(new CodeAnalysisTool());
  toolManager.registerTool(new GitTool());
}
