/**
 * WebviewManager Tests
 */

import { WebviewManager } from '../WebviewManager';
import { EventBus } from '@/core/EventBus';

// Mock VS Code API
const mockWebviewPanel = {
  webview: {
    html: '',
    postMessage: jest.fn(),
    onDidReceiveMessage: jest.fn(),
  },
  viewType: 'test.view',
  title: 'Test View',
  visible: true,
  active: true,
  disposed: false,
  onDidDispose: jest.fn(),
  onDidChangeViewState: jest.fn(),
  reveal: jest.fn(),
  dispose: jest.fn(),
};

const mockWindow = {
  createWebviewPanel: jest.fn(),
};

const mockUri = {
  file: jest.fn(),
  with: jest.fn(),
};

// Update global vscode mock
(global as any).vscode = {
  ...(global as any).vscode,
  window: { ...(global as any).vscode.window, ...mockWindow },
  Uri: mockUri,
  ViewColumn: {
    One: 1,
    Two: 2,
    Three: 3,
  },
};

describe('WebviewManager', () => {
  let webviewManager: WebviewManager;
  let eventBus: EventBus;
  let mockExtensionContext: any;

  beforeEach(() => {
    eventBus = new EventBus();
    mockExtensionContext = {
      extensionPath: '/test/extension/path',
    };

    // Reset mocks
    jest.clearAllMocks();

    // Setup mock return values
    mockWindow.createWebviewPanel.mockReturnValue(mockWebviewPanel);
    mockUri.file.mockReturnValue({ with: mockUri.with });
    mockUri.with.mockReturnValue('vscode-resource://test');

    // Update vscode mock with fresh mocks
    const vscode = require('vscode');
    vscode.window = { ...vscode.window, ...mockWindow };
    vscode.Uri = mockUri;

    webviewManager = new WebviewManager(eventBus, mockExtensionContext);
  });

  afterEach(() => {
    webviewManager.dispose();
    eventBus.destroy();
  });

  describe('webview creation', () => {
    it('should create a new webview', () => {
      const config = {
        viewType: 'test.view',
        title: 'Test View',
      };

      const webview = webviewManager.createWebview(config);

      expect(mockWindow.createWebviewPanel).toHaveBeenCalledWith(
        'test.view',
        'Test View',
        1, // ViewColumn.One
        expect.objectContaining({
          enableScripts: true,
          retainContextWhenHidden: true,
        })
      );

      expect(webview).toBeDefined();
      expect(webview.panel).toBe(mockWebviewPanel);
    });

    it('should return existing webview if already exists', () => {
      const config = {
        viewType: 'test.view',
        title: 'Test View',
      };

      const webview1 = webviewManager.createWebview(config);
      const webview2 = webviewManager.createWebview(config);

      expect(webview1).toBe(webview2);
      expect(mockWindow.createWebviewPanel).toHaveBeenCalledTimes(1);
      expect(mockWebviewPanel.reveal).toHaveBeenCalled();
    });

    it('should set HTML content for webview', () => {
      const config = {
        viewType: 'test.view',
        title: 'Test View',
      };

      webviewManager.createWebview(config);

      expect(mockWebviewPanel.webview.html).toContain('<!DOCTYPE html>');
      expect(mockWebviewPanel.webview.html).toContain('<div id="root"></div>');
      expect(mockWebviewPanel.webview.html).toContain('window.viewType = \'test.view\'');
    });
  });

  describe('webview management', () => {
    it('should get existing webview', () => {
      const config = {
        viewType: 'test.view',
        title: 'Test View',
      };

      const createdWebview = webviewManager.createWebview(config);
      const retrievedWebview = webviewManager.getWebview('test.view');

      expect(retrievedWebview).toBe(createdWebview);
    });

    it('should return undefined for non-existent webview', () => {
      const webview = webviewManager.getWebview('non.existent');
      expect(webview).toBeUndefined();
    });

    it('should get active webviews', () => {
      const config1 = { viewType: 'test.view1', title: 'Test View 1' };
      const config2 = { viewType: 'test.view2', title: 'Test View 2' };

      webviewManager.createWebview(config1);
      webviewManager.createWebview(config2);

      const activeWebviews = webviewManager.getActiveWebviews();
      expect(activeWebviews).toHaveLength(2);
    });

    it('should close webview', () => {
      const config = {
        viewType: 'test.view',
        title: 'Test View',
      };

      webviewManager.createWebview(config);
      webviewManager.closeWebview('test.view');

      expect(mockWebviewPanel.dispose).toHaveBeenCalled();
    });
  });

  describe('message handling', () => {
    it('should send message to specific webview', () => {
      const config = {
        viewType: 'test.view',
        title: 'Test View',
      };

      webviewManager.createWebview(config);
      const result = webviewManager.sendMessage('test.view', { type: 'test' });

      expect(result).toBe(true);
      expect(mockWebviewPanel.webview.postMessage).toHaveBeenCalledWith({ type: 'test' });
    });

    it('should return false when sending message to non-existent webview', () => {
      const result = webviewManager.sendMessage('non.existent', { type: 'test' });
      expect(result).toBe(false);
    });

    it('should broadcast message to all webviews', () => {
      const config1 = { viewType: 'test.view1', title: 'Test View 1' };
      const config2 = { viewType: 'test.view2', title: 'Test View 2' };

      // Create two webviews with different panels
      const mockPanel1 = { ...mockWebviewPanel, webview: { ...mockWebviewPanel.webview, postMessage: jest.fn() } };
      const mockPanel2 = { ...mockWebviewPanel, webview: { ...mockWebviewPanel.webview, postMessage: jest.fn() } };

      mockWindow.createWebviewPanel
        .mockReturnValueOnce(mockPanel1)
        .mockReturnValueOnce(mockPanel2);

      webviewManager.createWebview(config1);
      webviewManager.createWebview(config2);

      webviewManager.broadcastMessage({ type: 'broadcast' });

      expect(mockPanel1.webview.postMessage).toHaveBeenCalledWith({ type: 'broadcast' });
      expect(mockPanel2.webview.postMessage).toHaveBeenCalledWith({ type: 'broadcast' });
    });
  });

  describe('statistics', () => {
    it('should provide webview statistics', () => {
      const config1 = { viewType: 'test.view1', title: 'Test View 1' };
      const config2 = { viewType: 'test.view2', title: 'Test View 2' };

      webviewManager.createWebview(config1);
      webviewManager.createWebview(config2);

      const stats = webviewManager.getStats();

      expect(stats.totalWebviews).toBe(2);
      expect(stats.activeWebviews).toBe(2);
      expect(stats.webviewsByType['test.view1']).toBe(1);
      expect(stats.webviewsByType['test.view2']).toBe(1);
    });
  });

  describe('disposal', () => {
    it('should dispose all webviews', () => {
      const config1 = { viewType: 'test.view1', title: 'Test View 1' };
      const config2 = { viewType: 'test.view2', title: 'Test View 2' };

      const mockPanel1 = { ...mockWebviewPanel, dispose: jest.fn() };
      const mockPanel2 = { ...mockWebviewPanel, dispose: jest.fn() };

      mockWindow.createWebviewPanel
        .mockReturnValueOnce(mockPanel1)
        .mockReturnValueOnce(mockPanel2);

      webviewManager.createWebview(config1);
      webviewManager.createWebview(config2);

      webviewManager.dispose();

      expect(mockPanel1.dispose).toHaveBeenCalled();
      expect(mockPanel2.dispose).toHaveBeenCalled();
    });
  });
});
