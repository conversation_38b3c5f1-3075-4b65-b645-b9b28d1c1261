{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "node", "jsx": "react-jsx", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "strict": true, "noEmit": false, "outDir": "./out", "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src/ui/**/*", "src/types/**/*"], "exclude": ["node_modules", "out", "dist", "**/*.test.ts", "**/*.test.tsx"]}