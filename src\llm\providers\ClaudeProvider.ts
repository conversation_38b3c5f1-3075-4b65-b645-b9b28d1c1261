/**
 * <PERSON> Provider - Anthropic Claude API集成
 * 
 * 实现Anthropic Claude API的LLM提供者
 */

import { 
  ILLMProvider, 
  LLMRequestConfig, 
  LLMResponse, 
  LLMStreamChunk, 
  ModelInfo, 
  HealthStatus,
  ClaudeConfig,
  LLMError,
  RateLimitError,
  QuotaExceededError,
  ModelUnavailableError
} from '../interfaces';
import { Message, ToolCall, ToolResult, LLMProvider } from '@/types';

export class Claude<PERSON><PERSON><PERSON> implements ILLMProvider {
  public readonly name: LLMProvider = 'claude';
  public readonly supportsStreaming = true;
  public readonly supportsTools = true;
  public readonly supportsVision = true;

  private config: ClaudeConfig;
  private baseUrl = 'https://api.anthropic.com/v1';

  public readonly models = [
    'claude-3-5-sonnet-20241022',
    'claude-3-5-haiku-20241022',
    'claude-3-opus-20240229',
    'claude-3-sonnet-20240229',
    'claude-3-haiku-20240307',
  ];

  private modelInfoMap: Record<string, ModelInfo> = {
    'claude-3-5-sonnet-20241022': {
      name: 'claude-3-5-sonnet-20241022',
      displayName: 'Claude 3.5 Sonnet',
      description: 'Most intelligent model with best performance',
      maxTokens: 8192,
      inputCost: 0.003,
      outputCost: 0.015,
      supportsTools: true,
      supportsVision: true,
      contextWindow: 200000,
    },
    'claude-3-5-haiku-20241022': {
      name: 'claude-3-5-haiku-20241022',
      displayName: 'Claude 3.5 Haiku',
      description: 'Fastest model with good performance',
      maxTokens: 8192,
      inputCost: 0.00025,
      outputCost: 0.00125,
      supportsTools: true,
      supportsVision: true,
      contextWindow: 200000,
    },
    'claude-3-opus-20240229': {
      name: 'claude-3-opus-20240229',
      displayName: 'Claude 3 Opus',
      description: 'Most powerful model for complex tasks',
      maxTokens: 4096,
      inputCost: 0.015,
      outputCost: 0.075,
      supportsTools: true,
      supportsVision: true,
      contextWindow: 200000,
    },
    'claude-3-sonnet-20240229': {
      name: 'claude-3-sonnet-20240229',
      displayName: 'Claude 3 Sonnet',
      description: 'Balanced model for most tasks',
      maxTokens: 4096,
      inputCost: 0.003,
      outputCost: 0.015,
      supportsTools: true,
      supportsVision: true,
      contextWindow: 200000,
    },
    'claude-3-haiku-20240307': {
      name: 'claude-3-haiku-20240307',
      displayName: 'Claude 3 Haiku',
      description: 'Fast and efficient model',
      maxTokens: 4096,
      inputCost: 0.00025,
      outputCost: 0.00125,
      supportsTools: true,
      supportsVision: true,
      contextWindow: 200000,
    },
  };

  constructor(config: ClaudeConfig) {
    this.config = config;
    if (config.baseUrl) {
      this.baseUrl = config.baseUrl;
    }
  }

  configure(config: ClaudeConfig): void {
    this.config = { ...this.config, ...config };
    if (config.baseUrl) {
      this.baseUrl = config.baseUrl;
    }
  }

  async validateConfig(): Promise<boolean> {
    try {
      const response = await this.makeRequest('/messages', {
        method: 'POST',
        body: JSON.stringify({
          model: 'claude-3-haiku-20240307',
          max_tokens: 1,
          messages: [{ role: 'user', content: 'test' }],
        }),
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  getModelInfo(model: string): ModelInfo | undefined {
    return this.modelInfoMap[model];
  }

  async healthCheck(): Promise<HealthStatus> {
    const startTime = Date.now();
    
    try {
      const response = await this.makeRequest('/messages', {
        method: 'POST',
        body: JSON.stringify({
          model: 'claude-3-haiku-20240307',
          max_tokens: 1,
          messages: [{ role: 'user', content: 'health check' }],
        }),
      });

      const latency = Date.now() - startTime;
      
      return {
        status: response.ok ? 'healthy' : 'degraded',
        latency,
        timestamp: Date.now(),
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: (error as Error).message,
        timestamp: Date.now(),
      };
    }
  }

  async chat(messages: Message[], config: LLMRequestConfig): Promise<LLMResponse> {
    try {
      const claudeMessages = this.convertMessages(messages);
      const tools = config.tools ? this.convertTools(config.tools) : undefined;

      const requestBody: any = {
        model: config.model,
        max_tokens: config.maxTokens || 4096,
        messages: claudeMessages,
        temperature: config.temperature,
        top_p: config.topP,
        stop_sequences: config.stop,
      };

      if (tools) {
        requestBody.tools = tools;
      }

      const response = await this.makeRequest('/messages', {
        method: 'POST',
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`Claude API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      const toolCalls = data.content
        ?.filter((c: any) => c.type === 'tool_use')
        ?.map((c: any) => ({
          id: c.id,
          name: c.name,
          arguments: c.input,
        }));

      const textContent = data.content
        ?.filter((c: any) => c.type === 'text')
        ?.map((c: any) => c.text)
        ?.join('') || '';

      return {
        id: data.id,
        content: textContent,
        role: 'assistant',
        toolCalls,
        usage: data.usage ? {
          promptTokens: data.usage.input_tokens,
          completionTokens: data.usage.output_tokens,
          totalTokens: data.usage.input_tokens + data.usage.output_tokens,
        } : undefined,
        model: data.model,
        finishReason: data.stop_reason === 'end_turn' ? 'stop' : data.stop_reason,
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async* chatStream(messages: Message[], config: LLMRequestConfig): AsyncIterable<LLMStreamChunk> {
    try {
      const claudeMessages = this.convertMessages(messages);
      const tools = config.tools ? this.convertTools(config.tools) : undefined;

      const requestBody: any = {
        model: config.model,
        max_tokens: config.maxTokens || 4096,
        messages: claudeMessages,
        temperature: config.temperature,
        top_p: config.topP,
        stop_sequences: config.stop,
        stream: true,
      };

      if (tools) {
        requestBody.tools = tools;
      }

      const response = await this.makeRequest('/messages', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Accept': 'text/event-stream',
        },
      });

      if (!response.ok) {
        throw new Error(`Claude API error: ${response.status} ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') return;

              try {
                const parsed = JSON.parse(data);
                if (parsed.type === 'content_block_delta') {
                  yield {
                    id: parsed.message?.id || '',
                    delta: {
                      content: parsed.delta?.text,
                    },
                  };
                } else if (parsed.type === 'message_stop') {
                  return;
                }
              } catch (e) {
                // Skip invalid JSON
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async callTool(toolCall: ToolCall, context?: any): Promise<ToolResult> {
    // 工具调用的具体实现将在工具集成模块中处理
    return {
      id: toolCall.id,
      result: `Tool ${toolCall.name} called with arguments: ${JSON.stringify(toolCall.arguments)}`,
    };
  }

  private convertMessages(messages: Message[]): any[] {
    return messages
      .filter(msg => msg.type !== 'system') // Claude handles system messages differently
      .map(msg => {
        if (msg.type === 'user') {
          return {
            role: 'user',
            content: msg.content,
          };
        } else if (msg.type === 'assistant') {
          const content: any[] = [];
          
          if (msg.content) {
            content.push({ type: 'text', text: msg.content });
          }
          
          if (msg.toolCalls) {
            content.push(...msg.toolCalls.map(tc => ({
              type: 'tool_use',
              id: tc.id,
              name: tc.name,
              input: tc.arguments,
            })));
          }

          return {
            role: 'assistant',
            content,
          };
        } else if (msg.type === 'tool') {
          return {
            role: 'user',
            content: [
              {
                type: 'tool_result',
                tool_use_id: msg.metadata?.toolCallId,
                content: msg.content,
              }
            ],
          };
        }
      });
  }

  private convertTools(tools: any[]): any[] {
    return tools.map(tool => ({
      name: tool.name,
      description: tool.description,
      input_schema: tool.parameters,
    }));
  }

  private async makeRequest(endpoint: string, options: RequestInit): Promise<Response> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const headers = {
      'Content-Type': 'application/json',
      'x-api-key': this.config.apiKey,
      'anthropic-version': this.config.version || '2023-06-01',
      ...this.config.customHeaders,
      ...options.headers,
    };

    return fetch(url, {
      ...options,
      headers,
    });
  }

  private handleError(error: any): Error {
    if (error.status === 429) {
      const retryAfter = error.headers?.['retry-after'];
      return new RateLimitError('claude', retryAfter ? parseInt(retryAfter) : undefined, error);
    }

    if (error.status === 402) {
      return new QuotaExceededError('claude', error);
    }

    if (error.status === 404) {
      return new ModelUnavailableError('claude', 'unknown', error);
    }

    return new LLMError(
      error.message || 'Unknown Claude error',
      'claude',
      error.code || 'UNKNOWN_ERROR',
      error.status,
      error
    );
  }
}
