/**
 * Code Analysis Tool - 代码分析工具
 * 
 * 提供代码分析、复杂度计算、依赖分析等功能
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import {
  ITool,
  ToolDefinition,
  ToolExecutionContext,
  ToolExecutionResult,
  ValidationResult,
  ToolUsageStats,
  CodeAnalysisOptions,
  CodeMetrics,
  CodeIssue,
  ToolExecutionError,
} from '../interfaces';

export class CodeAnalysisTool implements ITool {
  public readonly definition: ToolDefinition = {
    name: 'code_analysis',
    description: 'Analyze code for metrics, complexity, dependencies, and potential issues',
    category: 'code-analysis',
    parameters: {
      type: 'object',
      properties: {
        path: {
          type: 'string',
          description: 'File or directory path to analyze',
        },
        type: {
          type: 'string',
          description: 'Type of analysis to perform',
          enum: ['metrics', 'complexity', 'dependencies', 'issues', 'structure', 'all'],
          default: 'all',
        },
        includeComments: {
          type: 'boolean',
          description: 'Include comments in analysis',
          default: true,
        },
        includeTests: {
          type: 'boolean',
          description: 'Include test files in analysis',
          default: false,
        },
        maxDepth: {
          type: 'number',
          description: 'Maximum directory depth to analyze',
          default: 10,
        },
        languages: {
          type: 'array',
          description: 'Programming languages to analyze',
          items: { type: 'string' },
          default: ['javascript', 'typescript', 'python', 'java', 'csharp'],
        },
        excludePatterns: {
          type: 'array',
          description: 'File patterns to exclude',
          items: { type: 'string' },
          default: ['node_modules', '.git', 'dist', 'build'],
        },
      },
      required: ['path'],
    },
    permissions: ['read-files'],
    examples: [
      {
        description: 'Analyze code metrics for a file',
        parameters: {
          path: './src/index.ts',
          type: 'metrics',
        },
      },
      {
        description: 'Analyze complexity for entire project',
        parameters: {
          path: './src',
          type: 'complexity',
          includeTests: true,
        },
      },
    ],
  };

  private usageStats: ToolUsageStats = {
    totalExecutions: 0,
    successfulExecutions: 0,
    failedExecutions: 0,
    averageExecutionTime: 0,
  };

  async execute(
    parameters: Record<string, any>,
    context: ToolExecutionContext
  ): Promise<ToolExecutionResult> {
    const startTime = Date.now();
    this.usageStats.totalExecutions++;

    try {
      const filePath = this.resolvePath(parameters.path, context.workspaceRoot);
      const analysisType = parameters.type || 'all';
      
      const options: CodeAnalysisOptions = {
        includeComments: parameters.includeComments ?? true,
        includeTests: parameters.includeTests ?? false,
        maxDepth: parameters.maxDepth ?? 10,
        languages: parameters.languages ?? ['javascript', 'typescript', 'python', 'java', 'csharp'],
        excludePatterns: parameters.excludePatterns ?? ['node_modules', '.git', 'dist', 'build'],
      };

      let result: any;

      switch (analysisType) {
        case 'metrics':
          result = await this.analyzeMetrics(filePath, options);
          break;
        case 'complexity':
          result = await this.analyzeComplexity(filePath, options);
          break;
        case 'dependencies':
          result = await this.analyzeDependencies(filePath, options);
          break;
        case 'issues':
          result = await this.analyzeIssues(filePath, options);
          break;
        case 'structure':
          result = await this.analyzeStructure(filePath, options);
          break;
        case 'all':
          result = await this.analyzeAll(filePath, options);
          break;
        default:
          throw new ToolExecutionError('code_analysis', `Unknown analysis type: ${analysisType}`);
      }

      const duration = Date.now() - startTime;
      this.updateStats(duration, true);

      return {
        success: true,
        result,
        metadata: { duration },
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      this.updateStats(duration, false);

      return {
        success: false,
        error: (error as Error).message,
        metadata: { duration },
      };
    }
  }

  validate(parameters: Record<string, any>): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!parameters.path) {
      errors.push('Path is required');
    }

    if (parameters.type && !['metrics', 'complexity', 'dependencies', 'issues', 'structure', 'all'].includes(parameters.type)) {
      errors.push('Invalid analysis type');
    }

    if (parameters.maxDepth && (parameters.maxDepth < 1 || parameters.maxDepth > 50)) {
      warnings.push('Max depth should be between 1 and 50');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  }

  getUsageStats(): ToolUsageStats {
    return { ...this.usageStats };
  }

  // 私有方法
  private resolvePath(filePath: string, workspaceRoot?: string): string {
    if (path.isAbsolute(filePath)) {
      return filePath;
    }
    
    if (workspaceRoot) {
      return path.resolve(workspaceRoot, filePath);
    }
    
    return path.resolve(filePath);
  }

  private async analyzeMetrics(filePath: string, options: CodeAnalysisOptions): Promise<CodeMetrics> {
    const files = await this.getCodeFiles(filePath, options);
    let totalLines = 0;
    let totalComplexity = 0;
    const dependencies: Set<string> = new Set();
    const issues: CodeIssue[] = [];

    for (const file of files) {
      try {
        const content = await fs.readFile(file, 'utf8');
        const fileMetrics = this.analyzeFileMetrics(content, file);
        
        totalLines += fileMetrics.linesOfCode;
        totalComplexity += fileMetrics.complexity;
        
        fileMetrics.dependencies.forEach(dep => dependencies.add(dep));
        issues.push(...fileMetrics.issues);
      } catch (error) {
        issues.push({
          type: 'error',
          message: `Failed to analyze file: ${(error as Error).message}`,
          file,
          line: 0,
          column: 0,
          severity: 5,
        });
      }
    }

    return {
      linesOfCode: totalLines,
      complexity: totalComplexity,
      maintainabilityIndex: this.calculateMaintainabilityIndex(totalLines, totalComplexity),
      dependencies: Array.from(dependencies),
      issues,
    };
  }

  private async analyzeComplexity(filePath: string, options: CodeAnalysisOptions): Promise<any> {
    const files = await this.getCodeFiles(filePath, options);
    const complexityResults: Array<{
      file: string;
      complexity: number;
      functions: Array<{ name: string; complexity: number; line: number }>;
    }> = [];

    for (const file of files) {
      try {
        const content = await fs.readFile(file, 'utf8');
        const complexity = this.calculateFileComplexity(content, file);
        complexityResults.push(complexity);
      } catch (error) {
        // 忽略无法读取的文件
      }
    }

    return {
      files: complexityResults,
      summary: {
        totalFiles: complexityResults.length,
        averageComplexity: complexityResults.reduce((sum, f) => sum + f.complexity, 0) / complexityResults.length,
        highComplexityFiles: complexityResults.filter(f => f.complexity > 10).length,
      },
    };
  }

  private async analyzeDependencies(filePath: string, options: CodeAnalysisOptions): Promise<any> {
    const files = await this.getCodeFiles(filePath, options);
    const dependencies: Map<string, Set<string>> = new Map();
    const externalDependencies: Set<string> = new Set();

    for (const file of files) {
      try {
        const content = await fs.readFile(file, 'utf8');
        const fileDeps = this.extractDependencies(content, file);
        
        dependencies.set(file, new Set(fileDeps.internal));
        fileDeps.external.forEach(dep => externalDependencies.add(dep));
      } catch (error) {
        // 忽略无法读取的文件
      }
    }

    return {
      internal: Object.fromEntries(
        Array.from(dependencies.entries()).map(([file, deps]) => [file, Array.from(deps)])
      ),
      external: Array.from(externalDependencies),
      graph: this.buildDependencyGraph(dependencies),
    };
  }

  private async analyzeIssues(filePath: string, options: CodeAnalysisOptions): Promise<CodeIssue[]> {
    const files = await this.getCodeFiles(filePath, options);
    const allIssues: CodeIssue[] = [];

    for (const file of files) {
      try {
        const content = await fs.readFile(file, 'utf8');
        const issues = this.findCodeIssues(content, file);
        allIssues.push(...issues);
      } catch (error) {
        allIssues.push({
          type: 'error',
          message: `Failed to analyze file: ${(error as Error).message}`,
          file,
          line: 0,
          column: 0,
          severity: 5,
        });
      }
    }

    return allIssues;
  }

  private async analyzeStructure(filePath: string, options: CodeAnalysisOptions): Promise<any> {
    const files = await this.getCodeFiles(filePath, options);
    const structure: any = {
      directories: new Set(),
      fileTypes: new Map(),
      totalFiles: files.length,
    };

    for (const file of files) {
      const dir = path.dirname(file);
      const ext = path.extname(file);
      
      structure.directories.add(dir);
      structure.fileTypes.set(ext, (structure.fileTypes.get(ext) || 0) + 1);
    }

    return {
      directories: Array.from(structure.directories),
      fileTypes: Object.fromEntries(structure.fileTypes),
      totalFiles: structure.totalFiles,
    };
  }

  private async analyzeAll(filePath: string, options: CodeAnalysisOptions): Promise<any> {
    const [metrics, complexity, dependencies, issues, structure] = await Promise.all([
      this.analyzeMetrics(filePath, options),
      this.analyzeComplexity(filePath, options),
      this.analyzeDependencies(filePath, options),
      this.analyzeIssues(filePath, options),
      this.analyzeStructure(filePath, options),
    ]);

    return {
      metrics,
      complexity,
      dependencies,
      issues,
      structure,
    };
  }

  private async getCodeFiles(filePath: string, options: CodeAnalysisOptions): Promise<string[]> {
    const files: string[] = [];
    const stats = await fs.stat(filePath);

    if (stats.isFile()) {
      if (this.isCodeFile(filePath, options)) {
        files.push(filePath);
      }
    } else if (stats.isDirectory()) {
      await this.collectCodeFiles(filePath, files, options, 0);
    }

    return files;
  }

  private async collectCodeFiles(
    dirPath: string,
    files: string[],
    options: CodeAnalysisOptions,
    depth: number
  ): Promise<void> {
    if (depth >= (options.maxDepth || 10)) {
      return;
    }

    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);

        if (this.shouldExclude(fullPath, options.excludePatterns || [])) {
          continue;
        }

        if (entry.isDirectory()) {
          await this.collectCodeFiles(fullPath, files, options, depth + 1);
        } else if (this.isCodeFile(fullPath, options)) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      // 忽略无法访问的目录
    }
  }

  private isCodeFile(filePath: string, options: CodeAnalysisOptions): boolean {
    const ext = path.extname(filePath).toLowerCase();
    const basename = path.basename(filePath).toLowerCase();

    // 检查是否为测试文件
    if (!options.includeTests && this.isTestFile(basename)) {
      return false;
    }

    // 检查文件扩展名
    const codeExtensions = [
      '.js', '.jsx', '.ts', '.tsx',
      '.py', '.java', '.cs',
      '.cpp', '.c', '.h',
      '.go', '.rs', '.php',
      '.rb', '.swift', '.kt',
    ];

    return codeExtensions.includes(ext);
  }

  private isTestFile(filename: string): boolean {
    return filename.includes('.test.') || 
           filename.includes('.spec.') || 
           filename.includes('_test.') ||
           filename.includes('_spec.');
  }

  private shouldExclude(filePath: string, excludePatterns: string[]): boolean {
    return excludePatterns.some(pattern => filePath.includes(pattern));
  }

  private analyzeFileMetrics(content: string, filePath: string): CodeMetrics {
    const lines = content.split('\n');
    const linesOfCode = this.countLinesOfCode(lines);
    const complexity = this.calculateComplexity(content);
    const dependencies = this.extractDependencies(content, filePath);
    const issues = this.findCodeIssues(content, filePath);

    return {
      linesOfCode,
      complexity,
      maintainabilityIndex: this.calculateMaintainabilityIndex(linesOfCode, complexity),
      dependencies: [...dependencies.internal, ...dependencies.external],
      issues,
    };
  }

  private countLinesOfCode(lines: string[]): number {
    return lines.filter(line => {
      const trimmed = line.trim();
      return trimmed.length > 0 && !trimmed.startsWith('//') && !trimmed.startsWith('/*');
    }).length;
  }

  private calculateComplexity(content: string): number {
    // 简化的圈复杂度计算
    const complexityKeywords = [
      'if', 'else', 'while', 'for', 'switch', 'case',
      'catch', 'try', '&&', '||', '?', ':'
    ];

    let complexity = 1; // 基础复杂度

    for (const keyword of complexityKeywords) {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g');
      const matches = content.match(regex);
      if (matches) {
        complexity += matches.length;
      }
    }

    return complexity;
  }

  private calculateFileComplexity(content: string, filePath: string): any {
    // 这里应该实现更详细的复杂度分析
    const complexity = this.calculateComplexity(content);
    
    return {
      file: filePath,
      complexity,
      functions: [], // 简化实现
    };
  }

  private extractDependencies(content: string, filePath: string): { internal: string[]; external: string[] } {
    const internal: string[] = [];
    const external: string[] = [];

    // 匹配 import 语句
    const importRegex = /import\s+.*?\s+from\s+['"]([^'"]+)['"]/g;
    let match;

    while ((match = importRegex.exec(content)) !== null) {
      const dep = match[1];
      if (dep.startsWith('.') || dep.startsWith('/')) {
        internal.push(dep);
      } else {
        external.push(dep);
      }
    }

    // 匹配 require 语句
    const requireRegex = /require\(['"]([^'"]+)['"]\)/g;
    while ((match = requireRegex.exec(content)) !== null) {
      const dep = match[1];
      if (dep.startsWith('.') || dep.startsWith('/')) {
        internal.push(dep);
      } else {
        external.push(dep);
      }
    }

    return { internal, external };
  }

  private findCodeIssues(content: string, filePath: string): CodeIssue[] {
    const issues: CodeIssue[] = [];
    const lines = content.split('\n');

    lines.forEach((line, index) => {
      // 检查常见问题
      if (line.includes('console.log')) {
        issues.push({
          type: 'warning',
          message: 'Console.log statement found',
          file: filePath,
          line: index + 1,
          column: line.indexOf('console.log'),
          rule: 'no-console',
          severity: 2,
        });
      }

      if (line.includes('TODO') || line.includes('FIXME')) {
        issues.push({
          type: 'info',
          message: 'TODO/FIXME comment found',
          file: filePath,
          line: index + 1,
          column: 0,
          rule: 'todo-comment',
          severity: 1,
        });
      }
    });

    return issues;
  }

  private buildDependencyGraph(dependencies: Map<string, Set<string>>): any {
    // 简化的依赖图构建
    const graph: any = {};
    
    for (const [file, deps] of dependencies) {
      graph[file] = Array.from(deps);
    }
    
    return graph;
  }

  private calculateMaintainabilityIndex(linesOfCode: number, complexity: number): number {
    // 简化的可维护性指数计算
    const volume = linesOfCode * Math.log2(linesOfCode || 1);
    const maintainability = Math.max(0, (171 - 5.2 * Math.log(volume) - 0.23 * complexity - 16.2 * Math.log(linesOfCode || 1)) * 100 / 171);
    return Math.round(maintainability);
  }

  private updateStats(duration: number, success: boolean): void {
    if (success) {
      this.usageStats.successfulExecutions++;
    } else {
      this.usageStats.failedExecutions++;
    }

    const totalTime = this.usageStats.averageExecutionTime * (this.usageStats.totalExecutions - 1) + duration;
    this.usageStats.averageExecutionTime = totalTime / this.usageStats.totalExecutions;
    this.usageStats.lastUsed = Date.now();
  }
}
