/**
 * RAG Index Manager - RAG索引管理器
 * 
 * 企业级RAG检索系统的核心索引管理组件，负责多层级索引架构的构建和维护
 */

import * as vscode from 'vscode';
import * as path from 'path';
import { EventBus } from './EventBus';
import { EnhancedCodeContext } from './EnhancedCodeContextExtractor';

export interface IndexedItem {
  id: string;
  type: 'file' | 'function' | 'class' | 'interface' | 'variable' | 'comment' | 'documentation';
  filePath: string;
  content: string;
  metadata: IndexMetadata;
  embedding?: number[];
  lastUpdated: Date;
  hash: string;
}

export interface IndexMetadata {
  language: string;
  lineStart: number;
  lineEnd: number;
  name?: string;
  signature?: string;
  parameters?: string[];
  returnType?: string;
  dependencies?: string[];
  complexity?: number;
  tags?: string[];
  documentation?: string;
  score?: number;
}

export interface IndexStats {
  totalItems: number;
  fileCount: number;
  functionCount: number;
  classCount: number;
  lastIndexTime: Date;
  indexSize: number; // in bytes
  averageEmbeddingTime: number; // in ms
}

export interface IndexingOptions {
  includeComments: boolean;
  includeDocumentation: boolean;
  maxFileSize: number;
  supportedLanguages: string[];
  embeddingModel: string;
  batchSize: number;
}

export class RAGIndexManager {
  private eventBus: EventBus;
  private indexedItems: Map<string, IndexedItem> = new Map();
  private fileHashes: Map<string, string> = new Map();
  private indexingQueue: string[] = [];
  private isIndexing = false;
  private indexStats: IndexStats;
  private options: IndexingOptions;

  constructor(eventBus: EventBus, options?: Partial<IndexingOptions>) {
    this.eventBus = eventBus;
    this.options = {
      includeComments: true,
      includeDocumentation: true,
      maxFileSize: 1024 * 1024, // 1MB
      supportedLanguages: ['typescript', 'javascript', 'python', 'java', 'csharp', 'go', 'rust'],
      embeddingModel: 'text-embedding-3-small',
      batchSize: 50,
      ...options
    };
    
    this.indexStats = {
      totalItems: 0,
      fileCount: 0,
      functionCount: 0,
      classCount: 0,
      lastIndexTime: new Date(),
      indexSize: 0,
      averageEmbeddingTime: 0
    };
  }

  /**
   * 初始化索引系统
   */
  async initialize(): Promise<void> {
    await this.eventBus.emit({
      type: 'rag.index_manager_initializing',
      source: 'RAGIndexManager'
    });

    try {
      // 加载已有索引
      await this.loadExistingIndex();
      
      // 启动增量索引
      await this.startIncrementalIndexing();

      await this.eventBus.emit({
        type: 'rag.index_manager_initialized',
        source: 'RAGIndexManager',
        stats: this.indexStats
      });

      console.log('RAG Index Manager initialized successfully');
    } catch (error) {
      await this.eventBus.emit({
        type: 'rag.index_manager_init_failed',
        source: 'RAGIndexManager',
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * 构建完整索引
   */
  async buildFullIndex(): Promise<void> {
    const startTime = Date.now();
    
    await this.eventBus.emit({
      type: 'rag.full_index_started',
      source: 'RAGIndexManager'
    });

    try {
      // 清除现有索引
      this.indexedItems.clear();
      this.fileHashes.clear();

      // 获取所有支持的文件
      const files = await this.getAllSupportedFiles();
      
      // 批量处理文件
      const batches = this.createBatches(files, this.options.batchSize);
      
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        await this.processBatch(batch, i + 1, batches.length);
        
        // 发送进度更新
        await this.eventBus.emit({
          type: 'rag.index_progress',
          source: 'RAGIndexManager',
          progress: ((i + 1) / batches.length) * 100,
          processedFiles: (i + 1) * this.options.batchSize,
          totalFiles: files.length
        });
      }

      // 更新统计信息
      this.updateIndexStats();
      
      const duration = Date.now() - startTime;
      
      await this.eventBus.emit({
        type: 'rag.full_index_completed',
        source: 'RAGIndexManager',
        duration,
        stats: this.indexStats
      });

      console.log(`Full index completed in ${duration}ms`);
    } catch (error) {
      await this.eventBus.emit({
        type: 'rag.full_index_failed',
        source: 'RAGIndexManager',
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * 增量更新索引
   */
  async updateIndex(filePaths: string[]): Promise<void> {
    if (this.isIndexing) {
      // 添加到队列
      this.indexingQueue.push(...filePaths);
      return;
    }

    this.isIndexing = true;

    try {
      await this.eventBus.emit({
        type: 'rag.incremental_index_started',
        source: 'RAGIndexManager',
        fileCount: filePaths.length
      });

      for (const filePath of filePaths) {
        await this.indexFile(filePath);
      }

      this.updateIndexStats();

      await this.eventBus.emit({
        type: 'rag.incremental_index_completed',
        source: 'RAGIndexManager',
        fileCount: filePaths.length,
        stats: this.indexStats
      });

    } catch (error) {
      await this.eventBus.emit({
        type: 'rag.incremental_index_failed',
        source: 'RAGIndexManager',
        error: (error as Error).message
      });
      throw error;
    } finally {
      this.isIndexing = false;
      
      // 处理队列中的文件
      if (this.indexingQueue.length > 0) {
        const queuedFiles = [...this.indexingQueue];
        this.indexingQueue = [];
        await this.updateIndex(queuedFiles);
      }
    }
  }

  /**
   * 删除文件索引
   */
  async removeFileIndex(filePath: string): Promise<void> {
    const itemsToRemove: string[] = [];
    
    for (const [id, item] of this.indexedItems) {
      if (item.filePath === filePath) {
        itemsToRemove.push(id);
      }
    }

    for (const id of itemsToRemove) {
      this.indexedItems.delete(id);
    }

    this.fileHashes.delete(filePath);
    this.updateIndexStats();

    await this.eventBus.emit({
      type: 'rag.file_index_removed',
      source: 'RAGIndexManager',
      filePath,
      removedItems: itemsToRemove.length
    });
  }

  /**
   * 搜索索引项
   */
  searchIndex(query: string, options?: {
    type?: IndexedItem['type'][];
    language?: string;
    limit?: number;
    threshold?: number;
  }): IndexedItem[] {
    const results: IndexedItem[] = [];
    const queryLower = query.toLowerCase();
    
    for (const item of this.indexedItems.values()) {
      // 类型过滤
      if (options?.type && !options.type.includes(item.type)) {
        continue;
      }
      
      // 语言过滤
      if (options?.language && item.metadata.language !== options.language) {
        continue;
      }

      // 简单的文本匹配评分
      let score = 0;
      const contentLower = item.content.toLowerCase();
      const nameLower = item.metadata.name?.toLowerCase() || '';
      
      if (nameLower.includes(queryLower)) {
        score += 10;
      }
      
      if (contentLower.includes(queryLower)) {
        score += 5;
      }
      
      if (item.metadata.tags?.some(tag => tag.toLowerCase().includes(queryLower))) {
        score += 3;
      }

      if (score > (options?.threshold || 0)) {
        results.push({ ...item, metadata: { ...item.metadata, score } });
      }
    }

    // 按评分排序
    results.sort((a, b) => (b.metadata as any).score - (a.metadata as any).score);
    
    return results.slice(0, options?.limit || 50);
  }

  /**
   * 获取索引统计信息
   */
  getIndexStats(): IndexStats {
    return { ...this.indexStats };
  }

  /**
   * 获取索引项
   */
  getIndexedItem(id: string): IndexedItem | undefined {
    return this.indexedItems.get(id);
  }

  /**
   * 获取文件的所有索引项
   */
  getFileIndexedItems(filePath: string): IndexedItem[] {
    const items: IndexedItem[] = [];
    
    for (const item of this.indexedItems.values()) {
      if (item.filePath === filePath) {
        items.push(item);
      }
    }
    
    return items;
  }

  /**
   * 私有方法：获取所有支持的文件
   */
  private async getAllSupportedFiles(): Promise<string[]> {
    const files: string[] = [];
    const workspaceFolders = vscode.workspace.workspaceFolders;
    
    if (!workspaceFolders) {
      return files;
    }

    for (const folder of workspaceFolders) {
      const extensions = this.getSupportedExtensions();
      const pattern = `**/*.{${extensions.join(',')}}`;
      
      const foundFiles = await vscode.workspace.findFiles(
        new vscode.RelativePattern(folder, pattern),
        '**/node_modules/**',
        10000
      );

      files.push(...foundFiles.map(uri => uri.fsPath));
    }

    return files;
  }

  /**
   * 私有方法：获取支持的文件扩展名
   */
  private getSupportedExtensions(): string[] {
    const extensionMap: { [key: string]: string[] } = {
      'typescript': ['ts', 'tsx'],
      'javascript': ['js', 'jsx'],
      'python': ['py'],
      'java': ['java'],
      'csharp': ['cs'],
      'go': ['go'],
      'rust': ['rs']
    };

    const extensions: string[] = [];
    for (const language of this.options.supportedLanguages) {
      if (extensionMap[language]) {
        extensions.push(...extensionMap[language]);
      }
    }

    return extensions;
  }

  /**
   * 私有方法：创建批次
   */
  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    
    return batches;
  }

  /**
   * 私有方法：处理批次
   */
  private async processBatch(filePaths: string[], batchNumber: number, totalBatches: number): Promise<void> {
    console.log(`Processing batch ${batchNumber}/${totalBatches} (${filePaths.length} files)`);
    
    const promises = filePaths.map(filePath => this.indexFile(filePath));
    await Promise.allSettled(promises);
  }

  /**
   * 私有方法：索引单个文件
   */
  private async indexFile(filePath: string): Promise<void> {
    try {
      // 检查文件大小
      const stat = await vscode.workspace.fs.stat(vscode.Uri.file(filePath));
      if (stat.size > this.options.maxFileSize) {
        console.warn(`File too large, skipping: ${filePath}`);
        return;
      }

      // 读取文件内容
      const document = await vscode.workspace.openTextDocument(filePath);
      const content = document.getText();
      
      // 计算文件哈希
      const hash = this.calculateHash(content);
      const existingHash = this.fileHashes.get(filePath);
      
      // 如果文件未变化，跳过
      if (existingHash === hash) {
        return;
      }

      // 移除旧的索引项
      await this.removeFileIndex(filePath);
      
      // 解析文件内容
      const items = await this.parseFileContent(document, content);
      
      // 添加到索引
      for (const item of items) {
        this.indexedItems.set(item.id, item);
      }
      
      // 更新文件哈希
      this.fileHashes.set(filePath, hash);

    } catch (error) {
      console.error(`Failed to index file ${filePath}:`, error);
    }
  }

  /**
   * 私有方法：解析文件内容
   */
  private async parseFileContent(document: vscode.TextDocument, content: string): Promise<IndexedItem[]> {
    const items: IndexedItem[] = [];
    const language = document.languageId;
    const filePath = document.uri.fsPath;

    // 添加文件级索引
    items.push({
      id: this.generateId('file', filePath),
      type: 'file',
      filePath,
      content: content.length > 1000 ? content.substring(0, 1000) + '...' : content,
      metadata: {
        language,
        lineStart: 1,
        lineEnd: document.lineCount,
        name: path.basename(filePath),
        tags: [language, 'file']
      },
      lastUpdated: new Date(),
      hash: this.calculateHash(content)
    });

    // 解析代码结构
    try {
      const symbols = await vscode.commands.executeCommand<vscode.DocumentSymbol[]>(
        'vscode.executeDocumentSymbolProvider',
        document.uri
      );

      if (symbols) {
        for (const symbol of symbols) {
          const symbolItems = this.parseSymbol(symbol, document, filePath, language);
          items.push(...symbolItems);
        }
      }
    } catch (error) {
      console.warn(`Failed to parse symbols for ${filePath}:`, error);
    }

    return items;
  }

  /**
   * 私有方法：解析符号
   */
  private parseSymbol(
    symbol: vscode.DocumentSymbol,
    document: vscode.TextDocument,
    filePath: string,
    language: string,
    parentName?: string
  ): IndexedItem[] {
    const items: IndexedItem[] = [];
    const range = symbol.range;
    const content = document.getText(range);
    const fullName = parentName ? `${parentName}.${symbol.name}` : symbol.name;

    // 创建符号索引项
    const item: IndexedItem = {
      id: this.generateId(this.mapSymbolKind(symbol.kind), filePath, symbol.name),
      type: this.mapSymbolKind(symbol.kind),
      filePath,
      content,
      metadata: {
        language,
        lineStart: range.start.line + 1,
        lineEnd: range.end.line + 1,
        name: fullName,
        tags: [language, symbol.kind.toString(), this.mapSymbolKind(symbol.kind)]
      },
      lastUpdated: new Date(),
      hash: this.calculateHash(content)
    };

    // 添加额外的元数据
    if (symbol.kind === vscode.SymbolKind.Function || symbol.kind === vscode.SymbolKind.Method) {
      item.metadata.signature = this.extractFunctionSignature(content, language);
      item.metadata.parameters = this.extractParameters(content, language);
      item.metadata.returnType = this.extractReturnType(content, language);
      item.metadata.complexity = this.calculateComplexity(content);
    }

    items.push(item);

    // 递归处理子符号
    if (symbol.children) {
      for (const child of symbol.children) {
        const childItems = this.parseSymbol(child, document, filePath, language, fullName);
        items.push(...childItems);
      }
    }

    return items;
  }

  /**
   * 工具方法
   */
  private generateId(type: string, filePath: string, name?: string): string {
    const base = `${type}_${path.basename(filePath)}_${name || 'root'}`;
    return base.replace(/[^a-zA-Z0-9_]/g, '_') + '_' + Date.now();
  }

  private calculateHash(content: string): string {
    // 简单的哈希函数
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString();
  }

  private mapSymbolKind(kind: vscode.SymbolKind): IndexedItem['type'] {
    switch (kind) {
      case vscode.SymbolKind.Function:
      case vscode.SymbolKind.Method:
        return 'function';
      case vscode.SymbolKind.Class:
        return 'class';
      case vscode.SymbolKind.Interface:
        return 'interface';
      case vscode.SymbolKind.Variable:
      case vscode.SymbolKind.Property:
        return 'variable';
      default:
        return 'variable';
    }
  }

  private extractFunctionSignature(content: string, language: string): string {
    // 简化的函数签名提取
    const lines = content.split('\n');
    return lines[0]?.trim() || '';
  }

  private extractParameters(content: string, language: string): string[] {
    // 简化的参数提取
    const match = content.match(/\(([^)]*)\)/);
    if (match && match[1]) {
      return match[1].split(',').map(p => p.trim()).filter(p => p);
    }
    return [];
  }

  private extractReturnType(content: string, language: string): string | undefined {
    // 简化的返回类型提取
    if (language === 'typescript') {
      const match = content.match(/:\s*([^{]+)\s*{/);
      return match?.[1]?.trim();
    }
    return undefined;
  }

  private calculateComplexity(content: string): number {
    // 简单的圈复杂度计算
    const complexityKeywords = ['if', 'else', 'while', 'for', 'switch', 'case', 'catch', '&&', '||'];
    let complexity = 1;
    
    for (const keyword of complexityKeywords) {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g');
      const matches = content.match(regex);
      if (matches) {
        complexity += matches.length;
      }
    }
    
    return complexity;
  }

  private updateIndexStats(): void {
    this.indexStats.totalItems = this.indexedItems.size;
    this.indexStats.fileCount = 0;
    this.indexStats.functionCount = 0;
    this.indexStats.classCount = 0;
    this.indexStats.lastIndexTime = new Date();
    
    for (const item of this.indexedItems.values()) {
      switch (item.type) {
        case 'file':
          this.indexStats.fileCount++;
          break;
        case 'function':
          this.indexStats.functionCount++;
          break;
        case 'class':
          this.indexStats.classCount++;
          break;
      }
    }
    
    // 计算索引大小（估算）
    this.indexStats.indexSize = this.indexedItems.size * 1024; // 假设每项1KB
  }

  private async loadExistingIndex(): Promise<void> {
    // TODO: 从持久化存储加载索引
    console.log('Loading existing index...');
  }

  private async startIncrementalIndexing(): Promise<void> {
    // TODO: 启动增量索引监听
    console.log('Starting incremental indexing...');
  }

  /**
   * 清理资源
   */
  dispose(): void {
    this.indexedItems.clear();
    this.fileHashes.clear();
    this.indexingQueue = [];
  }
}
