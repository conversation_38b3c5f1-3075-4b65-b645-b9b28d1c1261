/**
 * Tool Integration Interfaces
 * 
 * 定义工具集成模块的接口和类型
 */

import { ToolCall, ToolResult } from '@/types';

// 工具执行上下文
export interface ToolExecutionContext {
  workspaceRoot?: string;
  activeFile?: string;
  selectedText?: string;
  cursorPosition?: {
    line: number;
    character: number;
  };
  projectInfo?: {
    name: string;
    type: string;
    dependencies: Record<string, string>;
  };
  userPreferences?: Record<string, any>;
}

// 工具执行结果
export interface ToolExecutionResult {
  success: boolean;
  result?: any;
  error?: string;
  metadata?: {
    duration: number;
    filesModified?: string[];
    commandsExecuted?: string[];
    warnings?: string[];
  };
}

// 工具定义
export interface ToolDefinition {
  name: string;
  description: string;
  category: ToolCategory;
  parameters: {
    type: 'object';
    properties: Record<string, ParameterDefinition>;
    required?: string[];
  };
  examples?: ToolExample[];
  permissions?: ToolPermission[];
  deprecated?: boolean;
  version?: string;
}

// 工具类别
export type ToolCategory = 
  | 'file-system'
  | 'code-analysis'
  | 'git'
  | 'terminal'
  | 'search'
  | 'refactor'
  | 'test'
  | 'debug'
  | 'documentation'
  | 'utility';

// 参数定义
export interface ParameterDefinition {
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  description: string;
  enum?: any[];
  default?: any;
  pattern?: string;
  minimum?: number;
  maximum?: number;
  items?: ParameterDefinition;
  properties?: Record<string, ParameterDefinition>;
}

// 工具示例
export interface ToolExample {
  description: string;
  parameters: Record<string, any>;
  expectedResult?: any;
}

// 工具权限
export type ToolPermission = 
  | 'read-files'
  | 'write-files'
  | 'execute-commands'
  | 'network-access'
  | 'git-operations'
  | 'workspace-modification';

// 工具接口
export interface ITool {
  readonly definition: ToolDefinition;
  
  execute(
    parameters: Record<string, any>,
    context: ToolExecutionContext
  ): Promise<ToolExecutionResult>;
  
  validate(parameters: Record<string, any>): ValidationResult;
  
  getUsageStats(): ToolUsageStats;
}

// 验证结果
export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

// 工具使用统计
export interface ToolUsageStats {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  lastUsed?: number;
}

// 工具管理器接口
export interface IToolManager {
  // 工具注册和管理
  registerTool(tool: ITool): void;
  unregisterTool(name: string): void;
  getTool(name: string): ITool | undefined;
  listTools(category?: ToolCategory): ToolDefinition[];
  
  // 工具执行
  executeTool(
    name: string,
    parameters: Record<string, any>,
    context: ToolExecutionContext
  ): Promise<ToolExecutionResult>;
  
  // 批量执行
  executeToolChain(
    calls: Array<{
      name: string;
      parameters: Record<string, any>;
    }>,
    context: ToolExecutionContext
  ): Promise<ToolExecutionResult[]>;
  
  // 工具发现和搜索
  searchTools(query: string): ToolDefinition[];
  suggestTools(context: ToolExecutionContext): ToolDefinition[];
  
  // 统计和监控
  getUsageStats(): Record<string, ToolUsageStats>;
  getToolHealth(): Record<string, ToolHealthStatus>;
}

// 工具健康状态
export interface ToolHealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  lastCheck: number;
  issues?: string[];
  dependencies?: {
    name: string;
    status: 'available' | 'missing' | 'outdated';
    version?: string;
  }[];
}

// 文件系统工具相关
export interface FileSystemOperation {
  type: 'read' | 'write' | 'create' | 'delete' | 'move' | 'copy' | 'list';
  path: string;
  content?: string;
  destination?: string;
  options?: {
    encoding?: string;
    recursive?: boolean;
    overwrite?: boolean;
  };
}

export interface FileInfo {
  path: string;
  name: string;
  size: number;
  type: 'file' | 'directory';
  extension?: string;
  lastModified: number;
  permissions?: {
    readable: boolean;
    writable: boolean;
    executable: boolean;
  };
}

// 代码分析工具相关
export interface CodeAnalysisOptions {
  includeComments?: boolean;
  includeTests?: boolean;
  maxDepth?: number;
  languages?: string[];
  excludePatterns?: string[];
}

export interface CodeMetrics {
  linesOfCode: number;
  complexity: number;
  maintainabilityIndex: number;
  testCoverage?: number;
  dependencies: string[];
  issues: CodeIssue[];
}

export interface CodeIssue {
  type: 'error' | 'warning' | 'info' | 'suggestion';
  message: string;
  file: string;
  line: number;
  column: number;
  rule?: string;
  severity: number;
}

// Git工具相关
export interface GitOperation {
  type: 'status' | 'add' | 'commit' | 'push' | 'pull' | 'branch' | 'merge' | 'diff';
  files?: string[];
  message?: string;
  branch?: string;
  remote?: string;
  options?: Record<string, any>;
}

export interface GitStatus {
  branch: string;
  ahead: number;
  behind: number;
  staged: string[];
  unstaged: string[];
  untracked: string[];
  conflicts: string[];
}

// 终端工具相关
export interface TerminalCommand {
  command: string;
  args?: string[];
  cwd?: string;
  env?: Record<string, string>;
  timeout?: number;
  shell?: string;
}

export interface TerminalResult {
  exitCode: number;
  stdout: string;
  stderr: string;
  duration: number;
  command: string;
}

// 搜索工具相关
export interface SearchOptions {
  pattern: string;
  caseSensitive?: boolean;
  wholeWord?: boolean;
  regex?: boolean;
  includeFiles?: string[];
  excludeFiles?: string[];
  maxResults?: number;
}

export interface SearchResult {
  file: string;
  line: number;
  column: number;
  match: string;
  context: {
    before: string[];
    after: string[];
  };
}

// 重构工具相关
export interface RefactorOperation {
  type: 'rename' | 'extract' | 'inline' | 'move' | 'organize-imports';
  target: string;
  newName?: string;
  scope?: 'file' | 'project' | 'workspace';
  options?: Record<string, any>;
}

export interface RefactorResult {
  changes: FileChange[];
  conflicts: RefactorConflict[];
  preview?: string;
}

export interface FileChange {
  file: string;
  type: 'create' | 'modify' | 'delete' | 'rename';
  oldContent?: string;
  newContent?: string;
  newPath?: string;
}

export interface RefactorConflict {
  file: string;
  line: number;
  message: string;
  severity: 'error' | 'warning';
}

// 测试工具相关
export interface TestOptions {
  pattern?: string;
  coverage?: boolean;
  watch?: boolean;
  verbose?: boolean;
  timeout?: number;
}

export interface TestResult {
  passed: number;
  failed: number;
  skipped: number;
  total: number;
  duration: number;
  coverage?: TestCoverage;
  failures: TestFailure[];
}

export interface TestCoverage {
  lines: number;
  functions: number;
  branches: number;
  statements: number;
}

export interface TestFailure {
  test: string;
  file: string;
  error: string;
  stack?: string;
}

// 工具错误类型
export class ToolError extends Error {
  constructor(
    message: string,
    public toolName: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ToolError';
  }
}

export class ToolValidationError extends ToolError {
  constructor(toolName: string, errors: string[], details?: any) {
    super(`Validation failed for tool ${toolName}: ${errors.join(', ')}`, toolName, 'VALIDATION_ERROR', details);
    this.name = 'ToolValidationError';
  }
}

export class ToolExecutionError extends ToolError {
  constructor(toolName: string, message: string, details?: any) {
    super(`Execution failed for tool ${toolName}: ${message}`, toolName, 'EXECUTION_ERROR', details);
    this.name = 'ToolExecutionError';
  }
}

export class ToolPermissionError extends ToolError {
  constructor(toolName: string, permission: ToolPermission, details?: any) {
    super(`Permission denied for tool ${toolName}: ${permission}`, toolName, 'PERMISSION_ERROR', details);
    this.name = 'ToolPermissionError';
  }
}
