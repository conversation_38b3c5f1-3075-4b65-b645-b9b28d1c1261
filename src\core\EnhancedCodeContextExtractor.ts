/**
 * Enhanced Code Context Extractor - 增强代码上下文提取器
 * 
 * 企业级代码上下文获取系统，支持智能范围扩展、优先级算法、增量更新
 */

import * as vscode from 'vscode';
import * as path from 'path';
import { EventBus } from './EventBus';
import { CodeContext } from './CodeContextExtractor';

export interface EnhancedCodeContext extends CodeContext {
  priority: number;
  relevanceScore: number;
  dependencies: DependencyInfo[];
  callGraph: CallGraphNode[];
  gitContext?: GitContextInfo;
  errorContext?: ErrorContextInfo;
}

export interface DependencyInfo {
  type: 'import' | 'export' | 'require' | 'include';
  source: string;
  target: string;
  filePath: string;
  lineNumber: number;
  isExternal: boolean;
}

export interface CallGraphNode {
  name: string;
  type: 'function' | 'method' | 'class' | 'variable';
  filePath: string;
  lineNumber: number;
  callers: string[];
  callees: string[];
  complexity: number;
}

export interface GitContextInfo {
  lastModified: Date;
  author: string;
  commitMessage: string;
  changedLines: number[];
  relatedCommits: string[];
}

export interface ErrorContextInfo {
  errorType: string;
  stackTrace: string[];
  relatedFiles: string[];
  suggestedFixes: string[];
}

export interface ContextExtractionOptions {
  includeGitHistory?: boolean;
  includeDependencies?: boolean;
  includeCallGraph?: boolean;
  maxDepth?: number;
  maxFiles?: number;
  priorityThreshold?: number;
}

export class EnhancedCodeContextExtractor {
  private eventBus: EventBus;
  private contextCache: Map<string, EnhancedCodeContext> = new Map();
  private dependencyGraph: Map<string, DependencyInfo[]> = new Map();
  private callGraph: Map<string, CallGraphNode[]> = new Map();
  private fileWatchers: Map<string, vscode.FileSystemWatcher> = new Map();

  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
    this.setupFileWatchers();
  }

  /**
   * 获取增强的代码上下文
   */
  async getEnhancedContext(
    targetFile?: string,
    targetRange?: vscode.Range,
    options: ContextExtractionOptions = {}
  ): Promise<EnhancedCodeContext[]> {
    const startTime = Date.now();
    
    try {
      await this.eventBus.emit({
        type: 'context.enhanced_extraction_started',
        source: 'EnhancedCodeContextExtractor',
        targetFile,
        options
      });

      // 1. 获取基础上下文
      const baseContexts = await this.getBaseContexts(targetFile, targetRange);
      
      // 2. 扩展上下文范围
      const expandedContexts = await this.expandContextRange(baseContexts, options);
      
      // 3. 计算优先级和相关性
      const prioritizedContexts = await this.calculatePriorities(expandedContexts, targetFile, targetRange);
      
      // 4. 应用过滤和排序
      const filteredContexts = this.filterAndSort(prioritizedContexts, options);
      
      // 5. 缓存结果
      this.cacheContexts(filteredContexts);

      const duration = Date.now() - startTime;
      
      await this.eventBus.emit({
        type: 'context.enhanced_extraction_completed',
        source: 'EnhancedCodeContextExtractor',
        contextCount: filteredContexts.length,
        duration
      });

      return filteredContexts;

    } catch (error) {
      await this.eventBus.emit({
        type: 'context.enhanced_extraction_failed',
        source: 'EnhancedCodeContextExtractor',
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * 获取函数/类的完整定义和调用关系
   */
  async getFunctionCallGraph(symbolName: string, filePath: string): Promise<CallGraphNode[]> {
    const cacheKey = `${filePath}:${symbolName}`;
    
    if (this.callGraph.has(cacheKey)) {
      return this.callGraph.get(cacheKey)!;
    }

    const callGraph: CallGraphNode[] = [];
    
    try {
      // 使用VS Code的语言服务获取符号信息
      const document = await vscode.workspace.openTextDocument(filePath);
      const symbols = await vscode.commands.executeCommand<vscode.DocumentSymbol[]>(
        'vscode.executeDocumentSymbolProvider',
        document.uri
      );

      if (symbols) {
        for (const symbol of symbols) {
          const node = await this.analyzeSymbol(symbol, document);
          if (node && (node.name === symbolName || node.name.includes(symbolName))) {
            callGraph.push(node);
          }
        }
      }

      // 查找调用关系
      await this.findCallRelationships(callGraph, filePath);
      
      this.callGraph.set(cacheKey, callGraph);
      return callGraph;

    } catch (error) {
      console.error('Failed to get function call graph:', error);
      return [];
    }
  }

  /**
   * 跨文件依赖链追踪
   */
  async traceDependencyChain(filePath: string, maxDepth: number = 3): Promise<DependencyInfo[]> {
    const visited = new Set<string>();
    const dependencies: DependencyInfo[] = [];
    
    await this.traceDependenciesRecursive(filePath, dependencies, visited, 0, maxDepth);
    
    return dependencies;
  }

  /**
   * 获取Git历史上下文
   */
  async getGitContext(filePath: string): Promise<GitContextInfo | null> {
    try {
      // 检查是否在Git仓库中
      const gitExtension = vscode.extensions.getExtension('vscode.git');
      if (!gitExtension) {
        return null;
      }

      const git = gitExtension.exports.getAPI(1);
      const repository = git.repositories.find((repo: any) => 
        filePath.startsWith(repo.rootUri.fsPath)
      );

      if (!repository) {
        return null;
      }

      // 获取文件的最近提交信息
      const relativePath = path.relative(repository.rootUri.fsPath, filePath);
      const log = await repository.log({ path: relativePath, maxEntries: 5 });

      if (log.length === 0) {
        return null;
      }

      const lastCommit = log[0];
      
      return {
        lastModified: lastCommit.commitDate || new Date(),
        author: lastCommit.authorName || 'Unknown',
        commitMessage: lastCommit.message || '',
        changedLines: [], // TODO: 实现具体的行变更检测
        relatedCommits: log.slice(1, 3).map((commit: any) => commit.hash)
      };

    } catch (error) {
      console.error('Failed to get git context:', error);
      return null;
    }
  }

  /**
   * 获取错误堆栈和调试上下文
   */
  async getErrorContext(): Promise<ErrorContextInfo | null> {
    try {
      // 获取当前的诊断信息（错误、警告）
      const activeEditor = vscode.window.activeTextEditor;
      if (!activeEditor) {
        return null;
      }

      const diagnostics = vscode.languages.getDiagnostics(activeEditor.document.uri);
      if (diagnostics.length === 0) {
        return null;
      }

      const errors = diagnostics.filter(d => d.severity === vscode.DiagnosticSeverity.Error);
      if (errors.length === 0) {
        return null;
      }

      const primaryError = errors[0];
      
      return {
        errorType: primaryError.code?.toString() || 'Unknown',
        stackTrace: [primaryError.message],
        relatedFiles: [activeEditor.document.uri.fsPath],
        suggestedFixes: this.generateErrorFixes(primaryError)
      };

    } catch (error) {
      console.error('Failed to get error context:', error);
      return null;
    }
  }

  /**
   * 实时监听代码变化，动态更新上下文
   */
  private setupFileWatchers(): void {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders) return;

    for (const folder of workspaceFolders) {
      const pattern = new vscode.RelativePattern(folder, '**/*.{ts,js,tsx,jsx,py,java,cs,go,rs}');
      const watcher = vscode.workspace.createFileSystemWatcher(pattern);

      watcher.onDidChange(uri => this.handleFileChange(uri));
      watcher.onDidCreate(uri => this.handleFileCreate(uri));
      watcher.onDidDelete(uri => this.handleFileDelete(uri));

      this.fileWatchers.set(folder.uri.fsPath, watcher);
    }
  }

  /**
   * 处理文件变化
   */
  private async handleFileChange(uri: vscode.Uri): Promise<void> {
    const filePath = uri.fsPath;
    
    // 清除相关缓存
    this.invalidateCache(filePath);
    
    // 重新分析依赖关系
    await this.updateDependencies(filePath);
    
    await this.eventBus.emit({
      type: 'context.file_changed',
      source: 'EnhancedCodeContextExtractor',
      filePath
    });
  }

  /**
   * 获取基础上下文
   */
  private async getBaseContexts(
    targetFile?: string,
    targetRange?: vscode.Range
  ): Promise<EnhancedCodeContext[]> {
    const contexts: EnhancedCodeContext[] = [];
    
    // 获取当前选中的代码或文件
    const activeEditor = vscode.window.activeTextEditor;
    if (activeEditor) {
      const document = activeEditor.document;
      const filePath = document.uri.fsPath;
      
      let content: string;
      let lineRange: { start: number; end: number } | undefined;
      
      if (targetRange) {
        content = document.getText(targetRange);
        lineRange = {
          start: targetRange.start.line + 1,
          end: targetRange.end.line + 1
        };
      } else if (!activeEditor.selection.isEmpty) {
        content = document.getText(activeEditor.selection);
        lineRange = {
          start: activeEditor.selection.start.line + 1,
          end: activeEditor.selection.end.line + 1
        };
      } else {
        content = document.getText();
      }

      const baseContext: EnhancedCodeContext = {
        type: targetRange || !activeEditor.selection.isEmpty ? 'selection' : 'file',
        content,
        filePath,
        language: document.languageId,
        lineRange,
        priority: 1.0,
        relevanceScore: 1.0,
        dependencies: [],
        callGraph: [],
        metadata: await this.analyzeCodeMetadata(content, document.languageId)
      };

      contexts.push(baseContext);
    }

    return contexts;
  }

  /**
   * 扩展上下文范围
   */
  private async expandContextRange(
    baseContexts: EnhancedCodeContext[],
    options: ContextExtractionOptions
  ): Promise<EnhancedCodeContext[]> {
    const expandedContexts = [...baseContexts];
    
    for (const context of baseContexts) {
      // 添加依赖关系
      if (options.includeDependencies) {
        const dependencies = await this.traceDependencyChain(
          context.filePath,
          options.maxDepth || 2
        );
        context.dependencies = dependencies;
        
        // 添加依赖文件的上下文
        for (const dep of dependencies.slice(0, options.maxFiles || 5)) {
          if (dep.target !== context.filePath) {
            const depContext = await this.createDependencyContext(dep);
            if (depContext) {
              expandedContexts.push(depContext);
            }
          }
        }
      }

      // 添加调用图
      if (options.includeCallGraph && context.metadata?.functions) {
        for (const funcName of context.metadata.functions.slice(0, 3)) {
          const callGraph = await this.getFunctionCallGraph(funcName, context.filePath);
          context.callGraph.push(...callGraph);
        }
      }

      // 添加Git上下文
      if (options.includeGitHistory) {
        const gitContext = await this.getGitContext(context.filePath);
        context.gitContext = gitContext || undefined;
      }
    }

    return expandedContexts;
  }

  /**
   * 计算优先级和相关性
   */
  private async calculatePriorities(
    contexts: EnhancedCodeContext[],
    targetFile?: string,
    targetRange?: vscode.Range
  ): Promise<EnhancedCodeContext[]> {
    for (const context of contexts) {
      // 基础优先级
      let priority = 0.5;
      let relevanceScore = 0.5;

      // 文件类型权重
      if (context.type === 'selection') priority += 0.4;
      else if (context.type === 'file') priority += 0.3;
      else if (context.type === 'related') priority += 0.2;

      // 文件距离权重
      if (targetFile) {
        const distance = this.calculateFileDistance(context.filePath, targetFile);
        relevanceScore += Math.max(0, 1 - distance / 5);
      }

      // 最近修改权重
      if (context.gitContext) {
        const daysSinceModified = (Date.now() - context.gitContext.lastModified.getTime()) / (1000 * 60 * 60 * 24);
        relevanceScore += Math.max(0, 1 - daysSinceModified / 30);
      }

      // 依赖关系权重
      relevanceScore += context.dependencies.length * 0.1;

      // 调用图复杂度权重
      const avgComplexity = context.callGraph.reduce((sum, node) => sum + node.complexity, 0) / 
                           (context.callGraph.length || 1);
      relevanceScore += Math.min(avgComplexity / 10, 0.3);

      context.priority = Math.min(priority, 1.0);
      context.relevanceScore = Math.min(relevanceScore, 1.0);
    }

    return contexts;
  }

  /**
   * 过滤和排序上下文
   */
  private filterAndSort(
    contexts: EnhancedCodeContext[],
    options: ContextExtractionOptions
  ): EnhancedCodeContext[] {
    // 过滤低优先级的上下文
    const threshold = options.priorityThreshold || 0.3;
    const filtered = contexts.filter(ctx => ctx.priority >= threshold);

    // 按优先级和相关性排序
    filtered.sort((a, b) => {
      const scoreA = a.priority * 0.6 + a.relevanceScore * 0.4;
      const scoreB = b.priority * 0.6 + b.relevanceScore * 0.4;
      return scoreB - scoreA;
    });

    // 限制数量
    const maxFiles = options.maxFiles || 10;
    return filtered.slice(0, maxFiles);
  }

  /**
   * 缓存上下文结果
   */
  private cacheContexts(contexts: EnhancedCodeContext[]): void {
    for (const context of contexts) {
      const cacheKey = `${context.filePath}:${context.type}`;
      this.contextCache.set(cacheKey, context);
    }
  }

  /**
   * 工具方法
   */
  private async analyzeCodeMetadata(content: string, language: string): Promise<any> {
    // 重用之前的代码分析逻辑
    return {
      functions: this.extractFunctions(content, language),
      classes: this.extractClasses(content, language),
      imports: this.extractImports(content, language),
      lineCount: content.split('\n').length
    };
  }

  private extractFunctions(content: string, language: string): string[] {
    const functions: string[] = [];
    
    if (language === 'typescript' || language === 'javascript') {
      const functionRegex = /(?:function\s+(\w+)|(\w+)\s*[:=]\s*(?:async\s+)?function|(?:async\s+)?(\w+)\s*\([^)]*\)\s*(?::\s*[^{]+)?\s*\{)/g;
      let match;
      while ((match = functionRegex.exec(content)) !== null) {
        const functionName = match[1] || match[2] || match[3];
        if (functionName && !functions.includes(functionName)) {
          functions.push(functionName);
        }
      }
    }
    
    return functions;
  }

  private extractClasses(content: string, language: string): string[] {
    const classes: string[] = [];
    
    if (language === 'typescript' || language === 'javascript') {
      const classRegex = /class\s+(\w+)/g;
      let match;
      while ((match = classRegex.exec(content)) !== null) {
        classes.push(match[1]);
      }
    }
    
    return classes;
  }

  private extractImports(content: string, language: string): string[] {
    const imports: string[] = [];
    
    if (language === 'typescript' || language === 'javascript') {
      const importRegex = /import\s+(?:{[^}]+}|\w+|\*\s+as\s+\w+)\s+from\s+['"]([^'"]+)['"]/g;
      let match;
      while ((match = importRegex.exec(content)) !== null) {
        imports.push(match[1]);
      }
    }
    
    return imports;
  }

  private calculateFileDistance(file1: string, file2: string): number {
    const path1 = file1.split(path.sep);
    const path2 = file2.split(path.sep);
    
    let commonLength = 0;
    for (let i = 0; i < Math.min(path1.length, path2.length); i++) {
      if (path1[i] === path2[i]) {
        commonLength++;
      } else {
        break;
      }
    }
    
    return (path1.length - commonLength) + (path2.length - commonLength);
  }

  private async analyzeSymbol(symbol: vscode.DocumentSymbol, document: vscode.TextDocument): Promise<CallGraphNode | null> {
    try {
      const range = symbol.range;
      const content = document.getText(range);
      
      return {
        name: symbol.name,
        type: this.mapSymbolKind(symbol.kind),
        filePath: document.uri.fsPath,
        lineNumber: range.start.line + 1,
        callers: [],
        callees: this.findCallees(content),
        complexity: this.calculateComplexity(content)
      };
    } catch (error) {
      return null;
    }
  }

  private mapSymbolKind(kind: vscode.SymbolKind): 'function' | 'method' | 'class' | 'variable' {
    switch (kind) {
      case vscode.SymbolKind.Function:
        return 'function';
      case vscode.SymbolKind.Method:
        return 'method';
      case vscode.SymbolKind.Class:
        return 'class';
      default:
        return 'variable';
    }
  }

  private findCallees(content: string): string[] {
    const callees: string[] = [];
    const callRegex = /(\w+)\s*\(/g;
    let match;
    
    while ((match = callRegex.exec(content)) !== null) {
      const callee = match[1];
      if (!callees.includes(callee)) {
        callees.push(callee);
      }
    }
    
    return callees;
  }

  private calculateComplexity(content: string): number {
    // 简单的圈复杂度计算
    const complexityKeywords = ['if', 'else', 'while', 'for', 'switch', 'case', 'catch', '&&', '||'];
    let complexity = 1; // 基础复杂度
    
    for (const keyword of complexityKeywords) {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g');
      const matches = content.match(regex);
      if (matches) {
        complexity += matches.length;
      }
    }
    
    return complexity;
  }

  private async findCallRelationships(callGraph: CallGraphNode[], filePath: string): Promise<void> {
    // TODO: 实现更复杂的调用关系分析
    // 这里可以使用语言服务器协议来获取更准确的调用关系
  }

  private async traceDependenciesRecursive(
    filePath: string,
    dependencies: DependencyInfo[],
    visited: Set<string>,
    depth: number,
    maxDepth: number
  ): Promise<void> {
    if (depth >= maxDepth || visited.has(filePath)) {
      return;
    }

    visited.add(filePath);

    try {
      const document = await vscode.workspace.openTextDocument(filePath);
      const content = document.getText();
      const imports = this.extractImports(content, document.languageId);

      for (const importPath of imports) {
        const resolvedPath = await this.resolveImportPath(importPath, filePath);
        if (resolvedPath) {
          const dependency: DependencyInfo = {
            type: 'import',
            source: filePath,
            target: resolvedPath,
            filePath: filePath,
            lineNumber: this.findImportLine(content, importPath),
            isExternal: this.isExternalDependency(importPath)
          };

          dependencies.push(dependency);

          if (!dependency.isExternal) {
            await this.traceDependenciesRecursive(
              resolvedPath,
              dependencies,
              visited,
              depth + 1,
              maxDepth
            );
          }
        }
      }
    } catch (error) {
      console.error(`Failed to trace dependencies for ${filePath}:`, error);
    }
  }

  private async resolveImportPath(importPath: string, fromFile: string): Promise<string | null> {
    // 简化的路径解析逻辑
    if (importPath.startsWith('.')) {
      const dir = path.dirname(fromFile);
      return path.resolve(dir, importPath);
    }
    return null; // 外部依赖
  }

  private findImportLine(content: string, importPath: string): number {
    const lines = content.split('\n');
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes(importPath)) {
        return i + 1;
      }
    }
    return 1;
  }

  private isExternalDependency(importPath: string): boolean {
    return !importPath.startsWith('.') && !importPath.startsWith('/');
  }

  private async createDependencyContext(dep: DependencyInfo): Promise<EnhancedCodeContext | null> {
    try {
      const document = await vscode.workspace.openTextDocument(dep.target);
      const content = document.getText();

      return {
        type: 'related',
        content: content.length > 5000 ? content.substring(0, 5000) + '\n// ... 文件内容已截断' : content,
        filePath: dep.target,
        language: document.languageId,
        priority: 0.6,
        relevanceScore: 0.7,
        dependencies: [],
        callGraph: [],
        metadata: await this.analyzeCodeMetadata(content, document.languageId)
      };
    } catch (error) {
      return null;
    }
  }

  private generateErrorFixes(diagnostic: vscode.Diagnostic): string[] {
    const fixes: string[] = [];
    
    // 基于错误类型生成建议修复
    const message = diagnostic.message.toLowerCase();
    
    if (message.includes('undefined')) {
      fixes.push('检查变量是否已声明');
      fixes.push('添加空值检查');
    }
    
    if (message.includes('type')) {
      fixes.push('检查类型定义');
      fixes.push('添加类型注解');
    }
    
    return fixes;
  }

  private invalidateCache(filePath: string): void {
    const keysToDelete: string[] = [];
    
    for (const [key, context] of Array.from(this.contextCache.entries())) {
      if (context.filePath === filePath || 
          context.dependencies.some(dep => dep.target === filePath)) {
        keysToDelete.push(key);
      }
    }
    
    keysToDelete.forEach(key => this.contextCache.delete(key));
  }

  private async updateDependencies(filePath: string): Promise<void> {
    // 重新分析文件的依赖关系
    const dependencies = await this.traceDependencyChain(filePath, 2);
    this.dependencyGraph.set(filePath, dependencies);
  }

  private handleFileCreate(uri: vscode.Uri): void {
    // 处理新文件创建
    this.eventBus.emit({
      type: 'context.file_created',
      source: 'EnhancedCodeContextExtractor',
      filePath: uri.fsPath
    });
  }

  private handleFileDelete(uri: vscode.Uri): void {
    // 处理文件删除
    this.invalidateCache(uri.fsPath);
    this.dependencyGraph.delete(uri.fsPath);
    
    this.eventBus.emit({
      type: 'context.file_deleted',
      source: 'EnhancedCodeContextExtractor',
      filePath: uri.fsPath
    });
  }

  /**
   * 清理资源
   */
  dispose(): void {
    for (const watcher of Array.from(this.fileWatchers.values())) {
      watcher.dispose();
    }
    this.fileWatchers.clear();
    this.contextCache.clear();
    this.dependencyGraph.clear();
    this.callGraph.clear();
  }
}
