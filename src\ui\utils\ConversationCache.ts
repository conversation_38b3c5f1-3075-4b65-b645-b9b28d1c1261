/**
 * 对话历史缓存管理器
 * 
 * 智能管理对话历史，优化上下文窗口和查询性能
 */

import { CacheManager } from './CacheManager';

export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: number;
  metadata?: {
    tokens?: number;
    model?: string;
    temperature?: number;
    [key: string]: any;
  };
}

export interface Conversation {
  id: string;
  title: string;
  messages: Message[];
  createdAt: number;
  updatedAt: number;
  metadata?: {
    totalTokens?: number;
    model?: string;
    [key: string]: any;
  };
}

export interface ContextWindow {
  maxTokens: number;
  currentTokens: number;
  messages: Message[];
  summary?: string;
}

export interface ConversationCacheConfig {
  maxConversations: number;
  maxMessagesPerConversation: number;
  maxTokensPerContext: number;
  compressionThreshold: number;
  summaryEnabled: boolean;
  autoCleanup: boolean;
}

export class ConversationCache {
  private cacheManager: CacheManager;
  private config: ConversationCacheConfig;
  private conversations: Map<string, Conversation> = new Map();
  private contextWindows: Map<string, ContextWindow> = new Map();

  constructor(config: Partial<ConversationCacheConfig> = {}) {
    this.config = {
      maxConversations: 100,
      maxMessagesPerConversation: 1000,
      maxTokensPerContext: 4000,
      compressionThreshold: 50,
      summaryEnabled: true,
      autoCleanup: true,
      ...config,
    };

    this.cacheManager = new CacheManager({
      maxSize: 100 * 1024 * 1024, // 100MB
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7天
      compressionEnabled: true,
      persistToDisk: true,
    });

    this.loadConversations();
  }

  /**
   * 创建新对话
   */
  public async createConversation(title: string, initialMessage?: Message): Promise<Conversation> {
    const conversation: Conversation = {
      id: this.generateId(),
      title,
      messages: initialMessage ? [initialMessage] : [],
      createdAt: Date.now(),
      updatedAt: Date.now(),
      metadata: {
        totalTokens: initialMessage?.metadata?.tokens || 0,
      },
    };

    await this.saveConversation(conversation);
    return conversation;
  }

  /**
   * 获取对话
   */
  public async getConversation(id: string): Promise<Conversation | null> {
    // 先从内存缓存获取
    if (this.conversations.has(id)) {
      return this.conversations.get(id)!;
    }

    // 从持久化缓存获取
    const cached = await this.cacheManager.get<Conversation>(`conversation:${id}`);
    if (cached) {
      this.conversations.set(id, cached);
      return cached;
    }

    return null;
  }

  /**
   * 保存对话
   */
  public async saveConversation(conversation: Conversation): Promise<void> {
    conversation.updatedAt = Date.now();
    
    // 更新内存缓存
    this.conversations.set(conversation.id, conversation);
    
    // 保存到持久化缓存
    await this.cacheManager.set(`conversation:${conversation.id}`, conversation);
    
    // 更新对话列表
    await this.updateConversationList();
    
    // 自动清理
    if (this.config.autoCleanup) {
      await this.cleanup();
    }
  }

  /**
   * 添加消息到对话
   */
  public async addMessage(conversationId: string, message: Message): Promise<void> {
    const conversation = await this.getConversation(conversationId);
    if (!conversation) {
      throw new Error(`Conversation ${conversationId} not found`);
    }

    // 添加消息
    conversation.messages.push(message);
    
    // 更新元数据
    if (message.metadata?.tokens) {
      conversation.metadata = conversation.metadata || {};
      conversation.metadata.totalTokens = (conversation.metadata.totalTokens || 0) + message.metadata.tokens;
    }

    // 检查消息数量限制
    if (conversation.messages.length > this.config.maxMessagesPerConversation) {
      await this.compressConversation(conversation);
    }

    await this.saveConversation(conversation);
    
    // 更新上下文窗口
    await this.updateContextWindow(conversationId);
  }

  /**
   * 获取上下文窗口
   */
  public async getContextWindow(conversationId: string): Promise<ContextWindow | null> {
    // 先从缓存获取
    if (this.contextWindows.has(conversationId)) {
      return this.contextWindows.get(conversationId)!;
    }

    // 重新构建上下文窗口
    const conversation = await this.getConversation(conversationId);
    if (!conversation) {
      return null;
    }

    return await this.buildContextWindow(conversation);
  }

  /**
   * 构建上下文窗口
   */
  private async buildContextWindow(conversation: Conversation): Promise<ContextWindow> {
    const messages = [...conversation.messages];
    let currentTokens = 0;
    const contextMessages: Message[] = [];

    // 从最新消息开始，向前添加直到达到token限制
    for (let i = messages.length - 1; i >= 0; i--) {
      const message = messages[i];
      const messageTokens = this.estimateTokens(message.content);
      
      if (currentTokens + messageTokens > this.config.maxTokensPerContext) {
        break;
      }
      
      contextMessages.unshift(message);
      currentTokens += messageTokens;
    }

    const contextWindow: ContextWindow = {
      maxTokens: this.config.maxTokensPerContext,
      currentTokens,
      messages: contextMessages,
    };

    // 如果有被截断的消息且启用了摘要，生成摘要
    if (contextMessages.length < messages.length && this.config.summaryEnabled) {
      const truncatedMessages = messages.slice(0, messages.length - contextMessages.length);
      contextWindow.summary = await this.generateSummary(truncatedMessages);
    }

    // 缓存上下文窗口
    this.contextWindows.set(conversation.id, contextWindow);
    
    return contextWindow;
  }

  /**
   * 更新上下文窗口
   */
  private async updateContextWindow(conversationId: string): Promise<void> {
    const conversation = await this.getConversation(conversationId);
    if (conversation) {
      await this.buildContextWindow(conversation);
    }
  }

  /**
   * 压缩对话（移除旧消息，保留摘要）
   */
  private async compressConversation(conversation: Conversation): Promise<void> {
    const messagesToKeep = Math.floor(this.config.maxMessagesPerConversation * 0.7);
    const messagesToCompress = conversation.messages.slice(0, -messagesToKeep);
    
    if (messagesToCompress.length > this.config.compressionThreshold) {
      // 生成摘要
      const summary = await this.generateSummary(messagesToCompress);
      
      // 创建摘要消息
      const summaryMessage: Message = {
        id: this.generateId(),
        role: 'system',
        content: `[对话摘要] ${summary}`,
        timestamp: messagesToCompress[0].timestamp,
        metadata: {
          type: 'summary',
          originalMessageCount: messagesToCompress.length,
        },
      };
      
      // 替换旧消息
      conversation.messages = [summaryMessage, ...conversation.messages.slice(-messagesToKeep)];
    }
  }

  /**
   * 生成对话摘要
   */
  private async generateSummary(messages: Message[]): Promise<string> {
    // 简单的摘要生成逻辑
    // 在实际应用中，这里可以调用AI模型生成更智能的摘要
    
    const userMessages = messages.filter(m => m.role === 'user');
    const assistantMessages = messages.filter(m => m.role === 'assistant');
    
    const topics = this.extractTopics(messages);
    const timeRange = this.getTimeRange(messages);
    
    return `对话摘要 (${timeRange}): 包含${userMessages.length}条用户消息和${assistantMessages.length}条助手回复。主要话题：${topics.join('、')}。`;
  }

  /**
   * 提取话题
   */
  private extractTopics(messages: Message[]): string[] {
    // 简单的关键词提取
    const text = messages.map(m => m.content).join(' ');
    const words = text.toLowerCase().match(/\b\w{3,}\b/g) || [];
    
    // 统计词频
    const wordCount = new Map<string, number>();
    words.forEach(word => {
      wordCount.set(word, (wordCount.get(word) || 0) + 1);
    });
    
    // 返回最频繁的词汇
    return Array.from(wordCount.entries())
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3)
      .map(([word]) => word);
  }

  /**
   * 获取时间范围
   */
  private getTimeRange(messages: Message[]): string {
    if (messages.length === 0) return '';
    
    const start = new Date(messages[0].timestamp);
    const end = new Date(messages[messages.length - 1].timestamp);
    
    return `${start.toLocaleDateString()} - ${end.toLocaleDateString()}`;
  }

  /**
   * 估算token数量
   */
  private estimateTokens(text: string): number {
    // 简单的token估算：大约4个字符=1个token
    return Math.ceil(text.length / 4);
  }

  /**
   * 获取对话列表
   */
  public async getConversationList(): Promise<Conversation[]> {
    const list = await this.cacheManager.get<string[]>('conversation:list') || [];
    const conversations: Conversation[] = [];
    
    for (const id of list) {
      const conversation = await this.getConversation(id);
      if (conversation) {
        conversations.push(conversation);
      }
    }
    
    return conversations.sort((a, b) => b.updatedAt - a.updatedAt);
  }

  /**
   * 更新对话列表
   */
  private async updateConversationList(): Promise<void> {
    const ids = Array.from(this.conversations.keys());
    await this.cacheManager.set('conversation:list', ids);
  }

  /**
   * 删除对话
   */
  public async deleteConversation(id: string): Promise<void> {
    this.conversations.delete(id);
    this.contextWindows.delete(id);
    this.cacheManager.delete(`conversation:${id}`);
    await this.updateConversationList();
  }

  /**
   * 搜索对话
   */
  public async searchConversations(query: string): Promise<Conversation[]> {
    const conversations = await this.getConversationList();
    const lowerQuery = query.toLowerCase();
    
    return conversations.filter(conversation => {
      // 搜索标题
      if (conversation.title.toLowerCase().includes(lowerQuery)) {
        return true;
      }
      
      // 搜索消息内容
      return conversation.messages.some(message => 
        message.content.toLowerCase().includes(lowerQuery)
      );
    });
  }

  /**
   * 清理过期对话
   */
  private async cleanup(): Promise<void> {
    const conversations = await this.getConversationList();
    
    // 如果超过最大数量，删除最旧的对话
    if (conversations.length > this.config.maxConversations) {
      const toDelete = conversations
        .sort((a, b) => a.updatedAt - b.updatedAt)
        .slice(0, conversations.length - this.config.maxConversations);
      
      for (const conversation of toDelete) {
        await this.deleteConversation(conversation.id);
      }
    }
  }

  /**
   * 加载对话
   */
  private async loadConversations(): Promise<void> {
    const list = await this.cacheManager.get<string[]>('conversation:list') || [];
    
    // 预加载最近的对话到内存
    const recentIds = list.slice(0, 10);
    for (const id of recentIds) {
      const conversation = await this.cacheManager.get<Conversation>(`conversation:${id}`);
      if (conversation) {
        this.conversations.set(id, conversation);
      }
    }
  }

  /**
   * 生成ID
   */
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取缓存统计
   */
  public getStats() {
    return {
      conversations: this.conversations.size,
      contextWindows: this.contextWindows.size,
      cache: this.cacheManager.getStats(),
    };
  }

  /**
   * 销毁缓存
   */
  public destroy(): void {
    this.conversations.clear();
    this.contextWindows.clear();
    this.cacheManager.destroy();
  }
}

// 全局对话缓存实例
export const conversationCache = new ConversationCache();
