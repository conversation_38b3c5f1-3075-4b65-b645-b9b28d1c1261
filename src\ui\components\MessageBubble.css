/**
 * Message Bubble Styles - 消息气泡样式
 */

.message-bubble {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
  animation: messageSlideIn 0.3s ease-out;
}

.message-bubble.user {
  align-items: flex-end;
}

.message-bubble.assistant {
  align-items: flex-start;
}

.message-bubble.streaming {
  animation: messageSlideIn 0.3s ease-out, messagePulse 2s ease-in-out infinite;
}

/* 消息头部 */
.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  padding: 0 4px;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  font-size: 14px;
}

.message-bubble.user .message-avatar {
  background: var(--vscode-button-secondaryBackground);
  order: 2;
}

.message-meta {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.message-bubble.user .message-meta {
  align-items: flex-end;
  order: 1;
}

.message-role {
  font-size: 12px;
  font-weight: 600;
  color: var(--vscode-foreground);
}

.message-time {
  font-size: 11px;
  color: var(--vscode-descriptionForeground);
}

.expand-button {
  background: transparent;
  border: none;
  color: var(--vscode-icon-foreground);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  margin-left: auto;
}

.expand-button:hover {
  background: var(--vscode-toolbar-hoverBackground);
}

/* 消息内容 */
.message-content {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 12px;
  background: var(--vscode-panel-background);
  border: 1px solid var(--vscode-panel-border);
  position: relative;
  word-wrap: break-word;
  line-height: 1.5;
}

.message-bubble.user .message-content {
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border-color: var(--vscode-button-border);
}

.message-content.collapsed {
  max-height: 200px;
  overflow: hidden;
  position: relative;
}

.message-content.collapsed::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40px;
  background: linear-gradient(transparent, var(--vscode-panel-background));
  pointer-events: none;
}

/* Markdown样式 */
.message-content h1,
.message-content h2,
.message-content h3 {
  margin: 16px 0 8px 0;
  color: var(--vscode-foreground);
}

.message-content h1 { font-size: 20px; }
.message-content h2 { font-size: 18px; }
.message-content h3 { font-size: 16px; }

.message-content p {
  margin: 8px 0;
}

.message-content ul,
.message-content ol {
  margin: 8px 0;
  padding-left: 20px;
}

.message-content li {
  margin: 4px 0;
}

.message-content a {
  color: var(--vscode-textLink-foreground);
  text-decoration: none;
}

.message-content a:hover {
  text-decoration: underline;
}

.message-content strong {
  font-weight: 600;
  color: var(--vscode-foreground);
}

.message-content em {
  font-style: italic;
  color: var(--vscode-descriptionForeground);
}

.message-content code.inline-code {
  background: var(--vscode-textCodeBlock-background);
  color: var(--vscode-textPreformat-foreground);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: var(--vscode-editor-font-family);
  font-size: 0.9em;
}

/* 流式输入指示器 */
.streaming-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  padding: 8px 12px;
  background: var(--vscode-inputValidation-infoBackground);
  border-radius: 6px;
  border-left: 3px solid var(--vscode-inputValidation-infoBorder);
}

.typing-dots {
  display: flex;
  gap: 3px;
}

.typing-dots span {
  width: 4px;
  height: 4px;
  background: var(--vscode-progressBar-background);
  border-radius: 50%;
  animation: typingBounce 1.4s ease-in-out infinite both;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

/* 工具调用区域 */
.tool-calls {
  margin-top: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 消息操作 */
.message-actions {
  display: flex;
  gap: 4px;
  margin-top: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.message-bubble:hover .message-actions {
  opacity: 1;
}

.action-button {
  background: transparent;
  border: 1px solid var(--vscode-button-border);
  color: var(--vscode-icon-foreground);
  padding: 4px 6px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.action-button:hover {
  background: var(--vscode-toolbar-hoverBackground);
  border-color: var(--vscode-focusBorder);
}

.action-button:active {
  transform: scale(0.95);
}

/* 动画 */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes messagePulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes typingBounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .message-content {
    max-width: 85%;
    padding: 10px 12px;
  }
  
  .message-header {
    margin-bottom: 6px;
  }
  
  .message-avatar {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
  
  .message-actions {
    opacity: 1;
  }
}

/* 主题适配 */
@media (prefers-color-scheme: dark) {
  .message-content.collapsed::after {
    background: linear-gradient(transparent, var(--vscode-panel-background));
  }
}

/* 可访问性 */
.message-bubble:focus-within {
  outline: 2px solid var(--vscode-focusBorder);
  outline-offset: 2px;
  border-radius: 8px;
}

.action-button:focus {
  outline: 2px solid var(--vscode-focusBorder);
  outline-offset: 1px;
}
