/**
 * Global Styles - 全局样式
 * 
 * VS Code主题适配和通用样式定义
 */

/* 重置样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  font-family: var(--vscode-font-family);
  font-size: var(--vscode-font-size);
  line-height: 1.4;
  color: var(--vscode-foreground);
  background: var(--vscode-editor-background);
}

#root {
  height: 100%;
  width: 100%;
}

/* VS Code主题变量 */
:root {
  /* 字体 */
  --font-family-mono: var(--vscode-editor-font-family, 'Consolas', 'Monaco', 'Courier New', monospace);
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 24px;
  --spacing-2xl: 32px;
  
  /* 圆角 */
  --border-radius-sm: 3px;
  --border-radius-md: 6px;
  --border-radius-lg: 8px;
  --border-radius-xl: 12px;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  
  /* 动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.2s ease;
  --transition-slow: 0.3s ease;
}

/* 图标字体 */
.icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.icon-user::before { content: '👤'; }
.icon-robot::before { content: '🤖'; }
.icon-copy::before { content: '📋'; }
.icon-refresh::before { content: '🔄'; }
.icon-thumbs-up::before { content: '👍'; }
.icon-thumbs-down::before { content: '👎'; }
.icon-chevron-up::before { content: '▲'; }
.icon-chevron-down::before { content: '▼'; }
.icon-panel-open::before { content: '📋'; }
.icon-panel-close::before { content: '✖️'; }
.icon-trash::before { content: '🗑️'; }
.icon-send::before { content: '📤'; }
.icon-paperclip::before { content: '📎'; }
.icon-upload::before { content: '📤'; }
.icon-file::before { content: '📄'; }
.icon-x::before { content: '✖️'; }
.icon-tool::before { content: '🔧'; }
.icon-code::before { content: '💻'; }
.icon-search::before { content: '🔍'; }
.icon-chart::before { content: '📊'; }
.icon-clock::before { content: '🕐'; }
.icon-settings::before { content: '⚙️'; }
.icon-message::before { content: '💬'; }
.icon-database::before { content: '🗄️'; }
.icon-lightbulb::before { content: '💡'; }
.icon-git-branch::before { content: '🌿'; }
.icon-plus::before { content: '➕'; }
.icon-minimize::before { content: '➖'; }
.icon-play::before { content: '▶️'; }
.icon-check::before { content: '✅'; }
.icon-eye::before { content: '👁️'; }
.icon-download::before { content: '⬇️'; }
.icon-bug::before { content: '🐛'; }

/* 通用组件样式 */
.button {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--vscode-button-border);
  border-radius: var(--border-radius-md);
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  font-family: inherit;
  font-size: inherit;
  cursor: pointer;
  transition: all var(--transition-normal);
  text-decoration: none;
}

.button:hover {
  background: var(--vscode-button-hoverBackground);
}

.button:active {
  transform: translateY(1px);
}

.button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.button.primary {
  background: var(--vscode-button-background);
  border-color: var(--vscode-button-background);
}

.button.secondary {
  background: var(--vscode-button-secondaryBackground);
  border-color: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
}

.button.danger {
  background: var(--vscode-errorForeground);
  border-color: var(--vscode-errorForeground);
  color: white;
}

/* 输入框样式 */
.input {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--vscode-input-border);
  border-radius: var(--border-radius-md);
  background: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  font-family: inherit;
  font-size: inherit;
  transition: border-color var(--transition-normal);
}

.input:focus {
  outline: none;
  border-color: var(--vscode-focusBorder);
}

.input::placeholder {
  color: var(--vscode-input-placeholderForeground);
}

/* 卡片样式 */
.card {
  background: var(--vscode-panel-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

/* 标签样式 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--vscode-badge-background);
  color: var(--vscode-badge-foreground);
  border-radius: var(--border-radius-sm);
  font-size: 0.85em;
  font-weight: 500;
}

/* 分隔线 */
.divider {
  height: 1px;
  background: var(--vscode-panel-border);
  margin: var(--spacing-md) 0;
}

/* 加载动画 */
.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--vscode-progressBar-background);
  border-top: 2px solid var(--vscode-button-foreground);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-small {
  width: 12px;
  height: 12px;
  border-width: 1px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 进度条 */
.progress-bar {
  width: 100%;
  height: 4px;
  background: var(--vscode-progressBar-background);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--vscode-progressBar-foreground);
  transition: width var(--transition-normal);
}

/* 工具提示 */
.tooltip {
  position: relative;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--vscode-editorHoverWidget-background);
  color: var(--vscode-editorHoverWidget-foreground);
  border: 1px solid var(--vscode-editorHoverWidget-border);
  border-radius: var(--border-radius-sm);
  font-size: 0.8em;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity var(--transition-normal);
  z-index: 1000;
}

.tooltip:hover::after {
  opacity: 1;
}

/* 响应式工具类 */
.hidden-mobile {
  display: block;
}

.visible-mobile {
  display: none;
}

@media (max-width: 768px) {
  .hidden-mobile {
    display: none;
  }
  
  .visible-mobile {
    display: block;
  }
}

/* 可访问性 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 焦点样式 */
*:focus {
  outline: 2px solid var(--vscode-focusBorder);
  outline-offset: 2px;
}

/* 选择样式 */
::selection {
  background: var(--vscode-editor-selectionBackground);
  color: var(--vscode-editor-selectionForeground);
}

/* 主题适配 */
[data-theme="dark"] {
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.3);
}

[data-theme="light"] {
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
}

/* 动画性能优化 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
