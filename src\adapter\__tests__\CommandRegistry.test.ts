/**
 * CommandRegistry Tests
 */

import { CommandRegistry } from '../CommandRegistry';
import { EventBus } from '@/core/EventBus';

// Mock VS Code API
const mockCommands = {
  registerCommand: jest.fn(),
  executeCommand: jest.fn(),
};

const mockWindow = {
  showErrorMessage: jest.fn(),
};

// Update global vscode mock
(global as any).vscode = {
  ...(global as any).vscode,
  commands: mockCommands,
  window: { ...(global as any).vscode.window, ...mockWindow },
};

describe('CommandRegistry', () => {
  let commandRegistry: CommandRegistry;
  let eventBus: EventBus;

  beforeEach(() => {
    eventBus = new EventBus();

    // Reset mocks
    jest.clearAllMocks();

    // Setup mock return values
    mockCommands.registerCommand.mockReturnValue({
      dispose: jest.fn(),
    });

    // Update vscode mock with fresh mocks
    const vscode = require('vscode');
    vscode.commands = mockCommands;
    vscode.window = { ...vscode.window, ...mockWindow };

    commandRegistry = new CommandRegistry(eventBus);
  });

  afterEach(() => {
    commandRegistry.dispose();
    eventBus.destroy();
  });

  describe('command registration', () => {
    it('should register a command successfully', () => {
      const handler = jest.fn();
      const disposable = commandRegistry.registerCommand('test.command', handler);

      expect(mockCommands.registerCommand).toHaveBeenCalledWith(
        'test.command',
        expect.any(Function)
      );
      expect(commandRegistry.isCommandRegistered('test.command')).toBe(true);
      expect(disposable).toBeDefined();
      expect(typeof disposable.dispose).toBe('function');
    });

    it('should throw error when registering duplicate command', () => {
      const handler = jest.fn();
      
      commandRegistry.registerCommand('test.command', handler);
      
      expect(() => {
        commandRegistry.registerCommand('test.command', handler);
      }).toThrow('Command test.command is already registered');
    });

    it('should register command with metadata', () => {
      const handler = jest.fn();
      const metadata = {
        title: 'Test Command',
        category: 'Test',
        description: 'A test command',
      };

      commandRegistry.registerCommand('test.command', handler, metadata);

      const commandDef = commandRegistry.getCommandDefinition('test.command');
      expect(commandDef?.metadata).toEqual(metadata);
    });
  });

  describe('command execution', () => {
    it('should execute command and emit events', async () => {
      const handler = jest.fn().mockResolvedValue('result');
      const eventListener = jest.fn();
      
      eventBus.subscribe('command.started', eventListener);
      eventBus.subscribe('command.completed', eventListener);
      
      commandRegistry.registerCommand('test.command', handler);
      
      // Get the wrapped handler that was registered with VS Code
      const wrappedHandler = mockCommands.registerCommand.mock.calls[0][1];
      
      const result = await wrappedHandler('arg1', 'arg2');
      
      expect(handler).toHaveBeenCalledWith('arg1', 'arg2');
      expect(result).toBe('result');
      expect(eventListener).toHaveBeenCalledTimes(2);
    });

    it('should handle command execution errors', async () => {
      const error = new Error('Command failed');
      const handler = jest.fn().mockRejectedValue(error);
      const eventListener = jest.fn();
      
      eventBus.subscribe('command.failed', eventListener);
      
      commandRegistry.registerCommand('test.command', handler);
      
      const wrappedHandler = mockCommands.registerCommand.mock.calls[0][1];
      
      await expect(wrappedHandler()).rejects.toThrow('Command failed');
      expect(mockWindow.showErrorMessage).toHaveBeenCalledWith(
        'Command test.command failed: Command failed'
      );
      expect(eventListener).toHaveBeenCalledTimes(1);
    });

    it('should execute VS Code commands', async () => {
      const expectedResult = 'vscode result';
      mockCommands.executeCommand.mockResolvedValue(expectedResult);
      
      const result = await commandRegistry.executeCommand('vscode.command', 'arg1');
      
      expect(mockCommands.executeCommand).toHaveBeenCalledWith('vscode.command', 'arg1');
      expect(result).toBe(expectedResult);
    });
  });

  describe('command management', () => {
    it('should unregister command', () => {
      const handler = jest.fn();
      const mockDispose = jest.fn();
      
      mockCommands.registerCommand.mockReturnValue({
        dispose: mockDispose,
      });
      
      commandRegistry.registerCommand('test.command', handler);
      commandRegistry.unregisterCommand('test.command');
      
      expect(mockDispose).toHaveBeenCalled();
      expect(commandRegistry.isCommandRegistered('test.command')).toBe(false);
    });

    it('should get registered commands', () => {
      const handler = jest.fn();
      
      commandRegistry.registerCommand('test.command1', handler);
      commandRegistry.registerCommand('test.command2', handler);
      
      const commands = commandRegistry.getRegisteredCommands();
      expect(commands).toContain('test.command1');
      expect(commands).toContain('test.command2');
      expect(commands).toHaveLength(2);
    });

    it('should register multiple commands', () => {
      const commands = [
        { id: 'test.command1', handler: jest.fn() },
        { id: 'test.command2', handler: jest.fn() },
      ];
      
      const disposables = commandRegistry.registerCommands(commands);
      
      expect(disposables).toHaveLength(2);
      expect(commandRegistry.isCommandRegistered('test.command1')).toBe(true);
      expect(commandRegistry.isCommandRegistered('test.command2')).toBe(true);
    });

    it('should cleanup on registration failure', () => {
      const commands = [
        { id: 'test.command1', handler: jest.fn() },
        { id: 'test.command1', handler: jest.fn() }, // Duplicate
      ];
      
      expect(() => {
        commandRegistry.registerCommands(commands);
      }).toThrow();
      
      expect(commandRegistry.isCommandRegistered('test.command1')).toBe(false);
    });
  });

  describe('statistics', () => {
    it('should provide command statistics', () => {
      const handler = jest.fn();
      
      commandRegistry.registerCommand('test.command1', handler, { category: 'test' });
      commandRegistry.registerCommand('test.command2', handler, { category: 'test' });
      commandRegistry.registerCommand('other.command', handler, { category: 'other' });
      
      const stats = commandRegistry.getStats();
      
      expect(stats.totalCommands).toBe(3);
      expect(stats.commandsByCategory.test).toBe(2);
      expect(stats.commandsByCategory.other).toBe(1);
    });
  });

  describe('disposal', () => {
    it('should dispose all commands', () => {
      const handler = jest.fn();
      const mockDispose1 = jest.fn();
      const mockDispose2 = jest.fn();
      
      mockCommands.registerCommand
        .mockReturnValueOnce({ dispose: mockDispose1 })
        .mockReturnValueOnce({ dispose: mockDispose2 });
      
      commandRegistry.registerCommand('test.command1', handler);
      commandRegistry.registerCommand('test.command2', handler);
      
      commandRegistry.dispose();
      
      expect(mockDispose1).toHaveBeenCalled();
      expect(mockDispose2).toHaveBeenCalled();
      expect(commandRegistry.getRegisteredCommands()).toHaveLength(0);
    });
  });
});
