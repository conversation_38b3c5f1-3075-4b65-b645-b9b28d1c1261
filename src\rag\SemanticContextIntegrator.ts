/**
 * Semantic Context Integrator - 语义上下文集成器
 * 
 * 将语义分析结果集成到现有的上下文提取系统中
 */

import * as vscode from 'vscode';
import { EventBus } from '../core/EventBus';
import { SimpleCodeAnalyzer, SimpleCodeInfo } from './SimpleCodeAnalyzer';
import { EnhancedCodeContext } from '../core/EnhancedCodeContextExtractor';

export interface SemanticEnhancedContext extends EnhancedCodeContext {
  semanticInfo: SimpleCodeInfo;
  semanticSimilarity?: number;
  codeComplexityScore: number;
  maintainabilityIndex: number;
  semanticTags: string[];
}

export interface SemanticSearchQuery {
  text: string;
  semanticWeight: number;
  syntacticWeight: number;
  contextFiles?: string[];
  targetLanguages?: string[];
  complexityRange?: [number, number];
}

export interface SemanticSearchResult {
  context: SemanticEnhancedContext;
  relevanceScore: number;
  semanticScore: number;
  syntacticScore: number;
  explanation: string;
}

export class SemanticContextIntegrator {
  private eventBus: EventBus;
  private codeAnalyzer: SimpleCodeAnalyzer;
  private semanticCache: Map<string, SimpleCodeInfo> = new Map();
  private analysisQueue: Set<string> = new Set();
  private isInitialized = false;

  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
    this.codeAnalyzer = new SimpleCodeAnalyzer(eventBus);
  }

  /**
   * 初始化语义分析器
   */
  async initialize(workspaceFiles: string[]): Promise<void> {
    if (this.isInitialized) return;

    try {
      // 过滤代码文件
      const codeFiles = workspaceFiles.filter(file =>
        file.endsWith('.ts') ||
        file.endsWith('.js') ||
        file.endsWith('.tsx') ||
        file.endsWith('.jsx') ||
        file.endsWith('.py') ||
        file.endsWith('.java') ||
        file.endsWith('.cpp') ||
        file.endsWith('.c') ||
        file.endsWith('.cs') ||
        file.endsWith('.go') ||
        file.endsWith('.rs')
      );

      this.isInitialized = true;

      this.eventBus.emit({
        type: 'semantic_integrator.initialized',
        source: 'SemanticContextIntegrator',
        fileCount: codeFiles.length
      });
    } catch (error) {
      this.eventBus.emit({
        type: 'semantic_integrator.initialization_failed',
        source: 'SemanticContextIntegrator',
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * 增强代码上下文的语义信息
   */
  async enhanceContext(context: EnhancedCodeContext): Promise<SemanticEnhancedContext> {
    const semanticInfo = await this.getSemanticInfo(context.filePath);
    
    const enhancedContext: SemanticEnhancedContext = {
      ...context,
      semanticInfo,
      codeComplexityScore: this.calculateComplexityScore(semanticInfo),
      maintainabilityIndex: this.calculateMaintainabilityIndex(semanticInfo),
      semanticTags: this.generateSemanticTags(semanticInfo)
    };

    return enhancedContext;
  }

  /**
   * 获取文件的语义信息
   */
  async getSemanticInfo(filePath: string): Promise<SimpleCodeInfo> {
    // 检查缓存
    if (this.semanticCache.has(filePath)) {
      const cached = this.semanticCache.get(filePath)!;
      
      // 检查文件是否已修改
      if (await this.isFileModified(filePath, cached.lastAnalyzed)) {
        this.semanticCache.delete(filePath);
      } else {
        return cached;
      }
    }

    // 避免重复分析
    if (this.analysisQueue.has(filePath)) {
      // 等待正在进行的分析完成
      return new Promise((resolve) => {
        const checkInterval = setInterval(() => {
          if (this.semanticCache.has(filePath)) {
            clearInterval(checkInterval);
            resolve(this.semanticCache.get(filePath)!);
          }
        }, 100);
      });
    }

    this.analysisQueue.add(filePath);

    try {
      const semanticInfo = await this.codeAnalyzer.analyzeFile(filePath);
      this.semanticCache.set(filePath, semanticInfo);
      return semanticInfo;
    } finally {
      this.analysisQueue.delete(filePath);
    }
  }

  /**
   * 执行语义搜索
   */
  async semanticSearch(
    query: SemanticSearchQuery,
    contexts: EnhancedCodeContext[]
  ): Promise<SemanticSearchResult[]> {
    const results: SemanticSearchResult[] = [];

    for (const context of contexts) {
      const enhancedContext = await this.enhanceContext(context);
      const result = await this.calculateSemanticRelevance(query, enhancedContext);
      
      if (result.relevanceScore > 0.1) { // 过滤低相关性结果
        results.push(result);
      }
    }

    // 按相关性排序
    results.sort((a, b) => b.relevanceScore - a.relevanceScore);

    this.eventBus.emit({
      type: 'semantic_integrator.search_completed',
      source: 'SemanticContextIntegrator',
      queryText: query.text,
      resultCount: results.length
    });

    return results;
  }

  /**
   * 计算语义相关性
   */
  private async calculateSemanticRelevance(
    query: SemanticSearchQuery,
    context: SemanticEnhancedContext
  ): Promise<SemanticSearchResult> {
    // 1. 语义相似度计算
    const semanticScore = await this.calculateSemanticSimilarity(
      query.text,
      context.semanticInfo
    );

    // 2. 语法匹配度计算
    const syntacticScore = this.calculateSyntacticSimilarity(
      query.text,
      context.content || context.semanticInfo.content
    );

    // 3. 上下文相关性
    const contextScore = this.calculateContextRelevance(query, context);

    // 4. 综合评分
    const relevanceScore = 
      semanticScore * query.semanticWeight +
      syntacticScore * query.syntacticWeight +
      contextScore * 0.2;

    // 5. 生成解释
    const explanation = this.generateExplanation(
      semanticScore,
      syntacticScore,
      contextScore,
      context
    );

    return {
      context,
      relevanceScore,
      semanticScore,
      syntacticScore,
      explanation
    };
  }

  /**
   * 计算语义相似度
   */
  private async calculateSemanticSimilarity(
    queryText: string,
    semanticInfo: SimpleCodeInfo
  ): Promise<number> {
    // TODO: 实现真实的语义相似度计算
    // 这里使用简化的关键词匹配作为占位

    const queryTokens = this.tokenize(queryText.toLowerCase());
    const codeText = semanticInfo.content.toLowerCase();
    const codeTokens = this.tokenize(codeText);

    // 计算Jaccard相似度
    const intersection = queryTokens.filter(token => codeTokens.includes(token));
    const union = Array.from(new Set([...queryTokens, ...codeTokens]));

    return intersection.length / union.length;
  }

  /**
   * 计算语法相似度
   */
  private calculateSyntacticSimilarity(queryText: string, codeContent: string): number {
    const queryTokens = this.tokenize(queryText.toLowerCase());
    const codeTokens = this.tokenize(codeContent.toLowerCase());
    
    let matches = 0;
    for (const token of queryTokens) {
      if (codeTokens.includes(token)) {
        matches++;
      }
    }
    
    return queryTokens.length > 0 ? matches / queryTokens.length : 0;
  }

  /**
   * 计算上下文相关性
   */
  private calculateContextRelevance(
    query: SemanticSearchQuery,
    context: SemanticEnhancedContext
  ): number {
    let score = 0;

    // 语言匹配
    if (query.targetLanguages?.includes(context.language || context.semanticInfo.language)) {
      score += 0.3;
    }

    // 复杂度匹配
    if (query.complexityRange) {
      const [min, max] = query.complexityRange;
      const complexity = context.codeComplexityScore;
      if (complexity >= min && complexity <= max) {
        score += 0.3;
      }
    }

    // 文件相关性
    if (query.contextFiles?.some(file => (context.filePath || context.semanticInfo.filePath).includes(file))) {
      score += 0.4;
    }

    return Math.min(score, 1.0);
  }

  /**
   * 生成解释文本
   */
  private generateExplanation(
    semanticScore: number,
    syntacticScore: number,
    contextScore: number,
    context: SemanticEnhancedContext
  ): string {
    const explanations: string[] = [];

    if (semanticScore > 0.7) {
      explanations.push('语义高度相关');
    } else if (semanticScore > 0.4) {
      explanations.push('语义部分相关');
    }

    if (syntacticScore > 0.6) {
      explanations.push('关键词匹配度高');
    }

    if (contextScore > 0.5) {
      explanations.push('上下文相关');
    }

    if (context.codeComplexityScore > 80) {
      explanations.push('代码复杂度较高');
    }

    if (context.maintainabilityIndex > 80) {
      explanations.push('可维护性良好');
    }

    return explanations.join('，') || '基础匹配';
  }

  /**
   * 计算代码复杂度评分
   */
  private calculateComplexityScore(semanticInfo: SimpleCodeInfo): number {
    const metrics = semanticInfo.metrics;

    // 综合复杂度评分 (0-100)
    const complexityWeight = Math.min(metrics.complexity / 10, 1) * 40;
    const sizeWeight = Math.min(metrics.lines / 1000, 1) * 30;
    const dependencyWeight = Math.min((metrics.imports + metrics.exports) / 20, 1) * 30;

    return Math.round(complexityWeight + sizeWeight + dependencyWeight);
  }

  /**
   * 计算可维护性指数
   */
  private calculateMaintainabilityIndex(semanticInfo: SimpleCodeInfo): number {
    const metrics = semanticInfo.metrics;

    // 简化的可维护性指数计算 (0-100)
    let maintainability = 100;

    // 复杂度惩罚
    maintainability -= Math.min(metrics.complexity * 2, 40);

    // 文件大小惩罚
    maintainability -= Math.min(metrics.lines / 50, 20);

    // 函数数量奖励（适度的函数分解是好的）
    if (metrics.functions > 0 && metrics.functions < 20) {
      maintainability += 10;
    }

    // 接口定义奖励
    if (metrics.interfaces > 0) {
      maintainability += 5;
    }

    return Math.max(0, Math.round(maintainability));
  }

  /**
   * 生成语义标签
   */
  private generateSemanticTags(semanticInfo: SimpleCodeInfo): string[] {
    const tags: string[] = [];
    const metrics = semanticInfo.metrics;

    // 复杂度标签
    if (metrics.complexity > 20) tags.push('high-complexity');
    else if (metrics.complexity > 10) tags.push('medium-complexity');
    else tags.push('low-complexity');

    // 大小标签
    if (metrics.lines > 500) tags.push('large-file');
    else if (metrics.lines > 100) tags.push('medium-file');
    else tags.push('small-file');

    // 功能标签
    if (metrics.classes > 0) tags.push('object-oriented');
    if (metrics.functions > 10) tags.push('function-heavy');
    if (metrics.interfaces > 0) tags.push('interface-defined');

    // 语言标签
    tags.push(`lang-${semanticInfo.language}`);

    // 导入导出标签
    if (metrics.imports > 5) tags.push('import-heavy');
    if (metrics.exports > 3) tags.push('export-heavy');

    return tags;
  }

  /**
   * 简单分词
   */
  private tokenize(text: string): string[] {
    return text
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(token => token.length > 2);
  }

  /**
   * 检查文件是否已修改
   */
  private async isFileModified(filePath: string, lastAnalyzed: Date): Promise<boolean> {
    try {
      const stats = await vscode.workspace.fs.stat(vscode.Uri.file(filePath));
      return stats.mtime > lastAnalyzed.getTime();
    } catch {
      return true; // 如果无法获取文件状态，假设已修改
    }
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    this.semanticCache.clear();
    this.analysisQueue.clear();
    
    this.eventBus.emit({
      type: 'semantic_integrator.cache_cleared',
      source: 'SemanticContextIntegrator'
    });
  }

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return {
      cachedFiles: this.semanticCache.size,
      queuedFiles: this.analysisQueue.size,
      isInitialized: this.isInitialized
    };
  }
}
