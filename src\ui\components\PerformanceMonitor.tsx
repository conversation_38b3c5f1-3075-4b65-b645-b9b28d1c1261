/**
 * 性能监控组件
 * 
 * 实时监控动画性能、渲染性能和交互延迟
 * 提供性能指标显示和优化建议
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { animationManager } from '../utils/AnimationManager';
import { useTheme } from '../theme/ThemeProvider';

interface PerformanceData {
  fps: number;
  averageFps: number;
  frameTime: number;
  droppedFrames: number;
  memoryUsage: number;
  interactionDelay: number;
  animationCount: number;
  renderTime: number;
}

interface PerformanceMonitorProps {
  show?: boolean;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  compact?: boolean;
  onPerformanceChange?: (data: PerformanceData) => void;
}

export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  show = false,
  position = 'top-right',
  compact = false,
  onPerformanceChange,
}) => {
  const { colors, spacing, typography, borderRadius } = useTheme();
  const [performanceData, setPerformanceData] = useState<PerformanceData>({
    fps: 60,
    averageFps: 60,
    frameTime: 16.67,
    droppedFrames: 0,
    memoryUsage: 0,
    interactionDelay: 0,
    animationCount: 0,
    renderTime: 0,
  });
  
  const [isExpanded, setIsExpanded] = useState(!compact);
  const intervalRef = useRef<NodeJS.Timeout>();
  const lastInteractionTime = useRef<number>(0);
  const renderStartTime = useRef<number>(0);

  // 测量交互延迟
  const measureInteractionDelay = useCallback(() => {
    const startTime = performance.now();
    
    // 使用requestAnimationFrame来测量到下一帧的延迟
    requestAnimationFrame(() => {
      const delay = performance.now() - startTime;
      setPerformanceData(prev => ({
        ...prev,
        interactionDelay: delay,
      }));
    });
  }, []);

  // 测量渲染时间
  const measureRenderTime = useCallback(() => {
    renderStartTime.current = performance.now();
    
    // 使用setTimeout(0)来测量渲染完成时间
    setTimeout(() => {
      const renderTime = performance.now() - renderStartTime.current;
      setPerformanceData(prev => ({
        ...prev,
        renderTime,
      }));
    }, 0);
  }, []);

  // 获取内存使用情况
  const getMemoryUsage = useCallback((): number => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return Math.round(memory.usedJSHeapSize / 1024 / 1024 * 100) / 100; // MB
    }
    return 0;
  }, []);

  // 更新性能数据
  const updatePerformanceData = useCallback(() => {
    const animationMetrics = animationManager.getPerformanceMetrics();
    const memoryUsage = getMemoryUsage();
    
    const newData: PerformanceData = {
      fps: Math.round(animationMetrics.fps),
      averageFps: Math.round(animationMetrics.averageFps),
      frameTime: Math.round(animationMetrics.frameTimeHistory.slice(-1)[0] || 16.67 * 100) / 100,
      droppedFrames: animationMetrics.droppedFrames,
      memoryUsage,
      interactionDelay: performanceData.interactionDelay,
      animationCount: animationManager['animations']?.size || 0,
      renderTime: performanceData.renderTime,
    };
    
    setPerformanceData(newData);
    onPerformanceChange?.(newData);
    
    // 触发交互延迟测量
    measureInteractionDelay();
    measureRenderTime();
  }, [performanceData.interactionDelay, performanceData.renderTime, getMemoryUsage, measureInteractionDelay, measureRenderTime, onPerformanceChange]);

  // 启动性能监控
  useEffect(() => {
    if (show) {
      intervalRef.current = setInterval(updatePerformanceData, 1000);
      
      // 立即更新一次
      updatePerformanceData();
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }
    
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [show, updatePerformanceData]);

  // 监听用户交互
  useEffect(() => {
    const handleInteraction = () => {
      lastInteractionTime.current = performance.now();
      measureInteractionDelay();
    };
    
    document.addEventListener('click', handleInteraction);
    document.addEventListener('keydown', handleInteraction);
    document.addEventListener('scroll', handleInteraction);
    
    return () => {
      document.removeEventListener('click', handleInteraction);
      document.removeEventListener('keydown', handleInteraction);
      document.removeEventListener('scroll', handleInteraction);
    };
  }, [measureInteractionDelay]);

  if (!show) {
    return null;
  }

  // 获取性能状态颜色
  const getStatusColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return colors.success;
    if (value <= thresholds.warning) return colors.warning;
    return colors.error;
  };

  // 获取FPS状态颜色
  const getFpsColor = (fps: number) => {
    if (fps >= 55) return colors.success;
    if (fps >= 30) return colors.warning;
    return colors.error;
  };

  // 位置样式
  const getPositionStyles = (): React.CSSProperties => {
    const baseStyles: React.CSSProperties = {
      position: 'fixed',
      zIndex: 9999,
      padding: spacing.sm,
      backgroundColor: colors.surface,
      border: `1px solid ${colors.border}`,
      borderRadius: borderRadius.md,
      fontSize: typography.fontSize.sm,
      fontFamily: typography.fontFamily,
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      backdropFilter: 'blur(8px)',
      maxWidth: compact ? '200px' : '300px',
    };

    switch (position) {
      case 'top-left':
        return { ...baseStyles, top: spacing.md, left: spacing.md };
      case 'top-right':
        return { ...baseStyles, top: spacing.md, right: spacing.md };
      case 'bottom-left':
        return { ...baseStyles, bottom: spacing.md, left: spacing.md };
      case 'bottom-right':
        return { ...baseStyles, bottom: spacing.md, right: spacing.md };
      default:
        return { ...baseStyles, top: spacing.md, right: spacing.md };
    }
  };

  const headerStyles: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: isExpanded ? spacing.xs : 0,
    cursor: 'pointer',
  };

  const metricStyles: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs / 2,
    fontSize: typography.fontSize.xs,
  };

  const valueStyles = (color: string): React.CSSProperties => ({
    color,
    fontWeight: 'bold',
    fontFamily: 'monospace',
  });

  return (
    <div style={getPositionStyles()}>
      <div style={headerStyles} onClick={() => setIsExpanded(!isExpanded)}>
        <span style={{ fontWeight: 'bold', color: colors.text }}>
          性能监控
        </span>
        <span style={{ color: colors.textSecondary, fontSize: '12px' }}>
          {isExpanded ? '−' : '+'}
        </span>
      </div>
      
      {isExpanded && (
        <div>
          <div style={metricStyles}>
            <span style={{ color: colors.textSecondary }}>FPS:</span>
            <span style={valueStyles(getFpsColor(performanceData.fps))}>
              {performanceData.fps}
            </span>
          </div>
          
          <div style={metricStyles}>
            <span style={{ color: colors.textSecondary }}>平均FPS:</span>
            <span style={valueStyles(getFpsColor(performanceData.averageFps))}>
              {performanceData.averageFps}
            </span>
          </div>
          
          <div style={metricStyles}>
            <span style={{ color: colors.textSecondary }}>帧时间:</span>
            <span style={valueStyles(getStatusColor(performanceData.frameTime, { good: 16.67, warning: 33.33 }))}>
              {performanceData.frameTime}ms
            </span>
          </div>
          
          <div style={metricStyles}>
            <span style={{ color: colors.textSecondary }}>丢帧:</span>
            <span style={valueStyles(getStatusColor(performanceData.droppedFrames, { good: 0, warning: 5 }))}>
              {performanceData.droppedFrames}
            </span>
          </div>
          
          <div style={metricStyles}>
            <span style={{ color: colors.textSecondary }}>交互延迟:</span>
            <span style={valueStyles(getStatusColor(performanceData.interactionDelay, { good: 50, warning: 100 }))}>
              {performanceData.interactionDelay.toFixed(1)}ms
            </span>
          </div>
          
          <div style={metricStyles}>
            <span style={{ color: colors.textSecondary }}>渲染时间:</span>
            <span style={valueStyles(getStatusColor(performanceData.renderTime, { good: 16, warning: 50 }))}>
              {performanceData.renderTime.toFixed(1)}ms
            </span>
          </div>
          
          <div style={metricStyles}>
            <span style={{ color: colors.textSecondary }}>动画数:</span>
            <span style={valueStyles(getStatusColor(performanceData.animationCount, { good: 5, warning: 10 }))}>
              {performanceData.animationCount}
            </span>
          </div>
          
          {performanceData.memoryUsage > 0 && (
            <div style={metricStyles}>
              <span style={{ color: colors.textSecondary }}>内存:</span>
              <span style={valueStyles(getStatusColor(performanceData.memoryUsage, { good: 50, warning: 100 }))}>
                {performanceData.memoryUsage}MB
              </span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// Hook：使用性能监控
export const usePerformanceMonitor = () => {
  const [performanceData, setPerformanceData] = useState<PerformanceData | null>(null);
  const [isOptimal, setIsOptimal] = useState(true);
  
  const handlePerformanceChange = useCallback((data: PerformanceData) => {
    setPerformanceData(data);
    
    // 判断性能是否最优
    const optimal = 
      data.fps >= 55 &&
      data.interactionDelay < 50 &&
      data.droppedFrames < 2 &&
      data.renderTime < 16;
    
    setIsOptimal(optimal);
  }, []);
  
  return {
    performanceData,
    isOptimal,
    handlePerformanceChange,
  };
};
