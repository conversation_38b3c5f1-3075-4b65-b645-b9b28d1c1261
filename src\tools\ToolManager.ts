/**
 * Tool Manager - 工具管理器
 * 
 * 负责工具的注册、管理和执行
 */

import { EventBus } from '@/core/EventBus';
import {
  IToolManager,
  ITool,
  ToolDefinition,
  ToolCategory,
  ToolExecutionContext,
  ToolExecutionResult,
  ToolUsageStats,
  ToolHealthStatus,
  ToolError,
  ToolValidationError,
  ToolExecutionError,
  ToolPermissionError,
} from './interfaces';

export class ToolManager implements IToolManager {
  private tools: Map<string, ITool> = new Map();
  private eventBus: EventBus;
  private permissions: Set<string> = new Set();
  private executionHistory: Array<{
    toolName: string;
    timestamp: number;
    duration: number;
    success: boolean;
    error?: string;
  }> = [];

  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
    this.initializeDefaultPermissions();
  }

  private initializeDefaultPermissions(): void {
    // 默认权限，可以通过配置修改
    this.permissions.add('read-files');
    this.permissions.add('write-files');
    this.permissions.add('execute-commands');
    this.permissions.add('git-operations');
    this.permissions.add('workspace-modification');
  }

  // 工具注册和管理
  registerTool(tool: ITool): void {
    const name = tool.definition.name;
    
    if (this.tools.has(name)) {
      throw new ToolError(`Tool ${name} is already registered`, name, 'ALREADY_REGISTERED');
    }

    // 验证工具权限
    this.validateToolPermissions(tool);

    this.tools.set(name, tool);

    this.eventBus.emit({
      type: 'tool.registered',
      source: 'ToolManager',
      toolName: name,
      category: tool.definition.category,
      permissions: tool.definition.permissions || [],
    });
  }

  unregisterTool(name: string): void {
    const removed = this.tools.delete(name);
    
    if (removed) {
      this.eventBus.emit({
        type: 'tool.unregistered',
        source: 'ToolManager',
        toolName: name,
      });
    }
  }

  getTool(name: string): ITool | undefined {
    return this.tools.get(name);
  }

  listTools(category?: ToolCategory): ToolDefinition[] {
    const tools = Array.from(this.tools.values());
    
    if (category) {
      return tools
        .filter(tool => tool.definition.category === category)
        .map(tool => tool.definition);
    }
    
    return tools.map(tool => tool.definition);
  }

  // 工具执行
  async executeTool(
    name: string,
    parameters: Record<string, any>,
    context: ToolExecutionContext
  ): Promise<ToolExecutionResult> {
    const tool = this.getTool(name);
    if (!tool) {
      throw new ToolError(`Tool ${name} not found`, name, 'NOT_FOUND');
    }

    const startTime = Date.now();

    try {
      // 发布执行开始事件
      await this.eventBus.emit({
        type: 'tool.execution_started',
        source: 'ToolManager',
        toolName: name,
        parameters,
        context,
      });

      // 验证参数
      const validation = tool.validate(parameters);
      if (!validation.valid) {
        throw new ToolValidationError(name, validation.errors);
      }

      // 检查权限
      this.checkToolPermissions(tool, context);

      // 执行工具
      const result = await tool.execute(parameters, context);
      const duration = Date.now() - startTime;

      // 记录执行历史
      this.recordExecution(name, duration, true);

      // 发布执行完成事件
      await this.eventBus.emit({
        type: 'tool.execution_completed',
        source: 'ToolManager',
        toolName: name,
        duration,
        success: result.success,
        result: result.result,
      });

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = (error as Error).message;

      // 记录执行历史
      this.recordExecution(name, duration, false, errorMessage);

      // 发布执行失败事件
      await this.eventBus.emit({
        type: 'tool.execution_failed',
        source: 'ToolManager',
        toolName: name,
        duration,
        error: errorMessage,
      });

      throw error;
    }
  }

  // 批量执行
  async executeToolChain(
    calls: Array<{
      name: string;
      parameters: Record<string, any>;
    }>,
    context: ToolExecutionContext
  ): Promise<ToolExecutionResult[]> {
    const results: ToolExecutionResult[] = [];
    let updatedContext = { ...context };

    for (const call of calls) {
      try {
        const result = await this.executeTool(call.name, call.parameters, updatedContext);
        results.push(result);

        // 更新上下文，传递前一个工具的结果
        if (result.success && result.metadata?.filesModified) {
          updatedContext = {
            ...updatedContext,
            // 可以根据需要更新上下文
          };
        }

        // 如果某个工具执行失败，可以选择继续或停止
        if (!result.success) {
          // 这里可以根据策略决定是否继续
          break;
        }
      } catch (error) {
        results.push({
          success: false,
          error: (error as Error).message,
          metadata: { duration: 0 },
        });
        break; // 停止执行链
      }
    }

    return results;
  }

  // 工具发现和搜索
  searchTools(query: string): ToolDefinition[] {
    const lowerQuery = query.toLowerCase();
    
    return Array.from(this.tools.values())
      .filter(tool => {
        const def = tool.definition;
        return (
          def.name.toLowerCase().includes(lowerQuery) ||
          def.description.toLowerCase().includes(lowerQuery) ||
          def.category.toLowerCase().includes(lowerQuery)
        );
      })
      .map(tool => tool.definition);
  }

  suggestTools(context: ToolExecutionContext): ToolDefinition[] {
    const suggestions: ToolDefinition[] = [];

    // 基于上下文建议工具
    if (context.activeFile) {
      const extension = context.activeFile.split('.').pop()?.toLowerCase();
      
      // 根据文件类型建议工具
      if (['js', 'ts', 'jsx', 'tsx'].includes(extension || '')) {
        suggestions.push(...this.listTools('code-analysis'));
        suggestions.push(...this.listTools('refactor'));
      }
      
      if (['md', 'txt'].includes(extension || '')) {
        suggestions.push(...this.listTools('documentation'));
      }
    }

    if (context.selectedText) {
      suggestions.push(...this.listTools('search'));
      suggestions.push(...this.listTools('refactor'));
    }

    // 去重并限制数量
    const uniqueSuggestions = suggestions.filter(
      (tool, index, self) => self.findIndex(t => t.name === tool.name) === index
    );

    return uniqueSuggestions.slice(0, 10);
  }

  // 统计和监控
  getUsageStats(): Record<string, ToolUsageStats> {
    const stats: Record<string, ToolUsageStats> = {};

    for (const [name, tool] of this.tools) {
      stats[name] = tool.getUsageStats();
    }

    return stats;
  }

  async getToolHealth(): Promise<Record<string, ToolHealthStatus>> {
    const health: Record<string, ToolHealthStatus> = {};

    for (const [name, tool] of this.tools) {
      try {
        // 执行健康检查（如果工具支持）
        if ('healthCheck' in tool && typeof tool.healthCheck === 'function') {
          health[name] = await (tool as any).healthCheck();
        } else {
          // 默认健康状态
          health[name] = {
            status: 'healthy',
            lastCheck: Date.now(),
          };
        }
      } catch (error) {
        health[name] = {
          status: 'unhealthy',
          lastCheck: Date.now(),
          issues: [(error as Error).message],
        };
      }
    }

    return health;
  }

  // 权限管理
  private validateToolPermissions(tool: ITool): void {
    const requiredPermissions = tool.definition.permissions || [];
    
    for (const permission of requiredPermissions) {
      if (!this.permissions.has(permission)) {
        throw new ToolPermissionError(tool.definition.name, permission);
      }
    }
  }

  private checkToolPermissions(tool: ITool, context: ToolExecutionContext): void {
    const requiredPermissions = tool.definition.permissions || [];
    
    for (const permission of requiredPermissions) {
      if (!this.permissions.has(permission)) {
        throw new ToolPermissionError(tool.definition.name, permission);
      }
    }

    // 可以根据上下文进行更细粒度的权限检查
    if (requiredPermissions.includes('write-files') && !context.workspaceRoot) {
      throw new ToolPermissionError(
        tool.definition.name,
        'write-files',
        'No workspace root available'
      );
    }
  }

  // 执行历史记录
  private recordExecution(
    toolName: string,
    duration: number,
    success: boolean,
    error?: string
  ): void {
    this.executionHistory.push({
      toolName,
      timestamp: Date.now(),
      duration,
      success,
      error,
    });

    // 限制历史记录大小
    if (this.executionHistory.length > 1000) {
      this.executionHistory.shift();
    }
  }

  // 获取执行历史
  getExecutionHistory(toolName?: string): typeof this.executionHistory {
    if (toolName) {
      return this.executionHistory.filter(record => record.toolName === toolName);
    }
    return [...this.executionHistory];
  }

  // 权限管理方法
  grantPermission(permission: string): void {
    this.permissions.add(permission);
  }

  revokePermission(permission: string): void {
    this.permissions.delete(permission);
  }

  hasPermission(permission: string): boolean {
    return this.permissions.has(permission);
  }

  // 清理资源
  dispose(): void {
    this.tools.clear();
    this.executionHistory = [];
    this.permissions.clear();
  }
}
