/**
 * VS Code Adapter Module Exports
 * 
 * 导出VS Code适配器模块的所有公共接口
 */

export { VSCodeAdapter } from './VSCodeAdapter';
export { CommandRegistry } from './CommandRegistry';
export { WebviewManager } from './WebviewManager';
export { EventTranslator } from './EventTranslator';

export type {
  IVSCodeAdapter,
  IVSCodeAdapterExtended,
  WebviewConfig,
  IWebview,
  CommandHandler,
  DocumentChangeEvent,
  SelectionChangeEvent,
  WorkspaceChangeEvent,
  DocumentChangeHandler,
  SelectionChangeHandler,
  WorkspaceChangeHandler,
  StatusBarConfig,
  IStatusBarItem,
  QuickPickConfig,
  InputBoxConfig,
  ProgressConfig,
  DiagnosticInfo,
  ITreeDataProvider,
} from './interfaces';

// 工厂函数
export function createVSCodeAdapter(eventBus: any, extensionContext: any) {
  return new VSCodeAdapter(eventBus, extensionContext);
}
