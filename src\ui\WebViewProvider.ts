/**
 * WebView Provider - WebView提供者
 * 
 * 负责创建和管理VS Code WebView，集成React UI组件
 */

import * as vscode from 'vscode';
import * as path from 'path';
import { ChatService } from '../core/ChatService';
import { EventBus } from '../core/EventBus';

export class WebViewProvider implements vscode.WebviewViewProvider {
  public static readonly viewType = 'ai-agent.chatView';
  
  private _view?: vscode.WebviewView;
  private chatService: ChatService;
  private eventBus: EventBus;
  private disposables: vscode.Disposable[] = [];

  constructor(
    private readonly _extensionUri: vscode.Uri,
    chatService: ChatService,
    eventBus: EventBus
  ) {
    this.chatService = chatService;
    this.eventBus = eventBus;
  }

  public resolveWebviewView(
    webviewView: vscode.WebviewView,
    context: vscode.WebviewViewResolveContext,
    _token: vscode.CancellationToken,
  ) {
    this._view = webviewView;

    webviewView.webview.options = {
      enableScripts: true,
      localResourceRoots: [
        this._extensionUri
      ]
    };

    webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

    // 设置消息处理
    this.setupMessageHandling(webviewView.webview);

    // 监听事件总线
    this.setupEventListeners();
  }

  /**
   * 设置消息处理
   */
  private setupMessageHandling(webview: vscode.Webview) {
    webview.onDidReceiveMessage(
      async (data) => {
        try {
          switch (data.type) {
            case 'sendMessage':
              await this.handleSendMessage(data.message, data.attachments);
              break;
            
            case 'executeToolCall':
              await this.handleToolCall(data.toolCall);
              break;
            
            case 'clearChat':
              await this.handleClearChat();
              break;
            
            case 'getSystemStats':
              await this.handleGetSystemStats();
              break;
            
            case 'performRAGQuery':
              await this.handleRAGQuery(data.query, data.options);
              break;
            
            case 'rebuildIndex':
              await this.handleRebuildIndex();
              break;
            
            case 'syncKnowledge':
              await this.handleSyncKnowledge();
              break;
            
            case 'ready':
              await this.handleWebViewReady();
              break;
            
            default:
              console.warn('Unknown message type:', data.type);
          }
        } catch (error) {
          console.error('Error handling webview message:', error);
          this.sendMessage({
            type: 'error',
            error: (error as Error).message
          });
        }
      },
      null,
      this.disposables
    );
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners() {
    // 监听聊天服务事件
    this.eventBus.on('chat.message_received', (event) => {
      this.sendMessage({
        type: 'messageReceived',
        message: event.message
      });
    });

    this.eventBus.on('chat.streaming_chunk', (event) => {
      this.sendMessage({
        type: 'streamingChunk',
        chunk: event.chunk
      });
    });

    this.eventBus.on('chat.streaming_complete', (event) => {
      this.sendMessage({
        type: 'streamingComplete'
      });
    });

    // 监听RAG系统事件
    this.eventBus.on('rag.query_completed', (event) => {
      this.sendMessage({
        type: 'ragQueryCompleted',
        result: event.result
      });
    });

    // 监听工具调用事件
    this.eventBus.on('tool.execution_started', (event) => {
      this.sendMessage({
        type: 'toolExecutionStarted',
        toolCall: event.toolCall
      });
    });

    this.eventBus.on('tool.execution_completed', (event) => {
      this.sendMessage({
        type: 'toolExecutionCompleted',
        toolCall: event.toolCall,
        result: event.result
      });
    });
  }

  /**
   * 处理发送消息
   */
  private async handleSendMessage(message: string, attachments?: any[]) {
    try {
      // 发送消息到聊天服务
      await this.chatService.sendMessage({ message, stream: false });
      
      // 通知UI消息已发送
      this.sendMessage({
        type: 'messageSent',
        message: {
          role: 'user',
          content: message,
          timestamp: new Date(),
          attachments
        }
      });
    } catch (error) {
      this.sendMessage({
        type: 'error',
        error: (error as Error).message
      });
    }
  }

  /**
   * 处理工具调用
   */
  private async handleToolCall(toolCall: any) {
    try {
      const result = await this.chatService.getProgrammingTools().executeTool(
        toolCall.name,
        toolCall.parameters
      );
      
      this.sendMessage({
        type: 'toolCallResult',
        toolCall,
        result
      });
    } catch (error) {
      this.sendMessage({
        type: 'toolCallError',
        toolCall,
        error: (error as Error).message
      });
    }
  }

  /**
   * 处理清空聊天
   */
  private async handleClearChat() {
    try {
      this.chatService.clearConversationHistory();
      this.sendMessage({
        type: 'chatCleared'
      });
    } catch (error) {
      this.sendMessage({
        type: 'error',
        error: (error as Error).message
      });
    }
  }

  /**
   * 处理获取系统统计
   */
  private async handleGetSystemStats() {
    try {
      const stats = {
        tokenUsage: 1250,
        maxTokens: 64000,
        responseTime: 850,
        ragStats: this.chatService.getRAGStats()
      };
      
      this.sendMessage({
        type: 'systemStats',
        stats
      });
    } catch (error) {
      this.sendMessage({
        type: 'error',
        error: (error as Error).message
      });
    }
  }

  /**
   * 处理RAG查询
   */
  private async handleRAGQuery(query: string, options: any) {
    try {
      const result = await this.chatService.performRAGQuery(query, options);
      
      this.sendMessage({
        type: 'ragQueryResult',
        query,
        result
      });
    } catch (error) {
      this.sendMessage({
        type: 'ragQueryError',
        query,
        error: (error as Error).message
      });
    }
  }

  /**
   * 处理重建索引
   */
  private async handleRebuildIndex() {
    try {
      await this.chatService.rebuildRAGIndex();
      
      this.sendMessage({
        type: 'indexRebuilt'
      });
    } catch (error) {
      this.sendMessage({
        type: 'error',
        error: (error as Error).message
      });
    }
  }

  /**
   * 处理同步知识库
   */
  private async handleSyncKnowledge() {
    try {
      await this.chatService.syncKnowledgeBase();
      
      this.sendMessage({
        type: 'knowledgeSynced'
      });
    } catch (error) {
      this.sendMessage({
        type: 'error',
        error: (error as Error).message
      });
    }
  }

  /**
   * 处理WebView就绪
   */
  private async handleWebViewReady() {
    // 发送初始数据
    const stats = {
      tokenUsage: 0,
      maxTokens: 64000,
      responseTime: 0,
      ragStats: this.chatService.getRAGStats()
    };
    
    this.sendMessage({
      type: 'initialize',
      stats,
      conversationHistory: []
    });
  }

  /**
   * 发送消息到WebView
   */
  private sendMessage(message: any) {
    if (this._view) {
      this._view.webview.postMessage(message);
    }
  }

  /**
   * 生成WebView HTML
   */
  private _getHtmlForWebview(webview: vscode.Webview) {
    // 获取资源URI
    const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'out', 'webview.js'));
    const styleUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'out', 'webview.css'));
    
    // 生成nonce用于安全
    const nonce = getNonce();

    return `<!DOCTYPE html>
      <html lang="zh-CN">
      <head>
        <meta charset="UTF-8">
        <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}';">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link href="${styleUri}" rel="stylesheet">
        <title>AI Agent</title>
      </head>
      <body>
        <div id="root"></div>
        <script nonce="${nonce}" src="${scriptUri}"></script>
      </body>
      </html>`;
  }

  /**
   * 清理资源
   */
  dispose() {
    this.disposables.forEach(d => d.dispose());
  }
}

function getNonce() {
  let text = '';
  const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  for (let i = 0; i < 32; i++) {
    text += possible.charAt(Math.floor(Math.random() * possible.length));
  }
  return text;
}
