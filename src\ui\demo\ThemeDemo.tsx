/**
 * 主题系统演示页面
 * 
 * 展示主题切换、VS Code同步和Monaco Editor适配功能
 */

import React, { useState, useEffect } from 'react';
import { useTheme } from '../theme/ThemeProvider';
import { ThemeSelector, QuickThemeToggle, ThemePreview } from '../components/ThemeSelector';
import { Button } from '../components/Button';
import { CodeBlock } from '../components/CodeBlock';
import { Transition } from '../components/Transition';
import { Easing } from '../utils/AnimationManager';

export const ThemeDemo: React.FC = () => {
  const { theme, themeConfig, syncWithVSCode, isTransitioning } = useTheme();
  const [showCodeExample, setShowCodeExample] = useState(true);
  const [selectedComponent, setSelectedComponent] = useState<string>('all');

  const codeExample = `// AI编程助手主题系统示例
import { ThemeProvider, useTheme } from './theme/ThemeProvider';
import { vscodeThemeSync } from './theme/VSCodeThemeSync';

function App() {
  const { theme, setTheme, syncWithVSCode } = useTheme();
  
  // 自动同步VS Code主题
  useEffect(() => {
    if (syncWithVSCode) {
      const unsubscribe = vscodeThemeSync.onThemeChange((newTheme) => {
        setTheme(newTheme);
      });
      
      return unsubscribe;
    }
  }, [syncWithVSCode]);
  
  return (
    <div className={\`theme-\${theme}\`}>
      <h1>AI编程助手</h1>
      <ThemeSelector showSyncOption={true} />
    </div>
  );
}`;

  const containerStyles: React.CSSProperties = {
    padding: themeConfig.spacing.lg,
    maxWidth: '1200px',
    margin: '0 auto',
    fontFamily: themeConfig.typography.fontFamily,
    color: themeConfig.colors.text,
    backgroundColor: themeConfig.colors.background,
    minHeight: '100vh',
    transition: isTransitioning ? 'all 0.3s ease' : 'none',
  };

  const sectionStyles: React.CSSProperties = {
    marginBottom: themeConfig.spacing.xl,
    padding: themeConfig.spacing.lg,
    backgroundColor: themeConfig.colors.surface,
    borderRadius: themeConfig.borderRadius.lg,
    border: `1px solid ${themeConfig.colors.border}`,
    boxShadow: themeConfig.shadows.md,
  };

  const titleStyles: React.CSSProperties = {
    fontSize: themeConfig.typography.fontSize.xl,
    fontWeight: themeConfig.typography.fontWeight.bold,
    marginBottom: themeConfig.spacing.md,
    color: themeConfig.colors.text,
  };

  const subtitleStyles: React.CSSProperties = {
    fontSize: themeConfig.typography.fontSize.lg,
    fontWeight: themeConfig.typography.fontWeight.medium,
    marginBottom: themeConfig.spacing.sm,
    color: themeConfig.colors.text,
  };

  const infoBoxStyles: React.CSSProperties = {
    padding: themeConfig.spacing.md,
    backgroundColor: themeConfig.colors.background,
    borderRadius: themeConfig.borderRadius.md,
    border: `1px solid ${themeConfig.colors.border}`,
    marginBottom: themeConfig.spacing.md,
  };

  const statusIndicatorStyles = (isActive: boolean): React.CSSProperties => ({
    display: 'inline-flex',
    alignItems: 'center',
    gap: themeConfig.spacing.xs,
    padding: `${themeConfig.spacing.xs} ${themeConfig.spacing.sm}`,
    backgroundColor: isActive ? themeConfig.colors.success : themeConfig.colors.warning,
    color: '#ffffff',
    borderRadius: themeConfig.borderRadius.sm,
    fontSize: themeConfig.typography.fontSize.sm,
    fontWeight: themeConfig.typography.fontWeight.medium,
  });

  const componentShowcaseStyles: React.CSSProperties = {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
    gap: themeConfig.spacing.md,
    marginTop: themeConfig.spacing.md,
  };

  const componentCardStyles: React.CSSProperties = {
    padding: themeConfig.spacing.md,
    backgroundColor: themeConfig.colors.background,
    borderRadius: themeConfig.borderRadius.md,
    border: `1px solid ${themeConfig.colors.border}`,
  };

  const renderComponentShowcase = () => {
    const components = [
      {
        name: '按钮组件',
        key: 'buttons',
        content: (
          <div style={{ display: 'flex', gap: themeConfig.spacing.sm, flexWrap: 'wrap' }}>
            <Button variant="primary" size="sm">主要按钮</Button>
            <Button variant="secondary" size="sm">次要按钮</Button>
            <Button variant="outline" size="sm">轮廓按钮</Button>
            <Button variant="ghost" size="sm">幽灵按钮</Button>
          </div>
        ),
      },
      {
        name: '状态指示器',
        key: 'indicators',
        content: (
          <div style={{ display: 'flex', gap: themeConfig.spacing.sm, flexWrap: 'wrap' }}>
            <div style={statusIndicatorStyles(true)}>
              <span style={{ width: '8px', height: '8px', backgroundColor: '#ffffff', borderRadius: '50%' }} />
              成功状态
            </div>
            <div style={statusIndicatorStyles(false)}>
              <span style={{ width: '8px', height: '8px', backgroundColor: '#ffffff', borderRadius: '50%' }} />
              警告状态
            </div>
          </div>
        ),
      },
      {
        name: '主题选择器',
        key: 'theme-selector',
        content: (
          <ThemeSelector 
            compact={false}
            position="vertical"
            showSyncOption={true}
          />
        ),
      },
      {
        name: '快速切换',
        key: 'quick-toggle',
        content: (
          <div style={{ display: 'flex', gap: themeConfig.spacing.sm, alignItems: 'center' }}>
            <QuickThemeToggle size="sm" showLabel={true} />
            <QuickThemeToggle size="md" showIcon={true} />
            <QuickThemeToggle size="lg" showIcon={true} showLabel={true} />
          </div>
        ),
      },
    ];

    return components
      .filter(comp => selectedComponent === 'all' || comp.key === selectedComponent)
      .map((component) => (
        <Transition
          key={component.key}
          show={true}
          config={{
            type: 'slide-up',
            duration: 300,
            delay: components.indexOf(component) * 100,
            easing: Easing.easeOutCubic,
          }}
        >
          <div style={componentCardStyles}>
            <h4 style={subtitleStyles}>{component.name}</h4>
            {component.content}
          </div>
        </Transition>
      ));
  };

  return (
    <div style={containerStyles}>
      <h1 style={titleStyles}>AI编程助手 - 主题系统演示</h1>
      
      {/* 当前状态 */}
      <section style={sectionStyles}>
        <h2 style={subtitleStyles}>当前状态</h2>
        
        <div style={infoBoxStyles}>
          <div style={{ display: 'flex', gap: themeConfig.spacing.lg, flexWrap: 'wrap', alignItems: 'center' }}>
            <div>
              <strong>当前主题:</strong> {theme}
            </div>
            <div style={statusIndicatorStyles(syncWithVSCode)}>
              VS Code同步: {syncWithVSCode ? '已启用' : '已禁用'}
            </div>
            <div style={statusIndicatorStyles(!isTransitioning)}>
              动画状态: {isTransitioning ? '过渡中' : '稳定'}
            </div>
          </div>
        </div>
        
        <div style={{ display: 'flex', gap: themeConfig.spacing.sm, marginTop: themeConfig.spacing.md }}>
          <QuickThemeToggle showLabel={true} />
        </div>
      </section>

      {/* 主题选择器演示 */}
      <section style={sectionStyles}>
        <h2 style={subtitleStyles}>主题选择器</h2>
        <p style={{ color: themeConfig.colors.textSecondary, marginBottom: themeConfig.spacing.md }}>
          完整的主题选择界面，支持VS Code主题同步
        </p>
        
        <div style={{ display: 'flex', gap: themeConfig.spacing.lg, flexWrap: 'wrap' }}>
          <div>
            <h4 style={{ ...subtitleStyles, fontSize: themeConfig.typography.fontSize.md }}>水平布局</h4>
            <ThemeSelector position="horizontal" showSyncOption={true} />
          </div>
          
          <div>
            <h4 style={{ ...subtitleStyles, fontSize: themeConfig.typography.fontSize.md }}>垂直布局</h4>
            <ThemeSelector position="vertical" showSyncOption={true} />
          </div>
          
          <div>
            <h4 style={{ ...subtitleStyles, fontSize: themeConfig.typography.fontSize.md }}>紧凑模式</h4>
            <ThemeSelector compact={true} showSyncOption={true} />
          </div>
        </div>
      </section>

      {/* 组件展示 */}
      <section style={sectionStyles}>
        <h2 style={subtitleStyles}>组件主题适配</h2>
        <p style={{ color: themeConfig.colors.textSecondary, marginBottom: themeConfig.spacing.md }}>
          展示各种UI组件在不同主题下的表现
        </p>
        
        <div style={{ marginBottom: themeConfig.spacing.md }}>
          <select
            value={selectedComponent}
            onChange={(e) => setSelectedComponent(e.target.value)}
            style={{
              padding: themeConfig.spacing.sm,
              borderRadius: themeConfig.borderRadius.sm,
              border: `1px solid ${themeConfig.colors.border}`,
              backgroundColor: themeConfig.colors.surface,
              color: themeConfig.colors.text,
              fontSize: themeConfig.typography.fontSize.sm,
            }}
          >
            <option value="all">显示所有组件</option>
            <option value="buttons">按钮组件</option>
            <option value="indicators">状态指示器</option>
            <option value="theme-selector">主题选择器</option>
            <option value="quick-toggle">快速切换</option>
          </select>
        </div>
        
        <div style={componentShowcaseStyles}>
          {renderComponentShowcase()}
        </div>
      </section>

      {/* 代码示例 */}
      <section style={sectionStyles}>
        <h2 style={subtitleStyles}>代码示例</h2>
        <p style={{ color: themeConfig.colors.textSecondary, marginBottom: themeConfig.spacing.md }}>
          主题系统的使用示例，支持Monaco Editor主题同步
        </p>
        
        <div style={{ marginBottom: themeConfig.spacing.md }}>
          <Button
            onClick={() => setShowCodeExample(!showCodeExample)}
            variant="outline"
            size="sm"
          >
            {showCodeExample ? '隐藏' : '显示'}代码示例
          </Button>
        </div>
        
        <Transition
          show={showCodeExample}
          config={{
            type: 'slide-down',
            duration: 300,
            easing: Easing.easeOutCubic,
          }}
        >
          <CodeBlock
            code={codeExample}
            language="typescript"
            onCopy={(code) => {
              navigator.clipboard.writeText(code);
              console.log('代码已复制到剪贴板');
            }}
          />
        </Transition>
      </section>

      {/* 主题预览 */}
      <section style={sectionStyles}>
        <h2 style={subtitleStyles}>主题预览</h2>
        <p style={{ color: themeConfig.colors.textSecondary, marginBottom: themeConfig.spacing.md }}>
          快速预览不同主题的视觉效果
        </p>
        
        <div style={{ display: 'flex', gap: themeConfig.spacing.md, flexWrap: 'wrap' }}>
          <ThemePreview
            themeName="浅色主题"
            themeConfig={{ colors: { background: '#ffffff', surface: '#f9fafb', primary: '#3b82f6', border: '#e5e7eb' } }}
            isActive={theme === 'light'}
          />
          <ThemePreview
            themeName="深色主题"
            themeConfig={{ colors: { background: '#0f172a', surface: '#1e293b', primary: '#60a5fa', border: '#334155' } }}
            isActive={theme === 'dark'}
          />
          <ThemePreview
            themeName="高对比度"
            themeConfig={{ colors: { background: '#000000', surface: '#1a1a1a', primary: '#ffffff', border: '#666666' } }}
            isActive={theme === 'high-contrast'}
          />
        </div>
      </section>
    </div>
  );
};
