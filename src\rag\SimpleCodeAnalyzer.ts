/**
 * Simple Code Analyzer - 简化代码分析器
 * 
 * 提供基础的代码分析功能，避免复杂的TypeScript编译器API依赖
 */

import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { EventBus } from '@/core/EventBus';

export interface SimpleSymbolInfo {
  name: string;
  kind: 'function' | 'class' | 'interface' | 'variable' | 'method' | 'property';
  line: number;
  column: number;
  endLine: number;
  endColumn: number;
  documentation?: string;
}

export interface SimpleImportInfo {
  module: string;
  imports: string[];
  line: number;
  isDefault: boolean;
  isNamespace: boolean;
}

export interface SimpleExportInfo {
  name: string;
  line: number;
  isDefault: boolean;
}

export interface SimpleCodeMetrics {
  lines: number;
  functions: number;
  classes: number;
  interfaces: number;
  complexity: number;
  imports: number;
  exports: number;
}

export interface SimpleCodeInfo {
  filePath: string;
  language: string;
  symbols: SimpleSymbolInfo[];
  imports: SimpleImportInfo[];
  exports: SimpleExportInfo[];
  metrics: SimpleCodeMetrics;
  content: string;
  lastAnalyzed: Date;
}

export class SimpleCodeAnalyzer {
  private eventBus: EventBus;
  private cache: Map<string, SimpleCodeInfo> = new Map();

  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
  }

  /**
   * 分析文件
   */
  async analyzeFile(filePath: string): Promise<SimpleCodeInfo> {
    try {
      // 检查缓存
      if (this.cache.has(filePath)) {
        const cached = this.cache.get(filePath)!;
        if (await this.isFileUpToDate(filePath, cached.lastAnalyzed)) {
          return cached;
        }
      }

      const content = await this.readFile(filePath);
      const language = this.detectLanguage(filePath);
      
      const symbols = this.extractSymbols(content, language);
      const imports = this.extractImports(content, language);
      const exports = this.extractExports(content, language);
      const metrics = this.calculateMetrics(content, symbols, imports, exports);

      const codeInfo: SimpleCodeInfo = {
        filePath,
        language,
        symbols,
        imports,
        exports,
        metrics,
        content,
        lastAnalyzed: new Date()
      };

      this.cache.set(filePath, codeInfo);

      this.eventBus.emit({
        type: 'simple_analyzer.file_analyzed',
        source: 'SimpleCodeAnalyzer',
        filePath,
        symbolCount: symbols.length,
        complexity: metrics.complexity
      });

      return codeInfo;
    } catch (error) {
      this.eventBus.emit({
        type: 'simple_analyzer.analysis_failed',
        source: 'SimpleCodeAnalyzer',
        filePath,
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * 提取符号信息
   */
  private extractSymbols(content: string, language: string): SimpleSymbolInfo[] {
    const symbols: SimpleSymbolInfo[] = [];
    const lines = content.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const lineNumber = i + 1;

      // 提取函数
      const functionMatch = this.matchFunction(line, language);
      if (functionMatch) {
        symbols.push({
          name: functionMatch.name,
          kind: 'function',
          line: lineNumber,
          column: functionMatch.column,
          endLine: lineNumber,
          endColumn: functionMatch.column + functionMatch.name.length,
          documentation: this.extractDocumentation(lines, i)
        });
      }

      // 提取类
      const classMatch = this.matchClass(line, language);
      if (classMatch) {
        symbols.push({
          name: classMatch.name,
          kind: 'class',
          line: lineNumber,
          column: classMatch.column,
          endLine: lineNumber,
          endColumn: classMatch.column + classMatch.name.length
        });
      }

      // 提取接口
      const interfaceMatch = this.matchInterface(line, language);
      if (interfaceMatch) {
        symbols.push({
          name: interfaceMatch.name,
          kind: 'interface',
          line: lineNumber,
          column: interfaceMatch.column,
          endLine: lineNumber,
          endColumn: interfaceMatch.column + interfaceMatch.name.length
        });
      }

      // 提取变量
      const variableMatch = this.matchVariable(line, language);
      if (variableMatch) {
        symbols.push({
          name: variableMatch.name,
          kind: 'variable',
          line: lineNumber,
          column: variableMatch.column,
          endLine: lineNumber,
          endColumn: variableMatch.column + variableMatch.name.length
        });
      }
    }

    return symbols;
  }

  /**
   * 提取导入信息
   */
  private extractImports(content: string, language: string): SimpleImportInfo[] {
    const imports: SimpleImportInfo[] = [];
    const lines = content.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      const lineNumber = i + 1;

      if (language === 'typescript' || language === 'javascript') {
        // import { a, b } from 'module'
        const namedImportMatch = line.match(/^import\s*\{\s*([^}]+)\s*\}\s*from\s*['"`]([^'"`]+)['"`]/);
        if (namedImportMatch) {
          const importNames = namedImportMatch[1].split(',').map(name => name.trim());
          imports.push({
            module: namedImportMatch[2],
            imports: importNames,
            line: lineNumber,
            isDefault: false,
            isNamespace: false
          });
          continue;
        }

        // import defaultExport from 'module'
        const defaultImportMatch = line.match(/^import\s+(\w+)\s+from\s*['"`]([^'"`]+)['"`]/);
        if (defaultImportMatch) {
          imports.push({
            module: defaultImportMatch[2],
            imports: [defaultImportMatch[1]],
            line: lineNumber,
            isDefault: true,
            isNamespace: false
          });
          continue;
        }

        // import * as name from 'module'
        const namespaceImportMatch = line.match(/^import\s*\*\s*as\s+(\w+)\s+from\s*['"`]([^'"`]+)['"`]/);
        if (namespaceImportMatch) {
          imports.push({
            module: namespaceImportMatch[2],
            imports: [namespaceImportMatch[1]],
            line: lineNumber,
            isDefault: false,
            isNamespace: true
          });
        }
      }
    }

    return imports;
  }

  /**
   * 提取导出信息
   */
  private extractExports(content: string, language: string): SimpleExportInfo[] {
    const exports: SimpleExportInfo[] = [];
    const lines = content.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      const lineNumber = i + 1;

      if (language === 'typescript' || language === 'javascript') {
        // export function/class/interface
        const exportDeclarationMatch = line.match(/^export\s+(function|class|interface)\s+(\w+)/);
        if (exportDeclarationMatch) {
          exports.push({
            name: exportDeclarationMatch[2],
            line: lineNumber,
            isDefault: false
          });
          continue;
        }

        // export default
        const defaultExportMatch = line.match(/^export\s+default\s+(\w+)/);
        if (defaultExportMatch) {
          exports.push({
            name: defaultExportMatch[1],
            line: lineNumber,
            isDefault: true
          });
          continue;
        }

        // export { a, b }
        const namedExportMatch = line.match(/^export\s*\{\s*([^}]+)\s*\}/);
        if (namedExportMatch) {
          const exportNames = namedExportMatch[1].split(',').map(name => name.trim());
          exportNames.forEach(name => {
            exports.push({
              name,
              line: lineNumber,
              isDefault: false
            });
          });
        }
      }
    }

    return exports;
  }

  /**
   * 计算代码指标
   */
  private calculateMetrics(
    content: string,
    symbols: SimpleSymbolInfo[],
    imports: SimpleImportInfo[],
    exports: SimpleExportInfo[]
  ): SimpleCodeMetrics {
    const lines = content.split('\n');
    const functions = symbols.filter(s => s.kind === 'function').length;
    const classes = symbols.filter(s => s.kind === 'class').length;
    const interfaces = symbols.filter(s => s.kind === 'interface').length;

    // 简单的圈复杂度计算
    let complexity = 1; // 基础复杂度
    for (const line of lines) {
      // 计算控制流语句
      if (/\b(if|else|while|for|switch|case|catch|&&|\|\|)\b/.test(line)) {
        complexity++;
      }
    }

    return {
      lines: lines.length,
      functions,
      classes,
      interfaces,
      complexity,
      imports: imports.length,
      exports: exports.length
    };
  }

  /**
   * 匹配函数声明
   */
  private matchFunction(line: string, language: string): { name: string; column: number } | null {
    if (language === 'typescript' || language === 'javascript') {
      // function name() 或 const name = function() 或 const name = () =>
      const patterns = [
        /function\s+(\w+)\s*\(/,
        /const\s+(\w+)\s*=\s*function/,
        /const\s+(\w+)\s*=\s*\([^)]*\)\s*=>/,
        /(\w+)\s*:\s*\([^)]*\)\s*=>/
      ];

      for (const pattern of patterns) {
        const match = line.match(pattern);
        if (match) {
          return {
            name: match[1],
            column: line.indexOf(match[1])
          };
        }
      }
    }
    return null;
  }

  /**
   * 匹配类声明
   */
  private matchClass(line: string, language: string): { name: string; column: number } | null {
    if (language === 'typescript' || language === 'javascript') {
      const match = line.match(/class\s+(\w+)/);
      if (match) {
        return {
          name: match[1],
          column: line.indexOf(match[1])
        };
      }
    }
    return null;
  }

  /**
   * 匹配接口声明
   */
  private matchInterface(line: string, language: string): { name: string; column: number } | null {
    if (language === 'typescript') {
      const match = line.match(/interface\s+(\w+)/);
      if (match) {
        return {
          name: match[1],
          column: line.indexOf(match[1])
        };
      }
    }
    return null;
  }

  /**
   * 匹配变量声明
   */
  private matchVariable(line: string, language: string): { name: string; column: number } | null {
    if (language === 'typescript' || language === 'javascript') {
      const patterns = [
        /(?:const|let|var)\s+(\w+)/,
        /(\w+)\s*:/  // TypeScript 属性声明
      ];

      for (const pattern of patterns) {
        const match = line.match(pattern);
        if (match) {
          return {
            name: match[1],
            column: line.indexOf(match[1])
          };
        }
      }
    }
    return null;
  }

  /**
   * 提取文档注释
   */
  private extractDocumentation(lines: string[], currentIndex: number): string | undefined {
    // 查找前面的注释
    for (let i = currentIndex - 1; i >= 0; i--) {
      const line = lines[i].trim();
      if (line.startsWith('/**') || line.startsWith('/*')) {
        // 找到注释开始，收集注释内容
        const docLines: string[] = [];
        for (let j = i; j < currentIndex; j++) {
          const docLine = lines[j].trim();
          if (docLine.startsWith('*')) {
            docLines.push(docLine.replace(/^\*\s?/, ''));
          }
        }
        return docLines.join('\n').trim() || undefined;
      }
      if (line && !line.startsWith('//')) {
        break; // 遇到非注释行，停止查找
      }
    }
    return undefined;
  }

  /**
   * 检测编程语言
   */
  private detectLanguage(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase();
    switch (ext) {
      case '.ts':
      case '.tsx':
        return 'typescript';
      case '.js':
      case '.jsx':
        return 'javascript';
      case '.py':
        return 'python';
      case '.java':
        return 'java';
      case '.cpp':
      case '.cc':
      case '.cxx':
        return 'cpp';
      case '.c':
        return 'c';
      case '.cs':
        return 'csharp';
      case '.go':
        return 'go';
      case '.rs':
        return 'rust';
      default:
        return 'unknown';
    }
  }

  /**
   * 读取文件内容
   */
  private async readFile(filePath: string): Promise<string> {
    try {
      return fs.readFileSync(filePath, 'utf8');
    } catch (error) {
      throw new Error(`Failed to read file ${filePath}: ${(error as Error).message}`);
    }
  }

  /**
   * 检查文件是否最新
   */
  private async isFileUpToDate(filePath: string, lastAnalyzed: Date): Promise<boolean> {
    try {
      const stats = fs.statSync(filePath);
      return stats.mtime <= lastAnalyzed;
    } catch {
      return false;
    }
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    this.cache.clear();
    this.eventBus.emit({
      type: 'simple_analyzer.cache_cleared',
      source: 'SimpleCodeAnalyzer'
    });
  }

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return {
      cachedFiles: this.cache.size,
      totalSymbols: Array.from(this.cache.values()).reduce((sum, info) => sum + info.symbols.length, 0),
      totalLines: Array.from(this.cache.values()).reduce((sum, info) => sum + info.metrics.lines, 0)
    };
  }
}
