/**
 * Webpack Configuration for WebView
 * 
 * 专门用于构建React WebView组件的Webpack配置
 */

const path = require('path');

module.exports = {
  mode: 'production',
  entry: './src/ui/webview.tsx',
  output: {
    path: path.resolve(__dirname, 'out'),
    filename: 'webview.js',
    clean: false // 不清理其他文件
  },
  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.jsx'],
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: [
          {
            loader: 'ts-loader',
            options: {
              configFile: 'tsconfig.webview.json'
            }
          }
        ],
        exclude: /node_modules/
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader']
      },
      {
        test: /\.(png|jpg|jpeg|gif|svg)$/,
        type: 'asset/resource'
      }
    ]
  },
  externals: {
    vscode: 'commonjs vscode'
  },
  optimization: {
    minimize: true
  },
  devtool: 'source-map'
};
