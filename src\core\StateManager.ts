/**
 * State Manager - 全局状态管理器
 * 
 * 负责管理应用的全局状态，提供状态订阅、更新和持久化功能
 */

import { AppState, SystemState, ConfigState, ConversationState, ProjectState, UIState } from '@/types';

type StateListener<T = AppState> = (state: T) => void;
type StateSelector<T> = (state: AppState) => T;

export class StateManager {
  private state: AppState;
  private listeners: Set<StateListener> = new Set();
  private selectorListeners: Map<StateSelector<any>, Set<StateListener<any>>> = new Map();

  constructor(initialState?: Partial<AppState>) {
    this.state = this.createInitialState(initialState);
  }

  /**
   * 创建初始状态
   */
  private createInitialState(partial?: Partial<AppState>): AppState {
    const defaultState: AppState = {
      system: {
        isInitialized: false,
        isLoading: false,
        error: null,
        version: '0.0.1',
        performance: {
          memoryUsage: 0,
          cpuUsage: 0,
          responseTime: 0,
          indexSize: 0,
        },
      },
      config: {
        apiKey: '',
        baseUrl: 'https://api.openai.com/v1',
        model: 'gpt-4o',
        temperature: 0.2,
        maxTokens: 4096,
        provider: 'openai',
      },
      conversation: {
        messages: [],
        isProcessing: false,
        currentContext: {
          chunks: [],
          totalTokens: 0,
          sources: [],
        },
        history: [],
      },
      project: {
        workspace: {
          name: '',
          path: '',
          type: 'typescript',
          language: [],
          dependencies: [],
        },
        indexStatus: {
          isIndexing: false,
          progress: 0,
          totalFiles: 0,
          indexedFiles: 0,
          lastIndexed: 0,
        },
        files: [],
        gitStatus: {
          branch: '',
          hasChanges: false,
          staged: [],
          unstaged: [],
          untracked: [],
        },
      },
      ui: {
        theme: 'dark',
        layout: {
          sidebarWidth: 300,
          chatHeight: 400,
          fontSize: 14,
        },
        activePanel: 'chat',
        sidebarCollapsed: false,
        preferences: {
          language: 'zh-CN',
          autoSave: true,
          showLineNumbers: true,
          wordWrap: true,
          codeTheme: 'github-dark',
        },
      },
    };

    return { ...defaultState, ...partial };
  }

  /**
   * 获取当前状态
   */
  getState(): AppState {
    return { ...this.state };
  }

  /**
   * 使用选择器获取状态的一部分
   */
  getStateBySelector<T>(selector: StateSelector<T>): T {
    return selector(this.state);
  }

  /**
   * 更新状态
   */
  setState(updater: Partial<AppState> | ((state: AppState) => Partial<AppState>)): void {
    const updates = typeof updater === 'function' ? updater(this.state) : updater;
    
    const newState = this.mergeState(this.state, updates);
    
    if (newState !== this.state) {
      this.state = newState;
      this.notifyListeners();
    }
  }

  /**
   * 深度合并状态
   */
  private mergeState(current: AppState, updates: Partial<AppState>): AppState {
    const merged = { ...current };

    for (const key in updates) {
      if (updates.hasOwnProperty(key)) {
        const value = updates[key as keyof AppState];
        if (value && typeof value === 'object' && !Array.isArray(value)) {
          // 深度合并对象
          merged[key as keyof AppState] = {
            ...(current[key as keyof AppState] as any),
            ...value,
          } as any;
        } else if (value !== undefined) {
          merged[key as keyof AppState] = value as any;
        }
      }
    }

    return merged;
  }

  /**
   * 订阅状态变化
   */
  subscribe(listener: StateListener): () => void {
    this.listeners.add(listener);
    
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * 使用选择器订阅状态变化
   */
  subscribeWithSelector<T>(
    selector: StateSelector<T>,
    listener: StateListener<T>
  ): () => void {
    if (!this.selectorListeners.has(selector)) {
      this.selectorListeners.set(selector, new Set());
    }
    
    const listeners = this.selectorListeners.get(selector)!;
    listeners.add(listener);
    
    return () => {
      listeners.delete(listener);
      if (listeners.size === 0) {
        this.selectorListeners.delete(selector);
      }
    };
  }

  /**
   * 通知所有监听器
   */
  private notifyListeners(): void {
    // 通知全局监听器
    this.listeners.forEach(listener => {
      try {
        listener(this.state);
      } catch (error) {
        console.error('Error in state listener:', error);
      }
    });

    // 通知选择器监听器
    this.selectorListeners.forEach((listeners, selector) => {
      try {
        const selectedState = selector(this.state);
        listeners.forEach(listener => {
          try {
            listener(selectedState);
          } catch (error) {
            console.error('Error in selector listener:', error);
          }
        });
      } catch (error) {
        console.error('Error in selector:', error);
      }
    });
  }

  /**
   * 更新系统状态
   */
  updateSystemState(updates: Partial<SystemState>): void {
    this.setState(state => ({
      system: { ...state.system, ...updates }
    }));
  }

  /**
   * 更新配置状态
   */
  updateConfigState(updates: Partial<ConfigState>): void {
    this.setState(state => ({
      config: { ...state.config, ...updates }
    }));
  }

  /**
   * 更新对话状态
   */
  updateConversationState(updates: Partial<ConversationState>): void {
    this.setState(state => ({
      conversation: { ...state.conversation, ...updates }
    }));
  }

  /**
   * 更新项目状态
   */
  updateProjectState(updates: Partial<ProjectState>): void {
    this.setState(state => ({
      project: { ...state.project, ...updates }
    }));
  }

  /**
   * 更新UI状态
   */
  updateUIState(updates: Partial<UIState>): void {
    this.setState(state => ({
      ui: { ...state.ui, ...updates }
    }));
  }

  /**
   * 重置状态
   */
  reset(newState?: Partial<AppState>): void {
    this.state = this.createInitialState(newState);
    this.notifyListeners();
  }

  /**
   * 获取状态快照（用于调试）
   */
  getSnapshot(): string {
    return JSON.stringify(this.state, null, 2);
  }

  /**
   * 从快照恢复状态
   */
  restoreFromSnapshot(snapshot: string): void {
    try {
      const state = JSON.parse(snapshot);
      this.state = this.createInitialState(state);
      this.notifyListeners();
    } catch (error) {
      console.error('Failed to restore state from snapshot:', error);
    }
  }
}
