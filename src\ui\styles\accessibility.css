/**
 * 可访问性样式
 * 
 * 提供WCAG 2.1 AA级标准的可访问性样式
 */

/* 屏幕阅读器专用内容 */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* 可聚焦时显示屏幕阅读器内容 */
.sr-only:focus {
  position: static !important;
  width: auto !important;
  height: auto !important;
  padding: inherit !important;
  margin: inherit !important;
  overflow: visible !important;
  clip: auto !important;
  white-space: inherit !important;
}

/* 跳转链接 */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary, #007acc);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 10000;
  font-weight: bold;
  transition: top 0.3s ease;
}

.skip-link:focus {
  top: 6px;
}

/* 焦点指示器 */
.accessibility-focus-ring {
  position: absolute;
  pointer-events: none;
  z-index: 9999;
  border: 2px solid var(--color-primary, #007acc);
  border-radius: 4px;
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.3);
  transition: all 0.15s ease;
  opacity: 0;
}

/* 键盘用户焦点样式 */
.keyboard-user *:focus {
  outline: 2px solid var(--color-primary, #007acc);
  outline-offset: 2px;
}

/* 鼠标用户隐藏焦点样式 */
.mouse-user *:focus {
  outline: none;
}

/* 高对比度模式 */
[data-high-contrast="true"] {
  --color-background: #000000;
  --color-surface: #1a1a1a;
  --color-text: #ffffff;
  --color-textSecondary: #cccccc;
  --color-border: #666666;
  --color-primary: #ffffff;
  --color-secondary: #cccccc;
  --color-accent: #ffff00;
  --color-success: #00ff00;
  --color-warning: #ffff00;
  --color-error: #ff0000;
  --color-info: #00ffff;
}

[data-high-contrast="true"] * {
  border-color: var(--color-border) !important;
  background-color: var(--color-background) !important;
  color: var(--color-text) !important;
}

[data-high-contrast="true"] button,
[data-high-contrast="true"] input,
[data-high-contrast="true"] select,
[data-high-contrast="true"] textarea {
  border: 2px solid var(--color-text) !important;
  background-color: var(--color-background) !important;
  color: var(--color-text) !important;
}

[data-high-contrast="true"] button:hover,
[data-high-contrast="true"] button:focus {
  background-color: var(--color-text) !important;
  color: var(--color-background) !important;
}

/* 减少动画偏好 */
[data-reduced-motion="true"] *,
[data-reduced-motion="true"] *::before,
[data-reduced-motion="true"] *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* 可访问性增强的表单元素 */
.form-field {
  position: relative;
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--color-text);
}

.form-label.required::after {
  content: " *";
  color: var(--color-error, #ef4444);
  font-weight: bold;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid var(--color-border);
  border-radius: 4px;
  background-color: var(--color-background);
  color: var(--color-text);
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
}

.form-input:invalid {
  border-color: var(--color-error);
}

.form-input[aria-invalid="true"] {
  border-color: var(--color-error);
}

.form-error {
  display: block;
  margin-top: 0.25rem;
  color: var(--color-error);
  font-size: 0.875rem;
}

.form-help {
  display: block;
  margin-top: 0.25rem;
  color: var(--color-textSecondary);
  font-size: 0.875rem;
}

/* 可访问性增强的按钮 */
.btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 2px solid transparent;
  border-radius: 4px;
  background-color: var(--color-primary);
  color: white;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 44px; /* WCAG 2.1 AA 最小触摸目标 */
  min-width: 44px;
}

.btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn[aria-pressed="true"] {
  background-color: var(--color-accent);
  border-color: var(--color-accent);
}

/* 可访问性增强的导航 */
.nav {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  position: relative;
}

.nav-link {
  display: block;
  padding: 0.75rem 1rem;
  color: var(--color-text);
  text-decoration: none;
  transition: all 0.2s ease;
  min-height: 44px;
  display: flex;
  align-items: center;
}

.nav-link:hover,
.nav-link:focus {
  background-color: var(--color-surface);
  outline: 2px solid var(--color-primary);
  outline-offset: -2px;
}

.nav-link[aria-current="page"] {
  background-color: var(--color-primary);
  color: white;
  font-weight: bold;
}

/* 可访问性增强的模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  position: relative;
  background-color: var(--color-surface);
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  padding: 2rem;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--color-border);
}

.modal-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--color-text);
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--color-textSecondary);
  padding: 0.5rem;
  border-radius: 4px;
  min-width: 44px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover,
.modal-close:focus {
  background-color: var(--color-background);
  color: var(--color-text);
  outline: 2px solid var(--color-primary);
}

/* 可访问性增强的提示信息 */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip-content {
  position: absolute;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--color-text);
  color: var(--color-background);
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
  white-space: nowrap;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease;
}

.tooltip:hover .tooltip-content,
.tooltip:focus-within .tooltip-content {
  opacity: 1;
  visibility: visible;
}

.tooltip-content::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: var(--color-text);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .btn,
  .nav-link,
  .modal-close {
    min-height: 48px; /* 移动设备更大的触摸目标 */
    min-width: 48px;
  }
  
  .form-input {
    font-size: 16px; /* 防止iOS缩放 */
  }
}

/* 打印样式 */
@media print {
  .skip-link,
  .accessibility-focus-ring,
  .tooltip-content {
    display: none !important;
  }
  
  .sr-only {
    position: static !important;
    width: auto !important;
    height: auto !important;
    clip: auto !important;
    overflow: visible !important;
  }
}
