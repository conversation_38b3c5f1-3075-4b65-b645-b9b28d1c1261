{"compilerOptions": {"module": "commonjs", "target": "ES2020", "outDir": "out", "lib": ["ES2020", "DOM", "DOM.Iterable"], "sourceMap": true, "rootDir": "src", "strict": true, "noImplicitAny": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", ".vscode-test", "out", "dist"]}