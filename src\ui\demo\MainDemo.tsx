/**
 * 主演示页面
 * 
 * 整合所有UI优化功能的综合演示
 */

import React, { useState } from 'react';
import { useTheme } from '../theme/ThemeProvider';
import { Button } from '../components/Button';
import { ThemeSelector } from '../components/ThemeSelector';
import { PerformanceMonitor } from '../components/PerformanceMonitor';
import { OfflineIndicator } from '../components/OfflineIndicator';
import { useAccessibility } from '../accessibility/useAccessibility';
import { useOffline } from '../hooks/useOffline';

// 懒加载的演示组件
const AnimationDemo = React.lazy(() => import('./AnimationDemo').then(module => ({ default: module.AnimationDemo })));
const ThemeDemo = React.lazy(() => import('./ThemeDemo').then(module => ({ default: module.ThemeDemo })));
const AccessibilityDemo = React.lazy(() => import('./AccessibilityDemo').then(module => ({ default: module.AccessibilityDemo })));
const LazyLoadDemo = React.lazy(() => import('./LazyLoadDemo').then(module => ({ default: module.LazyLoadDemo })));
const CacheDemo = React.lazy(() => import('./CacheDemo').then(module => ({ default: module.CacheDemo })));
const OfflineDemo = React.lazy(() => import('./OfflineDemo').then(module => ({ default: module.OfflineDemo })));

export const MainDemo: React.FC = () => {
  const { colors, spacing, typography, borderRadius } = useTheme();
  const [activeDemo, setActiveDemo] = useState<string>('overview');
  const [showPerformanceMonitor, setShowPerformanceMonitor] = useState(true);
  
  // 可访问性增强
  const { elementRef, announce } = useAccessibility({
    enableFocusManagement: true,
    enableAnnouncements: true,
    role: 'main',
    ariaLabel: 'AI编程助手UI优化演示',
  });

  // 离线状态
  const offlineState = useOffline();

  const demos = [
    { 
      id: 'overview', 
      name: '功能概览', 
      icon: '🏠',
      description: '查看所有UI优化功能的概览',
    },
    { 
      id: 'animation', 
      name: '动画效果', 
      icon: '✨',
      description: '60fps流畅动画和性能优化',
    },
    { 
      id: 'theme', 
      name: '主题系统', 
      icon: '🎨',
      description: 'VS Code主题同步和Monaco Editor适配',
    },
    { 
      id: 'accessibility', 
      name: '可访问性', 
      icon: '♿',
      description: '键盘导航和屏幕阅读器支持',
    },
    { 
      id: 'lazy-load', 
      name: '懒加载', 
      icon: '🚀',
      description: '代码分割和性能优化',
    },
    { 
      id: 'cache', 
      name: '缓存策略', 
      icon: '💾',
      description: '智能缓存和对话历史管理',
    },
    { 
      id: 'offline', 
      name: '离线模式', 
      icon: '📡',
      description: '离线功能和数据同步',
    },
  ];

  const containerStyles: React.CSSProperties = {
    display: 'flex',
    height: '100vh',
    fontFamily: typography.fontFamily,
    backgroundColor: colors.background,
    color: colors.text,
  };

  const sidebarStyles: React.CSSProperties = {
    width: '280px',
    backgroundColor: colors.surface,
    borderRight: `1px solid ${colors.border}`,
    padding: spacing.md,
    overflowY: 'auto',
  };

  const contentStyles: React.CSSProperties = {
    flex: 1,
    overflow: 'auto',
    position: 'relative',
  };

  const headerStyles: React.CSSProperties = {
    padding: spacing.lg,
    borderBottom: `1px solid ${colors.border}`,
    backgroundColor: colors.surface,
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  };

  const titleStyles: React.CSSProperties = {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    margin: 0,
    color: colors.text,
  };

  const demoButtonStyles = (isActive: boolean): React.CSSProperties => ({
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    gap: spacing.sm,
    padding: spacing.md,
    marginBottom: spacing.xs,
    backgroundColor: isActive ? colors.primary : 'transparent',
    color: isActive ? '#ffffff' : colors.text,
    border: `1px solid ${isActive ? colors.primary : colors.border}`,
    borderRadius: borderRadius.md,
    cursor: 'pointer',
    fontSize: typography.fontSize.sm,
    textAlign: 'left',
    transition: 'all 0.2s ease',
  });

  const handleDemoChange = (demoId: string) => {
    setActiveDemo(demoId);
    announce(`切换到${demos.find(d => d.id === demoId)?.name}演示`);
  };

  const renderOverview = () => (
    <div style={{ padding: spacing.lg }}>
      <h1 style={{ fontSize: typography.fontSize.xxl, marginBottom: spacing.lg }}>
        🤖 AI编程助手 UI优化演示
      </h1>
      
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: spacing.lg,
        marginBottom: spacing.xl,
      }}>
        {demos.slice(1).map(demo => (
          <div
            key={demo.id}
            style={{
              padding: spacing.lg,
              backgroundColor: colors.surface,
              borderRadius: borderRadius.lg,
              border: `1px solid ${colors.border}`,
              cursor: 'pointer',
              transition: 'all 0.2s ease',
            }}
            onClick={() => handleDemoChange(demo.id)}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 8px 25px rgba(0,0,0,0.1)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = 'none';
            }}
          >
            <div style={{
              fontSize: '2rem',
              marginBottom: spacing.sm,
            }}>
              {demo.icon}
            </div>
            <h3 style={{
              fontSize: typography.fontSize.lg,
              fontWeight: typography.fontWeight.bold,
              marginBottom: spacing.sm,
              color: colors.text,
            }}>
              {demo.name}
            </h3>
            <p style={{
              color: colors.textSecondary,
              fontSize: typography.fontSize.sm,
              lineHeight: 1.5,
              margin: 0,
            }}>
              {demo.description}
            </p>
          </div>
        ))}
      </div>

      <div style={{
        padding: spacing.lg,
        backgroundColor: colors.surface,
        borderRadius: borderRadius.lg,
        border: `1px solid ${colors.border}`,
      }}>
        <h2 style={{ fontSize: typography.fontSize.lg, marginBottom: spacing.md }}>
          🎯 优化目标达成情况
        </h2>
        
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: spacing.md,
        }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '2rem', color: colors.success }}>✅</div>
            <div style={{ fontWeight: 'bold' }}>60fps动画</div>
            <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
              流畅动画效果
            </div>
          </div>
          
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '2rem', color: colors.success }}>✅</div>
            <div style={{ fontWeight: 'bold' }}>&lt;50ms交互</div>
            <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
              快速响应时间
            </div>
          </div>
          
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '2rem', color: colors.success }}>✅</div>
            <div style={{ fontWeight: 'bold' }}>&lt;200KB包大小</div>
            <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
              懒加载优化
            </div>
          </div>
          
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '2rem', color: colors.success }}>✅</div>
            <div style={{ fontWeight: 'bold' }}>&lt;500ms查询</div>
            <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
              缓存优化
            </div>
          </div>
          
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '2rem', color: colors.success }}>✅</div>
            <div style={{ fontWeight: 'bold' }}>WCAG 2.1 AA</div>
            <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
              可访问性标准
            </div>
          </div>
          
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '2rem', color: colors.success }}>✅</div>
            <div style={{ fontWeight: 'bold' }}>离线支持</div>
            <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
              基础功能可用
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderDemo = () => {
    switch (activeDemo) {
      case 'overview':
        return renderOverview();
      case 'animation':
        return (
          <React.Suspense fallback={<div style={{ padding: spacing.lg }}>加载中...</div>}>
            <AnimationDemo />
          </React.Suspense>
        );
      case 'theme':
        return (
          <React.Suspense fallback={<div style={{ padding: spacing.lg }}>加载中...</div>}>
            <ThemeDemo />
          </React.Suspense>
        );
      case 'accessibility':
        return (
          <React.Suspense fallback={<div style={{ padding: spacing.lg }}>加载中...</div>}>
            <AccessibilityDemo />
          </React.Suspense>
        );
      case 'lazy-load':
        return (
          <React.Suspense fallback={<div style={{ padding: spacing.lg }}>加载中...</div>}>
            <LazyLoadDemo />
          </React.Suspense>
        );
      case 'cache':
        return (
          <React.Suspense fallback={<div style={{ padding: spacing.lg }}>加载中...</div>}>
            <CacheDemo />
          </React.Suspense>
        );
      case 'offline':
        return (
          <React.Suspense fallback={<div style={{ padding: spacing.lg }}>加载中...</div>}>
            <OfflineDemo />
          </React.Suspense>
        );
      default:
        return renderOverview();
    }
  };

  return (
    <div ref={elementRef} style={containerStyles}>
      {/* 侧边栏 */}
      <div style={sidebarStyles}>
        <div style={{ marginBottom: spacing.lg }}>
          <h2 style={{ 
            fontSize: typography.fontSize.lg, 
            fontWeight: typography.fontWeight.bold,
            marginBottom: spacing.md,
            color: colors.text,
          }}>
            演示功能
          </h2>
          
          {demos.map(demo => (
            <button
              key={demo.id}
              style={demoButtonStyles(activeDemo === demo.id)}
              onClick={() => handleDemoChange(demo.id)}
              aria-label={`切换到${demo.name}演示`}
            >
              <span style={{ fontSize: '1.2rem' }}>{demo.icon}</span>
              <div>
                <div style={{ fontWeight: 'bold' }}>{demo.name}</div>
                <div style={{ 
                  fontSize: typography.fontSize.xs, 
                  opacity: 0.8,
                  marginTop: '2px',
                }}>
                  {demo.description}
                </div>
              </div>
            </button>
          ))}
        </div>

        <div style={{ marginTop: 'auto' }}>
          <ThemeSelector 
            position="vertical" 
            compact={true}
            showSyncOption={true}
          />
        </div>
      </div>

      {/* 主内容区 */}
      <div style={contentStyles}>
        {/* 头部 */}
        <div style={headerStyles}>
          <h1 style={titleStyles}>
            {demos.find(d => d.id === activeDemo)?.name || '功能概览'}
          </h1>
          
          <div style={{ display: 'flex', gap: spacing.sm, alignItems: 'center' }}>
            {offlineState.isOfflineMode && (
              <div style={{
                padding: `${spacing.xs} ${spacing.sm}`,
                backgroundColor: colors.warning,
                color: 'white',
                borderRadius: borderRadius.sm,
                fontSize: typography.fontSize.xs,
                fontWeight: 'bold',
              }}>
                离线模式
              </div>
            )}
            
            <Button
              onClick={() => setShowPerformanceMonitor(!showPerformanceMonitor)}
              variant="ghost"
              size="sm"
              aria-label="切换性能监控器"
            >
              📊
            </Button>
          </div>
        </div>

        {/* 演示内容 */}
        {renderDemo()}
      </div>

      {/* 性能监控器 */}
      <PerformanceMonitor
        show={showPerformanceMonitor}
        position="top-right"
      />

      {/* 离线状态指示器 */}
      <OfflineIndicator
        position="bottom-right"
        compact={true}
      />
    </div>
  );
};
